/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */

#fgd2wp_admin_page {
    float: left;
}
#fgd2wp_settings {
    float: left;
    max-width: 724px;
}
#fgd2wp_database_info {
    border: 1px solid #cccccc;
    background: #faebd7;
    margin: 10px;
    padding: 2px 10px;
}
#fgd2wp_extra_features {
    float: left;
    width: 300px;
    margin-top: 10px;
    margin-left: 10px;
    padding: 10px;
    border: #000 dashed 1px;
    background-color: #ffe;
}
#partial_import_toggle {
    text-decoration: underline;
    cursor: pointer;
    margin-bottom: 4px;
}
#fgd2wp_extra_features ul {
    list-style: disc inside;
}
.center {
    text-align: center;
}
#fgd2wp_paypal_donate {
    margin-top: 20px;
}
#fgd2wp-help-instructions ul, #fgd2wp-help-options ul {
    list-style-type: disc;
    padding-inline-start: 80px;
}
#fgd2wp-help-instructions h2, #fgd2wp-help-options h2 {
    margin-top: 30px;
}
#fgd2wp-help-instructions p, #fgd2wp-help-options p {
    margin-left: 30px;
}
.fgd2wp_premium_feature {
    color: #58b6fc;
}
#progressbar {
    position: relative;
    width: 100%;
    height: 40px;
    background-color: #ccc;
    border: 1px solid #000;
    border-radius: 5px;
}
.ui-progressbar .ui-progressbar-value {
    height: 100%;
    background: #00aa00;
    background-image: -webkit-linear-gradient(top, #00aa00, #008000);
    background-image: -moz-linear-gradient(top, #00aa00, #008000);
    background-image: -o-linear-gradient(top, #00aa00, #008000);
    background-image: -ms-linear-gradient(top, #00aa00, #008000);
    background-image: linear-gradient(to bottom, #00aa00, #008000);
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 0 rgba(255,255,255,0.5) inset;
    box-shadow: 0 1px 0 rgba(255,255,255,0.5) inset;
    border: 0;
}
#progresslabel {
    position: absolute;
    width: 100%;
    text-align: center;
    display: inline-block;
    margin: 8px 0 0 0;
    font-size: 18px;
}
#logger {
    width: 100%;
    height: 300px;
    background-color: #ddd;
    border: 1px solid #000;
    padding: 2px;
    overflow: scroll;
    resize: both;
}
#stop-import {
    background-color: #f00;
    color: #fff;
    display: none;
}
.error_msg {
    color: #f00;
}
.completed_msg {
    color: #008000;
    font-weight: bold;
}
.action_message {
	clear: both;
	padding-top: 10px;
    font-size: 18px;
    font-weight: bold;
}
.success {
    color: #008000;
 }
.failure {
    color: #f00;
}
#label_logger_autorefresh {
    float: right;
}
#partial_import_nodes {
    margin-left: 25px;
}
.submit_button_with_spinner {
	float: left;
	padding-top: 10px;
}
