# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-04-04T12:09:00+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: includes/class-acf-site-health.php:291
msgid "Update Source"
msgstr "અપડેટ સ્ત્રોત"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "ડિફૉલ્ટ રૂપે ફક્ત એડમિન વપરાશકર્તાઓ જ આ સેટિંગને સંપાદિત કરી શકે છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "ડિફૉલ્ટ રૂપે ફક્ત સુપર એડમિન વપરાશકર્તાઓ જ આ સેટિંગને સંપાદિત કરી શકે છે."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "બંધ કરો અને ફીલ્ડ ઉમેરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"તમારા વર્ગીકરણ પર મેટા બોક્સની સામગ્રીને હેન્ડલ કરવા માટે કૉલ કરવા માટે PHP ફંક્શન નામ. "
"સુરક્ષા માટે, આ કૉલબેકને $_POST અથવા $_GET જેવા કોઈપણ સુપરગ્લોબલ્સની ઍક્સેસ વિના વિશિષ્ટ "
"સંદર્ભમાં એક્ઝિક્યુટ કરવામાં આવશે."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"સંપાદન સ્ક્રીન માટે મેટા બોક્સ સેટ કરતી વખતે કૉલ કરવા માટે PHP ફંક્શન નામ. સુરક્ષા માટે, આ "
"કૉલબેકને $_POST અથવા $_GET જેવા કોઈપણ સુપરગ્લોબલ્સની ઍક્સેસ વિના વિશિષ્ટ સંદર્ભમાં "
"એક્ઝિક્યુટ કરવામાં આવશે."

#: includes/class-acf-site-health.php:292
msgid "wordpress.org"
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "સંપાદક UI માં મૂલ્યને ઍક્સેસ કરવાની મંજૂરી આપો"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "વધુ જાણો."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"સામગ્રી સંપાદકોને બ્લોક બાઈન્ડિંગ્સ અથવા ACF શોર્ટકોડનો ઉપયોગ કરીને એડિટર UI માં ફીલ્ડ "
"મૂલ્યને ઍક્સેસ કરવા અને પ્રદર્શિત કરવાની મંજૂરી આપો. %s"

#: includes/Blocks/Bindings.php:64
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"વિનંતી કરેલ ACF ફીલ્ડ પ્રકાર બ્લોક બાઈન્ડિંગ્સ અથવા ACF શોર્ટકોડમાં આઉટપુટને સપોર્ટ કરતું "
"નથી."

#: includes/api/api-template.php:1085 includes/Blocks/Bindings.php:72
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr "વિનંતી કરેલ ACF ફીલ્ડને બાઈન્ડીંગ અથવા ACF શોર્ટકોડમાં આઉટપુટ કરવાની મંજૂરી નથી."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"વિનંતી કરેલ ACF ફીલ્ડ પ્રકાર બાઈન્ડીંગ્સ અથવા ACF શોર્ટકોડમાં આઉટપુટને સપોર્ટ કરતું નથી."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[ACF શોર્ટકોડ બિન-સાર્વજનિક પોસ્ટમાંથી ફીલ્ડ પ્રદર્શિત કરી શકતું નથી]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[આ સાઇટ પર ACF શોર્ટકોડ અક્ષમ છે]"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr "ઉદ્યોગપતિ ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr "ફોરમ આયકન"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr "યુટ્યુબ ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr "હા ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr "ઝીંગ ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr "વર્ડપ્રેસ (વૈકલ્પિક) ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr "વોટ્સએપ ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr "બ્લોગ આયકન લખો"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr "વિજેટ્સ મેનુ આયકન"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr "સાઇટ આયકન જુઓ"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr "વધુ જાણો આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr "પૃષ્ઠ ચિહ્ન ઉમેરો"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr "વિડિઓ (alt3) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr "વિડિઓ (alt2) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr "વિડિઓ (Alt) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr "અપડેટ (Alt) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr "સાર્વત્રિક એક્સેસ (alt) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr "ટ્વિટર (alt) આઇકન"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr "ટ્વિટર ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr "ભરતી ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr "ટિકિટ ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr "ટેક્સ્ટ પેજ આયકન"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr "Table Row Delete ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr "RSS ચિહ્ન"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "ID (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "bbPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr ""

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "માફ કરશો, તમને તે કરવાની પરવાનગી નથી"

#: includes/class-acf-site-health.php:648
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr "આઇકન પીકરને મૂલ્યની જરૂર છે."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: includes/class-acf-site-health.php:709
msgid "JSON Load Paths"
msgstr ""

#: includes/class-acf-site-health.php:703
msgid "JSON Save Paths"
msgstr ""

#: includes/class-acf-site-health.php:694
msgid "Registered ACF Forms"
msgstr ""

#: includes/class-acf-site-health.php:688
msgid "Shortcode Enabled"
msgstr ""

#: includes/class-acf-site-health.php:680
msgid "Field Settings Tabs Enabled"
msgstr ""

#: includes/class-acf-site-health.php:672
msgid "Field Type Modal Enabled"
msgstr ""

#: includes/class-acf-site-health.php:664
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:655
msgid "Block Preloading Enabled"
msgstr ""

#: includes/class-acf-site-health.php:643
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:638
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:611
msgid "Registered ACF Blocks"
msgstr ""

#: includes/class-acf-site-health.php:605
msgid "Light"
msgstr ""

#: includes/class-acf-site-health.php:605
msgid "Standard"
msgstr "સામાન્ય"

#: includes/class-acf-site-health.php:604
msgid "REST API Format"
msgstr ""

#: includes/class-acf-site-health.php:596
msgid "Registered Options Pages (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:582
msgid "Registered Options Pages (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (UI)"
msgstr ""

#: includes/class-acf-site-health.php:547
msgid "Options Pages UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:539
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:527
msgid "Registered Taxonomies (UI)"
msgstr ""

#: includes/class-acf-site-health.php:515
msgid "Registered Post Types (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:503
msgid "Registered Post Types (UI)"
msgstr ""

#: includes/class-acf-site-health.php:490
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: includes/class-acf-site-health.php:483
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:478
msgid "Number of Fields by Field Type"
msgstr ""

#: includes/class-acf-site-health.php:445
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: includes/class-acf-site-health.php:432
msgid "Field Groups Enabled for REST API"
msgstr ""

#: includes/class-acf-site-health.php:420
msgid "Registered Field Groups (JSON)"
msgstr ""

#: includes/class-acf-site-health.php:408
msgid "Registered Field Groups (PHP)"
msgstr ""

#: includes/class-acf-site-health.php:396
msgid "Registered Field Groups (UI)"
msgstr ""

#: includes/class-acf-site-health.php:384
msgid "Active Plugins"
msgstr ""

#: includes/class-acf-site-health.php:358
msgid "Parent Theme"
msgstr ""

#: includes/class-acf-site-health.php:347
msgid "Active Theme"
msgstr ""

#: includes/class-acf-site-health.php:338
msgid "Is Multisite"
msgstr ""

#: includes/class-acf-site-health.php:333
msgid "MySQL Version"
msgstr ""

#: includes/class-acf-site-health.php:328
msgid "WordPress Version"
msgstr ""

#: includes/class-acf-site-health.php:321
msgid "Subscription Expiry Date"
msgstr ""

#: includes/class-acf-site-health.php:313
msgid "License Status"
msgstr ""

#: includes/class-acf-site-health.php:308
msgid "License Type"
msgstr ""

#: includes/class-acf-site-health.php:303
msgid "Licensed URL"
msgstr ""

#: includes/class-acf-site-health.php:297
msgid "License Activated"
msgstr ""

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr "મફત"

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr ""

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr "પ્લગઇન સંસ્કરણ"

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "કાયમી ધોરણે કાઢી નાખો"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr ""

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO લક્ષણ"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "અનલૉક કરવા માટે PRO રિન્યૂ કરો"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "PRO લાઇસન્સ રિન્યૂ કરો"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "સક્રિય લાયસન્સ વિના PRO ક્ષેત્રો સંપાદિત કરી શકાતા નથી."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"ACF બ્લોકને સોંપેલ ફીલ્ડ જૂથોને સંપાદિત કરવા માટે કૃપા કરીને તમારું ACF PRO લાઇસન્સ સક્રિય "
"કરો."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "આ વિકલ્પો પૃષ્ઠને સંપાદિત કરવા માટે કૃપા કરીને તમારું ACF PRO લાઇસન્સ સક્રિય કરો."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"છટકી ગયેલા HTML મૂલ્યો પરત કરવા માત્ર ત્યારે જ શક્ય છે જ્યારે format_value પણ સાચું હોય. "
"સુરક્ષા માટે ફીલ્ડ મૂલ્યો પરત કરવામાં આવ્યા નથી."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"એસ્કેપ કરેલ HTML મૂલ્ય પરત કરવું ત્યારે જ શક્ય છે જ્યારે format_value પણ સાચું હોય. સુરક્ષા "
"માટે ફીલ્ડ મૂલ્ય પરત કરવામાં આવ્યું નથી."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "વધુ વિગતો માટે કૃપા કરીને તમારા સાઇટ એડમિનિસ્ટ્રેટર અથવા ડેવલપરનો સંપર્ક કરો."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "વધુ&nbsp;જાણો"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "&nbsp;વિગતો છુપાવો"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "&nbsp;વિગતો બતાવો"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - %3$s દ્વારા પ્રસ્તુત"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "ACF PRO લાઇસન્સ રિન્યૂ કરો"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "લાયસન્સ રિન્યૂ કરો"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "લાયસન્સ મેનેજ કરો"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "બ્લોક એડિટરમાં 'ઉચ્ચ' સ્થિતિ સમર્થિત નથી"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "ACF PRO પર અપગ્રેડ કરો"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF <a href=\"%s\" target=\"_blank\">વિકલ્પો પૃષ્ઠો</a> એ ક્ષેત્રો દ્વારા વૈશ્વિક "
"સેટિંગ્સનું સંચાલન કરવા માટેના કસ્ટમ એડમિન પૃષ્ઠો છે. તમે બહુવિધ પૃષ્ઠો અને પેટા પૃષ્ઠો બનાવી "
"શકો છો."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "વિકલ્પો પૃષ્ઠ ઉમેરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "શીર્ષકના પ્લેસહોલ્ડર તરીકે ઉપયોગમાં લેવાતા એડિટરમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "શીર્ષક પ્લેસહોલ્ડર"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 મહિના મફત"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr " (%s માંથી ડુપ્લિકેટ)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "વિકલ્પો પૃષ્ઠો પસંદ કરો"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "ડુપ્લિકેટ વર્ગીકરણ"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "વર્ગીકરણ બનાવો"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "ડુપ્લિકેટ પોસ્ટ પ્રકાર"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "પોસ્ટ પ્રકાર બનાવો"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "ફીલ્ડ જૂથોને લિંક કરો"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "ક્ષેત્રો ઉમેરો"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "આ ક્ષેત્ર"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "એસીએફ પ્રો"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "પ્રતિસાદ"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "સપોર્ટ"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "દ્વારા વિકસિત અને જાળવણી કરવામાં આવે છે"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "પસંદ કરેલ ક્ષેત્ર જૂથોના સ્થાન નિયમોમાં આ %s ઉમેરો."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"દ્વિપક્ષીય સેટિંગને સક્ષમ કરવાથી તમે આ ફીલ્ડ માટે પસંદ કરેલ દરેક મૂલ્ય માટે લક્ષ્ય ફીલ્ડમાં મૂલ્ય "
"અપડેટ કરી શકો છો, પોસ્ટ ID, વર્ગીકરણ ID અથવા અપડેટ કરવામાં આવી રહેલી આઇટમના "
"વપરાશકર્તા ID ને ઉમેરીને અથવા દૂર કરી શકો છો. વધુ માહિતી માટે, કૃપા કરીને <a "
"href=\"%s\" target=\"_blank\">દસ્તાવેજીકરણ</a> વાંચો."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"અપડેટ કરવામાં આવી રહેલી આઇટમનો સંદર્ભ પાછો સંગ્રહિત કરવા માટે ક્ષેત્ર(ઓ) પસંદ કરો. તમે આ "
"ક્ષેત્ર પસંદ કરી શકો છો. જ્યાં આ ક્ષેત્ર પ્રદર્શિત થઈ રહ્યું છે તેની સાથે લક્ષ્ય ક્ષેત્રો સુસંગત "
"હોવા જોઈએ. ઉદાહરણ તરીકે, જો આ ક્ષેત્ર વર્ગીકરણ પર પ્રદર્શિત થાય છે, તો તમારું લક્ષ્ય "
"ક્ષેત્ર વર્ગીકરણ પ્રકારનું હોવું જોઈએ"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "લક્ષ્ય ક્ષેત્ર"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "આ ID નો સંદર્ભ આપીને પસંદ કરેલ મૂલ્યો પર ફીલ્ડ અપડેટ કરો"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "દ્વિપક્ષીય"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s ફીલ્ડ"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "બહુવિધ પસંદ કરો"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "WP એન્જિન લોગો"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "લોઅર કેસ અક્ષરો, માત્ર અન્ડરસ્કોર અને ડૅશ, મહત્તમ 32 અક્ષરો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "આ વર્ગીકરણની શરતો સોંપવા માટેની ક્ષમતાનું નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "ટર્મ ક્ષમતા સોંપો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "આ વર્ગીકરણની શરતોને કાઢી નાખવા માટેની ક્ષમતાનું નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "ટર્મ ક્ષમતા કાઢી નાખો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "આ વર્ગીકરણની શરતોને સંપાદિત કરવા માટેની ક્ષમતાનું નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "શરતો ક્ષમતા સંપાદિત કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "આ વર્ગીકરણની શરતોનું સંચાલન કરવા માટેની ક્ષમતાનું નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "શરતોની ક્ષમતા મેનેજ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"શોધ પરિણામો અને વર્ગીકરણ આર્કાઇવ પૃષ્ઠોમાંથી પોસ્ટ્સને બાકાત રાખવા જોઈએ કે કેમ તે સેટ કરે "
"છે."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP એન્જિનના વધુ સાધનો"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "%s પરની ટીમ દ્વારા વર્ડપ્રેસ સાથે બિલ્ડ કરનારાઓ માટે બનાવેલ છે"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "કિંમત અને અપગ્રેડ જુઓ"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "વધુ શીખો"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"તમારા વર્કફ્લોને ઝડપી બનાવો અને ACF બ્લોક્સ અને ઓપ્શન્સ પેજીસ જેવી સુવિધાઓ અને રિપીટર, "
"ફ્લેક્સિબલ કન્ટેન્ટ, ક્લોન અને ગેલેરી જેવા અત્યાધુનિક ફીલ્ડ પ્રકારો સાથે વધુ સારી વેબસાઇટ્સ "
"વિકસાવો."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "ACF PRO સાથે અદ્યતન સુવિધાઓને અનલૉક કરો અને હજી વધુ બનાવો"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s ફીલ્ડ"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "કોઈ શરતો નથી"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "કોઈ પોસ્ટ પ્રકાર નથી"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "કોઈ પોસ્ટ નથી"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "કોઈ વર્ગીકરણ નથી"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "કોઈ ક્ષેત્ર જૂથો નથી"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "કોઈ ફીલ્ડ નથી"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "કોઈ વર્ણન નથી"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "કોઈપણ પોસ્ટ સ્થિતિ"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"આ વર્ગીકરણ કી પહેલેથી જ ACF ની બહાર નોંધાયેલ અન્ય વર્ગીકરણ દ્વારા ઉપયોગમાં લેવાય છે અને "
"તેનો ઉપયોગ કરી શકાતો નથી."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"આ વર્ગીકરણ કી પહેલેથી જ ACF માં અન્ય વર્ગીકરણ દ્વારા ઉપયોગમાં લેવાય છે અને તેનો ઉપયોગ "
"કરી શકાતો નથી."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"વર્ગીકરણ કી માં માત્ર લોઅર કેસ આલ્ફાન્યૂમેરિક અક્ષરો, અન્ડરસ્કોર અથવા ડેશ હોવા જોઈએ."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "વર્ગીકરણ કી 32 અક્ષરોથી ઓછી હોવી જોઈએ."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "ટ્રેશમાં કોઈ વર્ગીકરણ મળ્યું નથી"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "કોઈ વર્ગીકરણ મળ્યું નથી"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "વર્ગીકરણ શોધો"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "વર્ગીકરણ જુઓ"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "નવુ વર્ગીકરણ"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "વર્ગીકરણ સંપાદિત કરો"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "નવુ વર્ગીકરણ ઉમેરો"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "ટ્રેશમાં કોઈ પોસ્ટ પ્રકારો મળ્યા નથી"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "કોઈ પોસ્ટ પ્રકારો મળ્યા નથી"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "પોસ્ટ પ્રકારો શોધો"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "પોસ્ટનો પ્રકાર જુઓ"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "નવો પોસ્ટ પ્રકાર"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "પોસ્ટ પ્રકાર સંપાદિત કરો"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "નવો પોસ્ટ પ્રકાર ઉમેરો"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"આ પોસ્ટ ટાઇપ કી પહેલેથી જ ACF ની બહાર નોંધાયેલ અન્ય પોસ્ટ પ્રકાર દ્વારા ઉપયોગમાં લેવાય "
"છે અને તેનો ઉપયોગ કરી શકાતો નથી."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"આ પોસ્ટ ટાઇપ કી પહેલેથી જ ACF માં અન્ય પોસ્ટ પ્રકાર દ્વારા ઉપયોગમાં છે અને તેનો ઉપયોગ "
"કરી શકાતો નથી."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"આ ફીલ્ડ વર્ડપ્રેસનો <a href=\"%s\" target=\"_blank\">આરક્ષિત શબ્દ</a> ન હોવો જોઈએ."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"પોસ્ટ ટાઈપ કીમાં માત્ર લોઅર કેસ આલ્ફાન્યૂમેરિક અક્ષરો, અન્ડરસ્કોર અથવા ડેશ હોવા જોઈએ."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "પોસ્ટ પ્રકાર કી 20 અક્ષરોથી ઓછી હોવી જોઈએ."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "અમે ACF બ્લોક્સમાં આ ક્ષેત્રનો ઉપયોગ કરવાની ભલામણ કરતા નથી."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"વર્ડપ્રેસ WYSIWYG એડિટર પ્રદર્શિત કરે છે જે પોસ્ટ્સ અને પૃષ્ઠો જોવા મળે છે જે સમૃદ્ધ ટેક્સ્ટ-"
"એડિટિંગ અનુભવ માટે પરવાનગી આપે છે જે મલ્ટીમીડિયા સામગ્રી માટે પણ પરવાનગી આપે છે."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG સંપાદક"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"એક અથવા વધુ વપરાશકર્તાઓની પસંદગીની મંજૂરી આપે છે જેનો ઉપયોગ ડેટા ઑબ્જેક્ટ્સ વચ્ચે સંબંધ "
"બનાવવા માટે થઈ શકે છે."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "ખાસ કરીને વેબ એડ્રેસ સ્ટોર કરવા માટે રચાયેલ ટેક્સ્ટ ઇનપુટ."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"એક ટૉગલ જે તમને 1 અથવા 0 (ચાલુ અથવા બંધ, સાચું કે ખોટું, વગેરે) નું મૂલ્ય પસંદ કરવાની મંજૂરી "
"આપે છે. સ્ટાઇલાઇઝ્ડ સ્વીચ અથવા ચેકબોક્સ તરીકે રજૂ કરી શકાય છે."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"સમય પસંદ કરવા માટે એક ઇન્ટરેક્ટિવ UI. ક્ષેત્ર સેટિંગ્સ ઉપયોગ કરીને સમય ફોર્મેટ કસ્ટમાઇઝ કરી "
"શકાય છે."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "ટેક્સ્ટના ફકરાને સ્ટોર કરવા માટે મૂળભૂત ટેક્સ્ટેરિયા ઇનપુટ."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "મૂળભૂત મૂળ લખાણ ઇનપુટ, એક તાર મૂલ્યો સંગ્રહ કરવા માટે ઉપયોગી."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"ફીલ્ડ સેટિંગ્સમાં ઉલ્લેખિત માપદંડ અને વિકલ્પોના આધારે એક અથવા વધુ વર્ગીકરણ શરતોની પસંદગીની "
"મંજૂરી આપે છે."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"તમને સંપાદન સ્ક્રીનમાં ટેબ કરેલ વિભાગોમાં ક્ષેત્રોને જૂથબદ્ધ કરવાની મંજૂરી આપે છે. ક્ષેત્રોને "
"વ્યવસ્થિત અને સંરચિત રાખવા માટે ઉપયોગી."

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr "તમે ઉલ્લેખિત કરો છો તે પસંદગીઓની પસંદગી સાથે ડ્રોપડાઉન સૂચિ."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"તમે હાલમાં સંપાદિત કરી રહ્યાં છો તે આઇટમ સાથે સંબંધ બનાવવા માટે એક અથવા વધુ પોસ્ટ્સ, પૃષ્ઠો "
"અથવા કસ્ટમ પોસ્ટ પ્રકારની આઇટમ્સ પસંદ કરવા માટેનું ડ્યુઅલ-કૉલમ ઇન્ટરફેસ. શોધવા અને ફિલ્ટર "
"કરવાના વિકલ્પોનો સમાવેશ થાય છે."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"શ્રેણી સ્લાઇડર તત્વનો ઉપયોગ કરીને ઉલ્લેખિત શ્રેણીમાં સંખ્યાત્મક મૂલ્ય પસંદ કરવા માટેનું ઇનપુટ."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"રેડિયો બટન ઇનપુટ્સનું એક જૂથ જે વપરાશકર્તાને તમે ઉલ્લેખિત મૂલ્યોમાંથી એક જ પસંદગી કરવાની "
"મંજૂરી આપે છે."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"શોધવાના વિકલ્પ સાથે એક અથવા ઘણી પોસ્ટ્સ, પૃષ્ઠો અથવા પોસ્ટ પ્રકારની વસ્તુઓ પસંદ કરવા "
"માટે એક ઇન્ટરેક્ટિવ અને વૈવિધ્યપૂર્ણ UI. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "માસ્ક્ડ ફીલ્ડનો ઉપયોગ કરીને પાસવર્ડ આપવા માટેનું ઇનપુટ."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "પોસ્ટ સ્ટેટસ દ્વારા ફિલ્ટર કરો"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"શોધવાના વિકલ્પ સાથે, એક અથવા વધુ પોસ્ટ્સ, પૃષ્ઠો, કસ્ટમ પોસ્ટ પ્રકારની આઇટમ્સ અથવા "
"આર્કાઇવ URL પસંદ કરવા માટે એક ઇન્ટરેક્ટિવ ડ્રોપડાઉન."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"મૂળ WordPress oEmbed કાર્યક્ષમતાનો ઉપયોગ કરીને વિડિઓઝ, છબીઓ, ટ્વીટ્સ, ઑડિઓ અને અન્ય "
"સામગ્રીને એમ્બેડ કરવા માટે એક ઇન્ટરેક્ટિવ ઘટક."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "સંખ્યાત્મક મૂલ્યો સુધી મર્યાદિત ઇનપુટ."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"અન્ય ક્ષેત્રોની સાથે સંપાદકોને સંદેશ પ્રદર્શિત કરવા માટે વપરાય છે. તમારા ક્ષેત્રોની આસપાસ "
"વધારાના સંદર્ભ અથવા સૂચનાઓ પ્રદાન કરવા માટે ઉપયોગી."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"તમને વર્ડપ્રેસ મૂળ લિંક પીકરનો ઉપયોગ કરીને લિંક અને તેના ગુણધર્મો જેવા કે શીર્ષક અને લક્ષ્યનો "
"ઉલ્લેખ કરવાની મંજૂરી આપે છે."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr "અપલોડ કરવા અથવા છબીઓ પસંદ કરવા માટે મૂળ વર્ડપ્રેસ મીડિયા પીકરનો ઉપયોગ કરે છે."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"ડેટા અને સંપાદન સ્ક્રીનને વધુ સારી રીતે ગોઠવવા માટે જૂથોમાં ક્ષેત્રોને સંરચિત કરવાનો માર્ગ "
"પૂરો પાડે છે."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Google નકશાનો ઉપયોગ કરીને સ્થાન પસંદ કરવા માટે એક ઇન્ટરેક્ટિવ UI. યોગ્ય રીતે પ્રદર્શિત "
"કરવા માટે Google Maps API કી અને વધારાના ગોઠવણીની જરૂર છે."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr "અપલોડ કરવા અથવા છબીઓ પસંદ કરવા માટે મૂળ વર્ડપ્રેસ મીડિયા પીકરનો ઉપયોગ કરે છે."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "ખાસ કરીને ઈમેલ એડ્રેસ સ્ટોર કરવા માટે રચાયેલ ટેક્સ્ટ ઇનપુટ."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"તારીખ અને સમય પસંદ કરવા માટે એક ઇન્ટરેક્ટિવ UI. તારીખ રીટર્ન ફોર્મેટ ફીલ્ડ સેટિંગ્સનો "
"ઉપયોગ કરીને કસ્ટમાઇઝ કરી શકાય છે."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"સમય પસંદ કરવા માટે એક ઇન્ટરેક્ટિવ UI. ક્ષેત્ર સેટિંગ્સ ઉપયોગ કરીને સમય ફોર્મેટ કસ્ટમાઇઝ કરી "
"શકાય છે."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "રંગ પસંદ કરવા અથવા હેક્સ મૂલ્યનો ઉલ્લેખ કરવા માટે એક ઇન્ટરેક્ટિવ UI."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"ચેકબૉક્સ ઇનપુટ્સનું જૂથ કે જે વપરાશકર્તાને તમે ઉલ્લેખિત કરો છો તે એક અથવા બહુવિધ મૂલ્યો પસંદ "
"કરવાની મંજૂરી આપે છે."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"તમે ઉલ્લેખિત કરેલ મૂલ્યો સાથેના બટનોનું જૂથ, વપરાશકર્તાઓ પ્રદાન કરેલ મૂલ્યોમાંથી એક વિકલ્પ "
"પસંદ કરી શકે છે."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"તમને કન્ટેન્ટ સંપાદિત કરતી વખતે બતાવવામાં આવતી સંકુચિત પેનલ્સમાં કસ્ટમ ફીલ્ડ્સને જૂથ અને "
"ગોઠવવાની મંજૂરી આપે છે. મોટા ડેટાસેટ્સને વ્યવસ્થિત રાખવા માટે ઉપયોગી."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"આ સ્લાઇડ્સ, ટીમના સભ્યો અને કૉલ-ટુ-એક્શન ટાઇલ્સ જેવી સામગ્રીને પુનરાવર્તિત કરવા માટેનો "
"ઉકેલ પૂરો પાડે છે, પેરન્ટ તરીકે કામ કરીને પેટાફિલ્ડના સમૂહ કે જેને વારંવાર પુનરાવર્તિત કરી "
"શકાય છે."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"આ જોડાણોના સંગ્રહનું સંચાલન કરવા માટે એક ઇન્ટરેક્ટિવ ઇન્ટરફેસ પૂરું પાડે છે. મોટાભાગની સેટિંગ્સ "
"ઇમેજ ફીલ્ડના પ્રકાર જેવી જ હોય ​​છે. વધારાની સેટિંગ્સ તમને ગેલેરીમાં નવા જોડાણો ક્યાં "
"ઉમેરવામાં આવે છે અને જોડાણોની ન્યૂનતમ/મહત્તમ સંખ્યાને મંજૂરી આપે છે તેનો ઉલ્લેખ કરવાની મંજૂરી આપે "
"છે."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"આ એક સરળ, સંરચિત, લેઆઉટ-આધારિત સંપાદક પ્રદાન કરે છે. લવચીક સામગ્રી ક્ષેત્ર તમને ઉપલબ્ધ "
"બ્લોક્સ ડિઝાઇન કરવા માટે લેઆઉટ અને સબફિલ્ડનો ઉપયોગ કરીને સંપૂર્ણ નિયંત્રણ સાથે સામગ્રીને "
"વ્યાખ્યાયિત કરવા, બનાવવા અને સંચાલિત કરવાની મંજૂરી આપે છે."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"આ તમને વર્તમાન ક્ષેત્રોને પસંદ કરવા અને પ્રદર્શિત કરવાની મંજૂરી આપે છે. તે ડેટાબેઝમાં કોઈપણ "
"ફીલ્ડની નકલ કરતું નથી, પરંતુ રન-ટાઇમ પર પસંદ કરેલ ફીલ્ડ્સને લોડ કરે છે અને પ્રદર્શિત કરે છે. "
"ક્લોન ફીલ્ડ કાં તો પોતાને પસંદ કરેલ ફીલ્ડ્સ સાથે બદલી શકે છે અથવા પસંદ કરેલ ફીલ્ડ્સને "
"સબફિલ્ડના જૂથ તરીકે પ્રદર્શિત કરી શકે છે."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "ક્લોન"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "પ્રો"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "અદ્યતન"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (નવું)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "અસલ"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "અમાન્ય પોસ્ટ આઈડી."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "સમીક્ષા માટે અમાન્ય પોસ્ટ પ્રકાર પસંદ કર્યો."

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "વધુ"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "ટ્યુટોરીયલ"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "ક્ષેત્ર પસંદ કરો"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "એક અલગ શોધ શબ્દ અજમાવો અથવા %s બ્રાઉઝ કરો"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "લોકપ્રિય ક્ષેત્રો"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "'%s' માટે કોઈ શોધ પરિણામો નથી"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "ફીલ્ડ્સ શોધો..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "ક્ષેત્ર પ્રકાર પસંદ કરો"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "પ્રખ્યાત"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "વર્ગીકરણ ઉમેરો"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "પોસ્ટ પ્રકારની સામગ્રીને વર્ગીકૃત કરવા માટે કસ્ટમ વર્ગીકરણ બનાવો"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "તમારી પ્રથમ વર્ગીકરણ ઉમેરો"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "અધિક્રમિક વર્ગીકરણમાં વંશજો હોઈ શકે છે (જેમ કે શ્રેણીઓ)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "અગ્રભાગ પર અને એડમિન ડેશબોર્ડમાં વર્ગીકરણ દૃશ્યમાન બનાવે છે."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "આ વર્ગીકરણ સાથે વર્ગીકૃત કરી શકાય તેવા એક અથવા ઘણા પોસ્ટ પ્રકારો."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "શૈલી"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "શૈલી"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "શૈલીઓ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr "`WP_REST_Terms_Controller` ને બદલે વાપરવા માટે વૈકલ્પિક કસ્ટમ નિયંત્રક."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "REST API માં આ પોસ્ટ પ્રકારનો પર્દાફાશ કરો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "ક્વેરી વેરીએબલ નામને કસ્ટમાઇઝ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"બિન-સુંદર પરમાલિંકનો ઉપયોગ કરીને શરતોને ઍક્સેસ કરી શકાય છે, દા.ત., {query_var}"
"={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "આ વર્ગીકરણ માટે પરમાલિંક્સ અક્ષમ છે."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr "સ્લગ તરીકે વર્ગીકરણ કીનો ઉપયોગ કરીને URL ને ફરીથી લખો. તમારું પરમાલિંક માળખું હશે"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "વર્ગીકરણ કી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "આ વર્ગીકરણ માટે વાપરવા માટે પરમાલિંકનો પ્રકાર પસંદ કરો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "પોસ્ટ ટાઇપ લિસ્ટિંગ સ્ક્રીન પર વર્ગીકરણ માટે કૉલમ પ્રદર્શિત કરો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "એડમિન કૉલમ બતાવો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "ઝડપી/બલ્ક સંપાદન પેનલમાં વર્ગીકરણ બતાવો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "ઝડપી સંપાદન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "ટેગ ક્લાઉડ વિજેટ નિયંત્રણોમાં વર્ગીકરણની સૂચિ બનાવો."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "ટૅગ ક્લાઉડ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"મેટા બૉક્સમાંથી સાચવેલા વર્ગીકરણ ડેટાને સેનિટાઇઝ કરવા માટે કૉલ કરવા માટે PHP ફંક્શન નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "મેટા બોક્સ સેનિટાઈઝેશન કોલબેક"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "મેટા બોક્સ કોલબેક રજીસ્ટર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "કોઈ મેટા બોક્સ નથી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "કસ્ટમ મેટા બોક્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"સામગ્રી સંપાદક સ્ક્રીન પરના મેટા બોક્સને નિયંત્રિત કરે છે. મૂળભૂત રીતે, શ્રેણીઓ મેટા બોક્સ "
"અધિક્રમિક વર્ગીકરણ માટે બતાવવામાં આવે છે, અને ટૅગ્સ મેટા બૉક્સ બિન-હાયરાર્કિકલ વર્ગીકરણ "
"માટે બતાવવામાં આવે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "મેટા બોક્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "શ્રેણીઓ મેટા બોક્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "ટૅગ્સ મેટા બોક્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "ટેગની લિંક"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "બ્લોક એડિટરમાં વપરાતી નેવિગેશન લિંક બ્લોક ભિન્નતાનું વર્ણન કરે છે."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "%s ની લિંક"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "ટૅગ લિંક"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr "બ્લોક એડિટરમાં વપરાતી નેવિગેશન લિંક બ્લોક ભિન્નતાનું વર્ણન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← ટૅગ્સ પર જાઓ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"શબ્દને અપડેટ કર્યા પછી મુખ્ય અનુક્રમણિકા સાથે પાછા લિંક કરવા માટે વપરાયેલ ટેક્સ્ટને સોંપે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "આઇટમ્સ પર પાછા ફરો"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← %s પર જાઓ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "ટૅગ્સની સૂચિ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "કોષ્ટક છુપાયેલા મથાળાને ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "ટૅગ્સ સૂચિ નેવિગેશન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "કોષ્ટક પૃષ્ઠ ક્રમાંકન છુપાયેલા મથાળાને ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "શ્રેણી દ્વારા ફિલ્ટર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "પોસ્ટ લિસ્ટ ટેબલમાં ફિલ્ટર બટન પર ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "આઇટમ દ્વારા ફિલ્ટર કરો"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "%s દ્વારા ફિલ્ટર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr "આ વર્ણન મૂળભૂત રીતે જાણીતું નથી; તેમ છતાં, કોઈક થિમમા કદાચ દેખાય."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "એડિટ ટૅગ્સ સ્ક્રીન પર પેરેન્ટ ફીલ્ડનું વર્ણન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "વર્ણન ક્ષેત્રનું વર્ણન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"અધિક્રમ(hierarchy) બનાવવા માટે એક પેરન્ટ ટર્મ સોંપો. ઉદાહરણ તરીકે જાઝ ટર્મ, બેબોપ અને "
"બિગ બેન્ડની પેરન્ટ હશે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "એડિટ ટૅગ્સ સ્ક્રીન પર પેરેન્ટ ફીલ્ડનું વર્ણન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "પિતૃ ક્ષેત્રનું વર્ણન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"\"સ્લગ\" એ નામનું URL-મૈત્રીપૂર્ણ સંસ્કરણ છે. તે સામાન્ય રીતે બધા લોઅરકેસ હોય છે અને તેમાં "
"માત્ર અક્ષરો, સંખ્યાઓ અને હાઇફન્સ હોય છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "એડિટ ટૅગ્સ સ્ક્રીન પર નામ ફીલ્ડનું વર્ણન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "નામ ક્ષેત્ર વર્ણન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "નામ જે તમારી સાઇટ પર દેખાશે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "એડિટ ટૅગ્સ સ્ક્રીન પર નામ ફીલ્ડનું વર્ણન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "નામ ક્ષેત્ર વર્ણન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "ટૅગ્સ નથી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"જ્યારે કોઈ ટૅગ્સ અથવા કૅટેગરીઝ ઉપલબ્ધ ન હોય ત્યારે પોસ્ટ્સ અને મીડિયા સૂચિ કોષ્ટકોમાં "
"પ્રદર્શિત ટેક્સ્ટને અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "કોઈ શરતો નથી"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "ના %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "કોઈ ટેગ મળ્યા નથી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"જ્યારે કોઈ ટૅગ્સ ઉપલબ્ધ ન હોય ત્યારે વર્ગીકરણ મેટા બૉક્સમાં 'સૌથી વધુ વપરાયેલામાંથી પસંદ "
"કરો' ટેક્સ્ટને ક્લિક કરતી વખતે પ્રદર્શિત ટેક્સ્ટને અસાઇન કરે છે અને જ્યારે વર્ગીકરણ માટે કોઈ "
"આઇટમ ન હોય ત્યારે ટર્મ્સ લિસ્ટ કોષ્ટકમાં વપરાયેલ ટેક્સ્ટને અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "મળ્યું નથી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "સૌથી વધુ ઉપયોગમાં લેવાતી ટેબના શીર્ષક ક્ષેત્રમાં ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "સૌથી વધુ વપરાયેલ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "સૌથી વધુ ઉપયોગ થયેલ ટૅગ્સ માંથી પસંદ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"જ્યારે JavaScript અક્ષમ હોય ત્યારે મેટા બૉક્સમાં વપરાયેલ 'સૌથી વધુ ઉપયોગમાંથી પસંદ કરો' "
"ટેક્સ્ટને અસાઇન કરે છે. માત્ર બિન-હાયરાર્કીકલ વર્ગીકરણ પર વપરાય છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "સૌથી વધુ વપરાયેલમાંથી પસંદ કરો"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "%s સૌથી વધુ ઉપયોગ થયેલ ટૅગ્સ માંથી પસંદ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "ટૅગ્સ ઉમેરો અથવા દૂર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"જ્યારે JavaScript અક્ષમ હોય ત્યારે મેટા બૉક્સમાં ઉપયોગમાં લેવાતી આઇટમ્સ ઉમેરો અથવા દૂર "
"કરો ટેક્સ્ટને સોંપે છે. માત્ર બિન-હાયરાર્કીકલ વર્ગીકરણ પર વપરાય છે"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "વસ્તુઓ ઉમેરો અથવા દૂર કરો"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "%s ઉમેરો અથવા દૂર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "અલ્પવિરામથી ટૅગ્સ અલગ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"વર્ગીકરણ મેટા બોક્સમાં વપરાયેલ અલ્પવિરામ ટેક્સ્ટ સાથે અલગ આઇટમ સોંપે છે. માત્ર બિન-"
"હાયરાર્કીકલ વર્ગીકરણ પર વપરાય છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "અલ્પવિરામથી ટૅગ્સ અલગ કરો"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "અલ્પવિરામથી %s ને અલગ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "લોકપ્રિય ટૅગ્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "લોકપ્રિય આઇટમ ટેક્સ્ટ અસાઇન કરે છે. માત્ર બિન-હાયરાર્કીકલ વર્ગીકરણ માટે વપરાય છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "લોકપ્રિય વસ્તુઓ"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "લોકપ્રિય %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "ટૅગ્સ શોધો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "શોધ આઇટમ ટેક્સ્ટ સોંપે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "પિતૃ શ્રેણી:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "પેરેન્ટ આઇટમ ટેક્સ્ટ અસાઇન કરે છે, પરંતુ અંતમાં કોલોન (:) સાથે ઉમેરવામાં આવે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "કોલોન સાથે પિતૃ શ્રેણી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "પિતૃ શ્રેણી"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "પેરેન્ટ આઇટમ ટેક્સ્ટ અસાઇન કરે છે. માત્ર અધિક્રમિક વર્ગીકરણ પર વપરાય છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "પિતૃ વસ્તુ"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "પેરન્ટ %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "નવા ટેગ નું નામ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "નવી આઇટમ નામનો ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "નવી આઇટમનું નામ"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "નવું %s નામ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "નવું ટેગ ઉમેરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "નવી આઇટમ ઉમેરો ટેક્સ્ટ સોંપે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "અદ્યતન ટેગ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "અપડેટ આઇટમ ટેક્સ્ટ સોંપે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "આઇટમ અપડેટ કરો"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "%s અપડેટ કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "ટેગ જુઓ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "સંપાદન દરમિયાન શબ્દ જોવા માટે એડમિન બારમાં."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "ટેગ માં ફેરફાર કરો"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "શબ્દ સંપાદિત કરતી વખતે સંપાદક સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "બધા ટૅગ્સ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "બધી આઇટમ ટેક્સ્ટ અસાઇન કરે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "મેનુ નામ લખાણ સોંપે છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "મેનુ લેબલ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "સક્રિય વર્ગીકરણ વર્ડપ્રેસ સાથે સક્ષમ અને નોંધાયેલ છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "વર્ગીકરણનો વર્ણનાત્મક સારાંશ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "શબ્દનો વર્ણનાત્મક સારાંશ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "ટર્મ વર્ણન"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "એક શબ્દ, કોઈ જગ્યા નથી. અન્ડરસ્કોર અને ડેશની મંજૂરી છે."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "ટર્મ સ્લગ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "મૂળભૂત શબ્દનું નામ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "ટર્મ નામ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"વર્ગીકરણ માટે એક શબ્દ બનાવો કે જેને કાઢી ન શકાય. તે મૂળભૂત રીતે પોસ્ટ્સ માટે પસંદ કરવામાં "
"આવશે નહીં."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "ડિફૉલ્ટ ટર્મ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"શું આ વર્ગીકરણની શરતો `wp_set_object_terms()` ને પ્રદાન કરવામાં આવી છે તે ક્રમમાં સૉર્ટ "
"કરવી જોઈએ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "સૉર્ટ શરતો"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "નવો પોસ્ટ પ્રકાર"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"વર્ડપ્રેસની કાર્યક્ષમતાને કસ્ટમ પોસ્ટ પ્રકારો સાથે પ્રમાણભૂત પોસ્ટ્સ અને પૃષ્ઠોથી આગળ વિસ્તૃત "
"કરો"

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "તમારો પ્રથમ પોસ્ટ પ્રકાર ઉમેરો"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "હું જાણું છું કે હું શું કરી રહ્યો છું, મને બધા વિકલ્પો બતાવો."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "અદ્યતન રૂપરેખાંકન"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "અધિક્રમિક વર્ગીકરણમાં વંશજો હોઈ શકે છે (જેમ કે શ્રેણીઓ)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "વંશવેલો"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "અગ્રભાગ પર અને એડમિન ડેશબોર્ડમાં દૃશ્યમાન."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "પબ્લિક"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "ફિલ્મ"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "લોઅર કેસ અક્ષરો, માત્ર અન્ડરસ્કોર અને ડૅશ, મહત્તમ ૨૦ અક્ષરો."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "ફિલ્મ"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "એકવચન નામ"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "મૂવીઝ"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "બહુવચન નામ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr "`WP_REST_Posts_Controller` ને બદલે વાપરવા માટે વૈકલ્પિક કસ્ટમ નિયંત્રક."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "નિયંત્રક વર્ગ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "REST API URL નો નેમસ્પેસ ભાગ."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "નેમસ્પેસ રૂટ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "પોસ્ટ પ્રકાર REST API URL માટે આધાર URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "આધાર URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"REST API માં આ પોસ્ટ પ્રકારનો પર્દાફાશ કરે છે. બ્લોક એડિટરનો ઉપયોગ કરવા માટે જરૂરી છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "REST API માં બતાવો"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "ક્વેરી વેરીએબલ નામને કસ્ટમાઇઝ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "ક્વેરી વેરીએબલ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "કોઈ ક્વેરી વેરીએબલ સપોર્ટ નથી"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "કસ્ટમ ક્વેરી વેરીએબલ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"બિન-સુંદર પરમાલિંકનો ઉપયોગ કરીને શરતોને ઍક્સેસ કરી શકાય છે, દા.ત., {query_var}"
"={term_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "વેરીએબલ સપોર્ટ ક્વેરી"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr "આઇટમ અને આઇટમ્સ માટેના URL ને ક્વેરી સ્ટ્રિંગ વડે એક્સેસ કરી શકાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "સાર્વજનિક રૂપે પૂછવા યોગ્ય"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"તમારી થીમમાં આર્કાઇવ ટેમ્પલેટ ફાઇલ સાથે કસ્ટમાઇઝ કરી શકાય તેવી આઇટમ આર્કાઇવ ધરાવે છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "આર્કાઇવ્સ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "આર્કાઇવ્સ જેવી વસ્તુઓ URL માટે પૃષ્ઠ ક્રમાંકન સપોર્ટ."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "પૃષ્ઠ ક્રમાંકન"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "પોસ્ટ પ્રકારની વસ્તુઓ માટે RSS ફીડ URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "ફીડ URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr "URL માં `WP_Rwrite::$front` ઉપસર્ગ ઉમેરવા માટે પરમાલિંક માળખું બદલે છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "ફ્રન્ટ URL ઉપસર્ગ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL સ્લગ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "આ વર્ગીકરણ માટે પરમાલિંક્સ અક્ષમ છે."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"નીચેના ઇનપુટમાં વ્યાખ્યાયિત કસ્ટમ સ્લગનો ઉપયોગ કરીને URL ને ફરીથી લખો. તમારું પરમાલિંક "
"માળખું હશે"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "કોઈ પરમાલિંક નથી (URL પુનઃલેખન અટકાવો)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "કસ્ટમ પરમાલિંક"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "પોસ્ટ પ્રકાર કી"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr "સ્લગ તરીકે વર્ગીકરણ કીનો ઉપયોગ કરીને URL ને ફરીથી લખો. તમારું પરમાલિંક માળખું હશે"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "પરમાલિંક ફરીથી લખો"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "જ્યારે તે વપરાશકર્તા કાઢી નાખવામાં આવે ત્યારે વપરાશકર્તા દ્વારા આઇટમ્સ કાઢી નાખો."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "વપરાશકર્તા સાથે કાઢી નાખો"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "પોસ્ટ પ્રકારને 'ટૂલ્સ' > 'નિકાસ'માંથી નિકાસ કરવાની મંજૂરી આપો."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "નિકાસ કરી શકે છે"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "વૈકલ્પિક રીતે ક્ષમતાઓમાં ઉપયોગમાં લેવા માટે બહુવચન પ્રદાન કરો."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "બહુવચન ક્ષમતા નામ"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr "આ પોસ્ટ પ્રકાર માટેની ક્ષમતાઓને આધાર આપવા માટે અન્ય પોસ્ટ પ્રકાર પસંદ કરો."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "એકવચન ક્ષમતા નામ"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"મૂળભૂત રીતે પોસ્ટ પ્રકારની ક્ષમતાઓ 'પોસ્ટ' ક્ષમતાના નામોને વારસામાં મેળવશે, દા.ત. "
"એડિટ_પોસ્ટ, ડિલીટ_પોસ્ટ. પોસ્ટ પ્રકારની વિશિષ્ટ ક્ષમતાઓનો ઉપયોગ કરવા સક્ષમ કરો, "
"દા.ત. edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "ક્ષમતાઓનું નામ બદલો"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "શોધમાંથી બાકાત રાખો"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"આઇટમ્સને 'દેખાવ' > 'મેનૂઝ' સ્ક્રીનમાં મેનૂમાં ઉમેરવાની મંજૂરી આપો. 'સ્ક્રીન વિકલ્પો'માં ચાલુ "
"કરવું આવશ્યક છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "દેખાવ મેનુ આધાર"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "એડમિન બારમાં 'નવા' મેનૂમાં આઇટમ તરીકે દેખાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "એડમિન બારમાં બતાવો"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "કસ્ટમ મેટા બોક્સ કોલબેક"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "મેનુ આયકન"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "એડમિન ડેશબોર્ડમાં સાઇડબાર મેનૂમાં સ્થિતિ."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "મેનુ સ્થિતિ"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"ડિફૉલ્ટ રૂપે પોસ્ટના પ્રકારને એડમિન મેનૂમાં નવી ટોચની આઇટમ મળશે. જો હાલની ટોચની આઇટમ "
"અહીં પૂરી પાડવામાં આવે છે, તો પોસ્ટનો પ્રકાર તેની હેઠળ સબમેનુ આઇટમ તરીકે ઉમેરવામાં આવશે."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "એડમિન મેનુ પેરન્ટ"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "સાઇડબાર મેનૂમાં એડમિન એડિટર નેવિગેશન."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "એડમિન મેનુમાં બતાવો"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "એડમિન ડેશબોર્ડમાં વસ્તુઓને સંપાદિત અને સંચાલિત કરી શકાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "UI માં બતાવો"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "પોસ્ટની લિંક."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "નેવિગેશન લિંક બ્લોક વિવિધતા માટે વર્ણન."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "આઇટમ લિંક વર્ણન"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "%s ની લિંક."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "પોસ્ટ લિંક"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "નેવિગેશન લિંક બ્લોક વિવિધતા માટે શીર્ષક."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "આઇટમ લિંક"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s લિંક"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "પોસ્ટ અપડેટ થઇ ગઈ છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "આઇટમ અપડેટ થયા પછી એડિટર નોટિસમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "આઈટમ અપડેટેડ."

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s અપડેટ થઇ ગયું!"

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "સુનિશ્ચિત પોસ્ટ."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "આઇટમ સુનિશ્ચિત કર્યા પછી સંપાદક સૂચનામાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "આઇટમ સુનિશ્ચિત"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s શેડ્યૂલ."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "પોસ્ટ ડ્રાફ્ટમાં પાછું ફેરવ્યું."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "આઇટમને ડ્રાફ્ટમાં પાછી ફેરવ્યા પછી સંપાદક સૂચનામાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "આઇટમ ડ્રાફ્ટમાં પાછી ફેરવાઈ"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s ડ્રાફ્ટમાં પાછું ફર્યું."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "પોસ્ટ ખાનગી રીતે પ્રકાશિત."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "આઇટમ સુનિશ્ચિત કર્યા પછી સંપાદક સૂચનામાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "આઇટમ ખાનગી રીતે પ્રકાશિત"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s ખાનગી રીતે પ્રકાશિત."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "પોસ્ટ પ્રકાશિત થઇ ગઈ છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "આઇટમ સુનિશ્ચિત કર્યા પછી સંપાદક સૂચનામાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "આઇટમ પ્રકાશિત"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s પ્રકાશિત."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "પોસ્ટ્સ યાદી"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીન પરની ફિલ્ટર લિંક્સ માટે સ્ક્રીન રીડર્સ દ્વારા ઉપયોગમાં લેવાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "આઇટ્મસ  યાદી"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s યાદી"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "પોસ્ટ્સ સંશોધક માટે ની યાદી"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીન પરની ફિલ્ટર લિંક્સ માટે સ્ક્રીન રીડર્સ દ્વારા ઉપયોગમાં લેવાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "વસ્તુઓની યાદી સંશોધક"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s ટૅગ્સ યાદી નેવિગેશન"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "તારીખ દ્વારા પોસ્ટ્સ ફિલ્ટર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીન પર તારીખ દ્વારા ફિલ્ટર માટે સ્ક્રીન રીડર્સ દ્વારા ઉપયોગમાં લેવાય "
"છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "તારીખ દ્વારા આઇટમ્સ ફિલ્ટર કરો"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "તારીખ દ્વારા %s ફિલ્ટર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "પોસ્ટની સૂચિ ને ફિલ્ટર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીન પરની ફિલ્ટર લિંક્સ માટે સ્ક્રીન રીડર્સ દ્વારા ઉપયોગમાં લેવાય છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "વસ્તુઓ ની યાદી ફિલ્ટર કરો"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "%s સૂચિને ફિલ્ટર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "મીડિયા મોડલમાં આ આઇટમ પર અપલોડ કરેલ તમામ મીડિયા દર્શાવે છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "આ પોસ્ટમાં અપલોડ કરવામાં આવ્યું છે"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "%s આ પોસ્ટમાં અપલોડ કરવામાં આવ્યું છે"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "પોસ્ટ માં સામેલ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "સામગ્રીમાં મીડિયા ઉમેરતી વખતે બટન લેબલ તરીકે."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "મીડિયા બટનમાં દાખલ કરો"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "%s માં દાખલ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "વૈશિષ્ટિકૃત છબી તરીકે ઉપયોગ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "વૈશિષ્ટિકૃત છબી તરીકે છબીનો ઉપયોગ કરવા માટે પસંદ કરવા માટેના બટન લેબલ તરીકે."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "વૈશિષ્ટિકૃત છબીનો ઉપયોગ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "વૈશિષ્ટિકૃત છબી દૂર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "ફીચર્ડ ઈમેજ દૂર કરતી વખતે બટન લેબલ તરીકે."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "વિશેષ ચિત્ર દૂર કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "ફીચર્ડ ચિત્ર સેટ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "ફીચર્ડ ઈમેજ સેટ કરતી વખતે બટન લેબલ તરીકે."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "ફીચર્ડ છબી સેટ કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "વૈશિષ્ટિકૃત છબી"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "ફીચર્ડ ઈમેજ મેટા બોક્સના શીર્ષક માટે ઉપયોગમાં લેવાતા એડિટરમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "ફીચર્ડ ઇમેજ મેટા બોક્સ"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "પોસ્ટ લક્ષણો"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "પોસ્ટ એટ્રીબ્યુટ્સ મેટા બોક્સના શીર્ષક માટે ઉપયોગમાં લેવાતા એડિટરમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "લક્ષણો મેટા બોક્સ"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s પોસ્ટ લક્ષણો"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "કાર્ય આર્કાઇવ્ઝ"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"આ લેબલ સાથે 'પોસ્ટ ટાઇપ આર્કાઇવ' આઇટમ્સને આર્કાઇવ્સ સક્ષમ સાથે CPTમાં અસ્તિત્વમાંના મેનૂમાં "
"આઇટમ ઉમેરતી વખતે બતાવવામાં આવેલી પોસ્ટ્સની સૂચિમાં ઉમેરે છે. જ્યારે 'લાઈવ પ્રીવ્યૂ' મોડમાં મેનુ "
"સંપાદિત કરવામાં આવે ત્યારે જ દેખાય છે અને કસ્ટમ આર્કાઈવ સ્લગ પ્રદાન કરવામાં આવે છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "આર્કાઇવ્સ નેવ મેનુ"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s આર્કાઇવ્સ"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "ટ્રેશમાં કોઈ પોસ્ટ્સ મળી નથી"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "જ્યારે ટ્રેશમાં કોઈ પોસ્ટ ન હોય ત્યારે પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "ટ્રેશમાં કોઈ આઇટમ્સ મળી નથી"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "ટ્રેશમાં કોઈ %s મળ્યું નથી"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "કોઈ પોસ્ટ મળી નથી"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"જ્યારે પ્રદર્શિત કરવા માટે કોઈ પોસ્ટ્સ ન હોય ત્યારે પોસ્ટ ટાઇપ સૂચિ સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "કોઈ આઇટમ્સ મળી નથી"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "કોઈ %s મળ્યું નથી"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "પોસ્ટ્સ શોધો"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "શબ્દ સંપાદિત કરતી વખતે સંપાદક સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "આઇટમ્સ શોધો"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "%s શોધો"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "પિતૃ પૃષ્ઠ:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "પોસ્ટ ટાઇપ લિસ્ટ સ્ક્રીનમાં અધિક્રમિક પ્રકારો માટે."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "પેરન્ટ %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "નવી પોસ્ટ"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "નવી આઇટમ"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "નવું %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "નવી પોસ્ટ ઉમેરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "શબ્દ સંપાદિત કરતી વખતે સંપાદક સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "નવી આઇટમ ઉમેરો"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "નવું %s ઉમેરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "પોસ્ટ્સ જુઓ"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"પેરેન્ટ આઇટમ 'બધી પોસ્ટ્સ' વ્યુમાં એડમિન બારમાં દેખાય છે, જો પોસ્ટ પ્રકાર આર્કાઇવ્સને સપોર્ટ "
"કરે છે અને હોમ પેજ તે પોસ્ટ પ્રકારનું આર્કાઇવ નથી."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "આઇટમ જુઓ"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "પોસ્ટ જુઓ"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "આઇટમમાં ફેરફાર કરતી વખતે તેને જોવા માટે એડમિન બારમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "આઇટમ જુઓ"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "%s જુઓ"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "પોસ્ટ સુધારો"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "આઇટમ સંપાદિત કરતી વખતે એડિટર સ્ક્રીનની ટોચ પર."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "આઇટમ સંપાદિત કરો"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "%s સંપાદિત કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "બધા પોસ્ટ્સ"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "એડમિન ડેશબોર્ડમાં પોસ્ટ ટાઇપ સબમેનુમાં."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "બધી વસ્તુઓ"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "બધા %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "પોસ્ટ પ્રકાર માટે એડમિન મેનુ નામ."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "મેનુ નુ નામ"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "એકવચન અને બહુવચન લેબલ્સનો ઉપયોગ કરીને બધા લેબલ્સ ફરીથી બનાવો"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "પુનઃસર્જન કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "સક્રિય પોસ્ટ પ્રકારો સક્ષમ અને વર્ડપ્રેસ સાથે નોંધાયેલ છે."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "સામગ્રી સંપાદકમાં વિવિધ સુવિધાઓને સક્ષમ કરો."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "પોસ્ટ ફોર્મેટ્સ"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "સંપાદક"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "ટ્રેકબેક્સ"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "પોસ્ટ પ્રકારની વસ્તુઓનું વર્ગીકરણ કરવા માટે વર્તમાન વર્ગીકરણ પસંદ કરો."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "ક્ષેત્રો બ્રાઉઝ કરો"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "આયાત કરવા માટે કંઈ નથી"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". કસ્ટમ પોસ્ટ પ્રકાર UI પ્લગઇન નિષ્ક્રિય કરી શકાય છે."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "કસ્ટમ પોસ્ટ પ્રકાર UI માંથી %d આઇટમ આયાત કરી -"
msgstr[1] "કસ્ટમ પોસ્ટ પ્રકાર UI માંથી %d આઇટમ આયાત કરી -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "વર્ગીકરણ આયાત કરવામાં નિષ્ફળ."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "પોસ્ટ પ્રકારો આયાત કરવામાં નિષ્ફળ."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "કસ્ટમ પોસ્ટ પ્રકાર UI પ્લગઇનમાંથી કંઈપણ આયાત માટે પસંદ કરેલ નથી."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "1 આઇટમ આયાત કરી"
msgstr[1] "%s આઇટમ આયાત કરી"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"પહેલાથી જ અસ્તિત્વમાં છે તે જ કી સાથે પોસ્ટ પ્રકાર અથવા વર્ગીકરણ આયાત કરવાથી વર્તમાન "
"પોસ્ટ પ્રકાર અથવા વર્ગીકરણની સેટિંગ્સ આયાતની સાથે ઓવરરાઈટ થઈ જશે."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "કસ્ટમ પોસ્ટ પ્રકાર UI માંથી આયાત કરો"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"નીચેના કોડનો ઉપયોગ પસંદ કરેલી વસ્તુઓના સ્થાનિક સંસ્કરણની નોંધણી કરવા માટે થઈ શકે છે. "
"સ્થાનિક રીતે ફીલ્ડ જૂથો, પોસ્ટ પ્રકારો અથવા વર્ગીકરણને સંગ્રહિત કરવાથી ઝડપી લોડ ટાઈમ, "
"વર્ઝન કંટ્રોલ અને ડાયનેમિક ફીલ્ડ્સ/સેટિંગ્સ જેવા ઘણા ફાયદા મળી શકે છે. ફક્ત નીચેના કોડને "
"તમારી થીમની functions.php ફાઇલમાં કોપી અને પેસ્ટ કરો અથવા તેને બાહ્ય ફાઇલમાં સમાવિષ્ટ "
"કરો, પછી ACF એડમિન તરફથી આઇટમ્સને નિષ્ક્રિય કરો અથવા કાઢી નાખો."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "નિકાસ - PHP જનરેટ કરો"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "નિકાસ કરો"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "વર્ગ"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "ટૅગ"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "વર્ગીકરણ સબમિટ કર્યું."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "વર્ગીકરણ સાચવ્યું."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "વર્ગીકરણ કાઢી નાખ્યું."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "વર્ગીકરણ અપડેટ કર્યું."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"આ વર્ગીકરણ રજીસ્ટર થઈ શક્યું નથી કારણ કે તેની કી અન્ય પ્લગઈન અથવા થીમ દ્વારા નોંધાયેલ "
"અન્ય વર્ગીકરણ દ્વારા ઉપયોગમાં લેવાય છે."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "વર્ગીકરણ સમન્વયિત."
msgstr[1] "%s વર્ગીકરણ સમન્વયિત."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "વર્ગીકરણ ડુપ્લિકેટ."
msgstr[1] "%s વર્ગીકરણ ડુપ્લિકેટ."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "વર્ગીકરણ નિષ્ક્રિય."
msgstr[1] "%s વર્ગીકરણ નિષ્ક્રિય."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "વર્ગીકરણ સક્રિય થયું."
msgstr[1] "%s વર્ગીકરણ સક્રિય."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "શરતો"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "પોસ્ટ પ્રકાર સમન્વયિત."
msgstr[1] "%s પોસ્ટ પ્રકારો સમન્વયિત."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "પોસ્ટ પ્રકાર ડુપ્લિકેટ."
msgstr[1] "%s પોસ્ટ પ્રકારો ડુપ્લિકેટ."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "પોસ્ટ પ્રકાર નિષ્ક્રિય."
msgstr[1] "%s પોસ્ટ પ્રકારો નિષ્ક્રિય કર્યા."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "પોસ્ટનો પ્રકાર સક્રિય કર્યો."
msgstr[1] "%s પોસ્ટ પ્રકારો સક્રિય થયા."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "પોસ્ટ પ્રકારો"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "સંવર્ધિત વિકલ્પો"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "મૂળભૂત સેટિંગ્સ"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"આ પોસ્ટ પ્રકાર રજીસ્ટર થઈ શક્યો નથી કારણ કે તેની કી અન્ય પ્લગઈન અથવા થીમ દ્વારા "
"નોંધાયેલ અન્ય પોસ્ટ પ્રકાર દ્વારા ઉપયોગમાં લેવાય છે."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "પૃષ્ઠો"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "હાલના ફીલ્ડ જૂથોને લિંક કરો"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s પોસ્ટ પ્રકાર બનાવ્યો"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "%s માં ફીલ્ડ્સ ઉમેરો"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s પોસ્ટ પ્રકાર અપડેટ કર્યો"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "પોસ્ટ પ્રકાર ડ્રાફ્ટ અપડેટ કર્યો."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "પોસ્ટ પ્રકાર માટે સુનિશ્ચિત થયેલ છે."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "પોસ્ટનો પ્રકાર સબમિટ કર્યો."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "પોસ્ટનો પ્રકાર સાચવ્યો."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "પોસ્ટ પ્રકાર અપડેટ કર્યો."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "પોસ્ટનો પ્રકાર કાઢી નાખ્યો."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "શોધવા માટે ટાઇપ કરો..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "માત્ર પ્રો"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "ક્ષેત્ર જૂથો સફળતાપૂર્વક લિંક થયા."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"કસ્ટમ પોસ્ટ પ્રકાર UI સાથે નોંધાયેલ પોસ્ટ પ્રકારો અને વર્ગીકરણ આયાત કરો અને ACF સાથે તેનું "
"સંચાલન કરો. <a href=\"%s\">પ્રારંભ કરો</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "વર્ગીકરણ"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "પોસ્ટ પ્રકાર"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "પૂર્ણ"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "ક્ષેત્ર જૂથો"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "પરવાનગીઓ"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "દૃશ્યતા"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "લેબલ્સ"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "ફીલ્ડ સેટિંગ્સ ટૅબ્સ"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[એસીએફ શોર્ટકોડ મૂલ્ય પૂર્વાવલોકન માટે અક્ષમ કર્યું]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "મોડલ બંધ કરો"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "ફિલ્ડ અન્ય જૂથમાં ખસેડવામાં આવ્યું"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "મોડલ બંધ કરો"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "આ ટૅબ પર ટૅબનું નવું જૂથ શરૂ કરો."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "સુધારાઓ"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "ફેરફારો સેવ કરો"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "ફિલ્ડ જૂથનું શીર્ષક"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "શીર્ષક ઉમેરો"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "ક્ષેત્ર જૂથ ઉમેરો"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr ""

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr ""

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "રજૂઆત"

#: includes/fields.php:383
msgid "Validation"
msgstr ""

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "સામાન્ય"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr ""

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "નિષ્ક્રિય"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "આ આઇટમ નિષ્ક્રિય કરો"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "સક્રિય કરો"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "આ આઇટમ સક્રિય કરો"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr ""

#: acf.php:504 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "નિષ્ક્રિય"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr ""

#: acf.php:562
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"અદ્યતન કસ્ટમ ફીલ્ડ્સ અને એડવાન્સ કસ્ટમ ફીલ્ડ્સ PRO એક જ સમયે સક્રિય ન હોવા જોઈએ. અમે "
"એડવાન્સ્ડ કસ્ટમ ફીલ્ડ્સ પ્રોને આપમેળે નિષ્ક્રિય કરી દીધું છે."

#: acf.php:560
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"અદ્યતન કસ્ટમ ફીલ્ડ્સ અને એડવાન્સ કસ્ટમ ફીલ્ડ્સ PRO એક જ સમયે સક્રિય ન હોવા જોઈએ. અમે "
"એડવાન્સ્ડ કસ્ટમ ફીલ્ડ્સને આપમેળે નિષ્ક્રિય કરી દીધા છે."

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - ACF શરૂ થાય તે પહેલાં અમે ACF ફીલ્ડ મૂલ્યો પુનઃપ્રાપ્ત કરવા માટે "
"એક અથવા વધુ કૉલ્સ શોધી કાઢ્યા છે. આ સમર્થિત નથી અને તે ખોટા અથવા ખોવાયેલા ડેટામાં "
"પરિણમી શકે છે. <a href=\"%2$s\" target=\"_blank\">આને કેવી રીતે ઠીક કરવું તે જાણો</"
"a>."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "અમાન્ય વિનંતી."

#: includes/fields/class-acf-field-select.php:635
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "REST API માં બતાવો"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "પારદર્શિતા સક્ષમ કરો"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "પ્રો પર અપગ્રેડ કરો"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "સક્રિય"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' ઈ - મેઈલ સરનામું માન્ય નથી."

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "રંગનું મુલ્ય"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "મુળભૂત રંગ પસંદ કરો"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "રંગ સાફ કરો"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "બ્લોક્સ"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "વિકલ્પો"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "વપરાશકર્તાઓ"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "મેનુ વસ્તુઓ"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "વિજેટો"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "જોડાણો"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "વર્ગીકરણ"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "પોસ્ટો"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "છેલ્લી અપડેટ: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "માફ કરશો, આ પોસ્ટ અલગ સરખામણી માટે અનુપલબ્ધ છે."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "અમાન્ય ક્ષેત્ર જૂથ પરિમાણ(ઓ)."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "સેવ પ્રતીક્ષામાં છે"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "સેવ થયેલ"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "આયાત"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "ફેરફારોની સમીક્ષા કરો"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "સ્થિત થયેલ છે: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "વિવિધ"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "સમન્વય ફેરફારો"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "તફાવત લોડ કરી રહ્યું છે"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "સ્થાનિક JSON ફેરફારોની સમીક્ષા કરો"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "વેબસાઇટની મુલાકાત લો"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "વિગતો જુઓ"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "આવૃત્તિ %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "માહિતી"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"અમે સમર્થનને લઈને કટ્ટરપંથી છીએ અને ઈચ્છીએ છીએ કે તમે ACF સાથે તમારી વેબસાઇટનો શ્રેષ્ઠ લાભ "
"મેળવો. જો તમને કોઈ મુશ્કેલી આવે, તો ત્યાં ઘણી જગ્યાએ મદદ મળી શકે છે:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "મદદ અને આધાર"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"જો તમને તમારી જાતને સહાયની જરૂર જણાય તો સંપર્કમાં રહેવા માટે કૃપા કરીને મદદ અને સમર્થન "
"ટેબનો ઉપયોગ કરો."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "અવલોકન"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr ""

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "અમાન્ય નૉન."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr ""

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr ""

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "વિજેટ"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "વપરાશકર્તાની ભૂમિકા"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "ટિપ્પણી"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "પોસ્ટ ફોર્મેટ"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "મેનુ વસ્તુ"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "પોસ્ટની સ્થિતિ"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "મેનુઓ"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "મેનુ ની જગ્યાઓ"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "મેનુ"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "પોસ્ટ્સ પેજ"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "પહેલું પાનું"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "પેજ પ્રકાર"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "લૉગ ઇન કર્યું"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "વર્તમાન વપરાશકર્તા"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "પેજ ટેમ્પલેટ"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "રજિસ્ટર"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "ઉમેરો / સંપાદિત કરો"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "વપરાશકર્તા ફોર્મ"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "પેજ પેરન્ટ"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "સુપર સંચાલક"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "વર્તમાન વપરાશકર્તા ભૂમિકા"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "મૂળભૂત ટેમ્પલેટ"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "પોસ્ટ ટેમ્પલેટ"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "પોસ્ટ કેટેગરી"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "બધા %s ફોર્મેટ"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "જોડાણ"

#: includes/validation.php:323
msgid "%s value is required"
msgstr ""

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr ""

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr ""

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "અને"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr ""

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"કૃપા કરીને તમામ પ્રીમિયમ એડ-ઓન્સ (%s) નવીનતમ સંસ્કરણ પર અપડેટ થયા છે તે પણ તપાસો."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr ""

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr ""

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "ગેલેરી"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr ""

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr ""

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "સ્ક્રીન પર છુપાવો"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "ટ્રેકબેકસ મોકલો"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "ટૅગ્સ"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "કેટેગરીઓ"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "પેજ લક્ષણો"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "ફોર્મેટ"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "લેખક"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "સ્લગ"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "પુનરાવર્તનો"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "ટિપ્પણીઓ"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "ચર્ચા"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "અવતરણ"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "પરમાલિંક"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr ""

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "પદ"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "સ્ટાઇલ"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "પ્રકાર"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "ચાવી"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "ઓર્ડર"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "પહોળાઈ"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr ""

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "જરૂરી?"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "ક્ષેત્ર પ્રકાર"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "ક્ષેત્રનું નામ"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "ફીલ્ડ લેબલ"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "કાઢી નાખો"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "ફિલ્ડ કાઢી નાખો"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "ખસેડો"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "ફાઇલ સંપાદિત કરો"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr ""

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "કોઈ અપડેટ ઉપલબ્ધ નથી."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "ડેટાબેઝ અપગ્રેડ પૂર્ણ. <a href=\"%s\">નવું શું છે તે જુઓ</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr ""

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr ""

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr ""

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr ""

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "કૃપા કરીને અપગ્રેડ કરવા માટે ઓછામાં ઓછી એક સાઇટ પસંદ કરો."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr ""

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "વેબસાઈટને %1$s થી %2$s સુધી ડેટાબેઝ સુધારાની જરૂર છે"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "સાઇટ"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "અપગ્રેડ સાઇટ્સ"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"નીચેની સાઇટ્સને DB સુધારાની જરૂર છે. તમે અદ્યતન બનાવા માંગો છો તે તપાસો અને પછી %s પર "
"ક્લિક કરો."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr ""

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "નિયમો"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "કૉપિ થઇ ગયું"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"તમે નિકાસ કરવા માંગો છો તે વસ્તુઓ પસંદ કરો અને પછી તમારી નિકાસ પદ્ધતિ પસંદ કરો. .json "
"ફાઇલમાં નિકાસ કરવા માટે JSON તરીકે નિકાસ કરો જે પછી તમે અન્ય ACF સ્થાપનમાં આયાત કરી "
"શકો છો. PHP કોડ પર નિકાસ કરવા માટે PHP ઉત્પન્ન કરો જેને તમે તમારી થીમમાં મૂકી શકો છો."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "સમન્વય"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "%s પસંદ કરો"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "ડુપ્લિકેટ"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr ""

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "માર્ગદર્શિકા"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "વર્ણન"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr ""

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "સક્રિય <span class=\"count\">(%s)</span>"
msgstr[1] "સક્રિય <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr ""

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "કસ્ટમ ફીલ્ડ"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr ""

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s ક્ષેત્ર હવે %2$s ક્ષેત્ર જૂથમાં મળી શકે છે"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "સક્રિય"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "સેટિંગ્સ"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "સ્થાન"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "શૂન્ય"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "નકલ"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(આ ક્ષેત્ર)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "ચકાસાયેલ"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "કસ્ટમ ફીલ્ડ ખસેડો"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "કોઈ ટૉગલ ફીલ્ડ ઉપલબ્ધ નથી"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "ક્ષેત્ર જૂથ શીર્ષક આવશ્યક છે"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "જ્યાં સુધી તેના ફેરફારો સાચવવામાં ન આવે ત્યાં સુધી આ ક્ષેત્ર ખસેડી શકાતું નથી"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "શબ્દમાળા \"field_\" નો ઉપયોગ ક્ષેત્રના નામની શરૂઆતમાં થઈ શકશે નહીં"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "ફીલ્ડ ગ્રુપ ડ્રાફ્ટ અપડેટ કર્યો."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "ક્ષેત્ર જૂથ માટે સુનિશ્ચિત થયેલ છે."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "ક્ષેત્ર જૂથ સબમિટ."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "ક્ષેત્ર જૂથ સાચવ્યું."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "ક્ષેત્ર જૂથ પ્રકાશિત."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "ફીલ્ડ જૂથ કાઢી નાખ્યું."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "ફીલ્ડ જૂથ અપડેટ કર્યું."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "સાધનો"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "ની સમાન નથી"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "ની બરાબર છે"

#: includes/locations.php:104
msgid "Forms"
msgstr "સ્વરૂપો"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "પેજ"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "પોસ્ટ"

#: includes/fields.php:328
msgid "Relational"
msgstr "સંબંધી"

#: includes/fields.php:327
msgid "Choice"
msgstr "પસંદગી"

#: includes/fields.php:325
msgid "Basic"
msgstr "પાયાની"

#: includes/fields.php:276
msgid "Unknown"
msgstr "અજ્ઞાત"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "ક્ષેત્ર પ્રકાર અસ્તિત્વમાં નથી"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr ""

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "પોસ્ટ અપડેટ થઇ ગઈ"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "સુધારો"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "ઇમેઇલ માન્ય કરો"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "લખાણ"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "શીર્ષક"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "પસંદગી કરતાં વધારે છે"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "કરતાં ઓછું મૂલ્ય છે"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "કરતાં વધુ મૂલ્ય છે"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "મૂલ્ય સમાવે છે"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "મૂલ્ય પેટર્ન સાથે મેળ ખાય છે"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "મૂલ્ય સમાન નથી"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "મૂલ્ય સમાન છે"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "કોઈ મૂલ્ય નથી"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "કોઈપણ મૂલ્ય ધરાવે છે"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "રદ"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "શું તમને ખાતરી છે?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr ""

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr ""

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr ""

#: includes/assets.php:367
msgid "Validation successful"
msgstr ""

#: includes/media.php:54
msgid "Restricted"
msgstr "પ્રતિબંધિત"

#: includes/media.php:53
msgid "Collapse Details"
msgstr ""

#: includes/media.php:52
msgid "Expand Details"
msgstr "વિગતો વિસ્તૃત કરો"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "આ પોસ્ટમાં અપલોડ કરવામાં આવ્યું છે"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "સુધારો"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "સંપાદિત કરો"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "જો તમે આ પૃષ્ઠ છોડીને જશો તો તમે કરેલા ફેરફારો ખોવાઈ જશે"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "ફાઇલનો પ્રકાર %s હોવો આવશ્યક છે."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "અથવા"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "ફાઇલનું કદ %s થી વધુ ન હોવું જોઈએ."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr ""

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "છબીની ઊંચાઈ %dpx કરતાં વધુ ન હોવી જોઈએ."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "છબીની ઊંચાઈ ઓછામાં ઓછી %dpx હોવી જોઈએ."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "છબીની પહોળાઈ %dpx થી વધુ ન હોવી જોઈએ."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "છબીની પહોળાઈ ઓછામાં ઓછી %dpx હોવી જોઈએ."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(કોઈ શીર્ષક નથી)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "પૂર્ણ કદ"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "મોટું"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "મધ્યમ"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "થંબનેલ"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(લેબલ નથી)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr ""

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "પંક્તિઓ"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "ટેક્સ્ટ વિસ્તાર"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "નવી પસંદગી ઉમેરો"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "આર્કાઇવ્સ"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "પૃષ્ઠ લિંક"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "ઉમેરો"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "નામ"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s ઉમેર્યું"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "વપરાશકર્તા નવા %s ઉમેરવામાં અસમર્થ છે"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "સંપાદન કરતી વખતે નવી શરતો બનાવવાની મંજૂરી આપો"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "ચેકબોક્સ"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "બહુવિધ મૂલ્યો"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "દેખાવ"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "ના %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr ""

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "મૂલ્ય %d ની બરાબર અથવા વધારે હોવું જોઈએ"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "મૂલ્ય સંખ્યા હોવી જોઈએ"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "સંખ્યા"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "ક્ષેત્રની પસંદગીમાં 'અન્ય' મૂલ્યો સાચવો"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr ""

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "અન્ય"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"પાછલા એકોર્ડિયનને રોકવા માટે અંતિમ બિંદુને વ્યાખ્યાયિત કરો. આ એકોર્ડિયન દેખાશે નહીં."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "ઓપન"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "એકોર્ડિયન"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr ""

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "ફાઈલ યુઆરએલ"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr ""

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "ફાઇલ ઉમેરો"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "કોઈ ફાઇલ પસંદ કરી નથી"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "ફાઇલનું નામ"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "ફાઇલ અપડેટ કરો"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "ફાઇલ સંપાદિત કરો\t"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "ફાઇલ પસંદ કરો"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "ફાઇલ"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "પાસવર્ડ"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr ""

#: includes/fields/class-acf-field-select.php:227 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "પસંદ કરો"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr ""

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "વધુ પરિણામો લોડ કરી રહ્યાં છીએ&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "તમે માત્ર %d વસ્તુઓ પસંદ કરી શકો છો"

#: includes/fields/class-acf-field-select.php:96
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "તમે માત્ર 1 આઇટમ પસંદ કરી શકો છો"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "કૃપા કરીને %d અક્ષરો કાઢી નાખો"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "કૃપા કરીને 1 અક્ષર કાઢી નાખો"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "કૃપા કરીને %d અથવા વધુ અક્ષરો દાખલ કરો"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "કૃપા કરીને 1 અથવા વધુ અક્ષરો દાખલ કરો"

#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "કોઈ બરાબરી મળી નથી"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:88
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d પરિણામો ઉપલબ્ધ છે, શોધખોળ કરવા માટે ઉપર અને નીચે એરો કીનો ઉપયોગ કરો."

#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "એક પરિણામ ઉપલબ્ધ છે, તેને પસંદ કરવા માટે એન્ટર દબાવો."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "પસંદ કરો"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "વપરાશકર્તા ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr ""

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr ""

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr ""

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr ""

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "વપરાશકર્તા"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "વિભાજક"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "રંગ પસંદ કરો"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "મૂળભૂત"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "સાફ કરો"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "રંગ પીકર"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "પી એમ(PM)"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "પસંદ કરો"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "પૂર્ણ"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "હવે"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "સમય ઝોન"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "માઇક્રોસેકન્ડ"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "મિલીસેકન્ડ"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "સેકન્ડ"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "મિનિટ"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "કલાક"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "સમય"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "સમય પસંદ કરો"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr ""

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "પ્લેસમેન્ટ"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "ટેબ"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "મૂલ્ય એક માન્ય URL હોવું આવશ્યક છે"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "લિંક યુઆરએલ"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr ""

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "લિંક પસંદ કરો"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "લિંક"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "ઇમેઇલ"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr ""

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "મહત્તમ મૂલ્ય"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "ન્યૂનતમ મૂલ્ય"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "શ્રેણી"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr ""

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "લેબલ"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "મૂલ્ય"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "ઊભું"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "આડું"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr "વધુ નિયંત્રણ માટે, તમે આના જેવું મૂલ્ય અને નામપટ્ટી બંનેનો ઉલ્લેખ કરી શકો છો:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "દરેક પસંદગીને નવી લાઇન પર દાખલ કરો."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "પસંદગીઓ"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "પેરેન્ટ"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "જ્યાં સુધી ફીલ્ડ ક્લિક ન થાય ત્યાં સુધી TinyMCE પ્રારંભ કરવામાં આવશે નહીં"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "ટૂલબાર"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "ફક્ત ટેક્સ્ટ"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "ટૅબ્સ"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "લખાણ"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "દ્રશ્ય"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "મૂલ્ય %d અક્ષરોથી વધુ ન હોવું જોઈએ"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr ""

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr ""

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr ""

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr ""

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr ""

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr ""

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr ""

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "નવી પોસ્ટ બનાવતી વખતે દેખાય છે"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "લખાણ"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s ને ઓછામાં ઓછા %2$s પસંદગીની જરૂર છે"
msgstr[1] "%1$s ને ઓછામાં ઓછી %2$s પસંદગીની જરૂર છે"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "પોસ્ટ આઈડી"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "ફીચર્ડ છબી"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "તત્વો"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "વર્ગીકરણ"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "પોસ્ટ પ્રકાર"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "ફિલ્ટર્સ"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "બધા વર્ગીકરણ"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "શોધો"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "કોઈ બરાબરી મળી નથી"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "લોડ કરી રહ્યું છે"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "મહત્તમ મૂલ્યો પહોંચી ગયા ( {max} મૂલ્યો )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "સંબંધ"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "અલ્પવિરામથી વિભાજિત સૂચિ. તમામ પ્રકારના માટે ખાલી છોડો"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr ""

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "મહત્તમ"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "ફાઈલ સાઇઝ઼:"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "કઈ છબીઓ અપલોડ કરી શકાય તે પ્રતિબંધિત કરો"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "ન્યૂનતમ"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "પોસ્ટમાં અપલોડ કરવામાં આવ્યું છે"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "બધા"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "મીડિયા લાઇબ્રેરીની પસંદગીને મર્યાદિત કરો"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "લાઇબ્રેરી"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "પૂર્વાવલોકન કદ"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr ""

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "ચિત્ર યુઆરએલ"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "અગ્રભાગ પર પરત કરેલ મૂલ્યનો ઉલ્લેખ કરો"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "વળતર મૂલ્ય"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "ચિત્ર ઉમેરો"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "કોઇ ચિત્ર પસંદ નથી કયુઁ"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "દૂર કરો"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "સંપાદિત કરો"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "બધી છબીઓ"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "છબી અપડેટ કરો"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "છબી સંપાદિત કરો"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "છબી પસંદ કરો"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "છબી"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "HTML માર્કઅપને અનુવાદ બદલે દૃશ્યમાન લખાણ તરીકે પ્રદર્શિત કરવાની મંજૂરી આપો"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr ""

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr ""

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr ""

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr ""

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "નવી રેખાઓ કેવી રીતે રેન્ડર કરવામાં આવે છે તેનું નિયંત્રણ કરે છે"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "નવી રેખાઓ"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "મૂલ્ય સાચવતી વખતે વપરાતી ગોઠવણ"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "પૂર્વ"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "આગળ"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "આજે"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "પૂર્ણ"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "તારીખ પીકર"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "પહોળાઈ"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "યુઆરએલ દાખલ કરો"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "જ્યારે નિષ્ક્રિય હોય ત્યારે લખાણ બતાવવામાં આવે છે"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr ""

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "મૂળભૂત મૂલ્ય"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "સંદેશ"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
msgid "No"
msgstr "ના"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
msgid "Yes"
msgstr "હા"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "સાચું / ખોટું"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "પંક્તિ"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "ટેબલ"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "બ્લોક"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "લેઆઉટ"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "જૂથ"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "ઊંચાઈ"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "ઝૂમ"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "મધ્ય"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "વર્તમાન સ્થાન શોધો"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "સ્થાન સાફ કરો"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "શોધો"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "ગૂગલે નકશો"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "કસ્ટમ:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "તારીખ પીકર"

#. translators: counts for inactive field groups
#: acf.php:510
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "નિષ્ક્રિય <span class=\"count\">(%s)</span>"
msgstr[1] "નિષ્ક્રિય <span class=\"count\">(%s)</span>"

#: acf.php:471
msgid "No Fields found in Trash"
msgstr "ટ્રેશમાં કોઈ ફીલ્ડ મળ્યાં નથી"

#: acf.php:470
msgid "No Fields found"
msgstr "કોઈ ક્ષેત્રો મળ્યાં નથી"

#: acf.php:469
msgid "Search Fields"
msgstr "શોધ ક્ષેત્રો"

#: acf.php:468
msgid "View Field"
msgstr "ક્ષેત્ર જુઓ"

#: acf.php:467 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "નવી ફીલ્ડ"

#: acf.php:466
msgid "Edit Field"
msgstr "ફીલ્ડ સંપાદિત કરો"

#: acf.php:465
msgid "Add New Field"
msgstr "નવી ફીલ્ડ ઉમેરો"

#: acf.php:463
msgid "Field"
msgstr "ફિલ્ડ"

#: acf.php:462 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "ક્ષેત્રો"

#: acf.php:437
msgid "No Field Groups found in Trash"
msgstr "ટ્રેશમાં કોઈ ફીલ્ડ જૂથો મળ્યાં નથી"

#: acf.php:436
msgid "No Field Groups found"
msgstr "કોઈ ક્ષેત્ર જૂથો મળ્યાં નથી"

#: acf.php:435
msgid "Search Field Groups"
msgstr "ક્ષેત્ર જૂથો શોધો"

#: acf.php:434
msgid "View Field Group"
msgstr "ક્ષેત્ર જૂથ જુઓ"

#: acf.php:433
msgid "New Field Group"
msgstr "નવું ક્ષેત્ર જૂથ"

#: acf.php:432
msgid "Edit Field Group"
msgstr "ક્ષેત્ર જૂથ સંપાદિત કરો"

#: acf.php:431
msgid "Add New Field Group"
msgstr "નવું ક્ષેત્ર જૂથ ઉમેરો"

#: acf.php:430 acf.php:464
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "નવું ઉમેરો"

#: acf.php:429
msgid "Field Group"
msgstr "ક્ષેત્ર જૂથ"

#: acf.php:428 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "ક્ષેત્ર જૂથો"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "શક્તિશાળી, વ્યાવસાયિક અને સાહજિક ક્ષેત્રો સાથે વર્ડપ્રેસ ને કસ્ટમાઇઝ કરો."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "અદ્યતન કસ્ટમ ક્ષેત્રો"
