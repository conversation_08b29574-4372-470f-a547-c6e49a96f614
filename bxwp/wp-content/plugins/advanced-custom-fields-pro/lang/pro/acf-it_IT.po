msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2023-04-18 07:25+0000\n"
"PO-Revision-Date: 2023-04-24 13:30+0100\n"
"Last-Translator: WP Engine <<EMAIL>>\n"
"Language-Team: WP Engine <<EMAIL>>\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"
"X-Loco-Target-Locale: it_IT\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:47
msgid "Options"
msgstr "Opzioni"

#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:527
msgid "Update"
msgstr "Aggiorna"

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Opzioni Aggiornate"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "Ricontrollare"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Pubblica"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nessun Field Group personalizzato trovato in questa Pagina Opzioni. <a "
"href=\"%s\">Crea un Field Group personalizzato</a>"

#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Modifica Field Group"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Errore</b>.Impossibile connettersi al server di aggiornamento"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Aggiornamenti"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clona"

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:818,
#: pro/fields/class-acf-field-flexible-content.php:78
msgid "Fields"
msgstr "Campi"

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Selezionare uno o più campi che si desidera clonare"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Visualizza"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Specificare lo stile utilizzato per il rendering del campo clona"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppo (Visualizza campi selezionati in un gruppo all'interno di questo "
"campo)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Senza interruzione (sostituisce questo campo con i campi selezionati)"

#: pro/fields/class-acf-field-clone.php:854,
#: pro/fields/class-acf-field-flexible-content.php:558,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:177
msgid "Layout"
msgstr "Layout"

#: pro/fields/class-acf-field-clone.php:855
msgid "Specify the style used to render the selected fields"
msgstr "Specificare lo stile utilizzato per il rendering dei campi selezionati"

#: pro/fields/class-acf-field-clone.php:860,
#: pro/fields/class-acf-field-flexible-content.php:629,
#: pro/fields/class-acf-field-repeater.php:185,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Blocco"

#: pro/fields/class-acf-field-clone.php:861,
#: pro/fields/class-acf-field-flexible-content.php:628,
#: pro/fields/class-acf-field-repeater.php:184
msgid "Table"
msgstr "Tabella"

#: pro/fields/class-acf-field-clone.php:862,
#: pro/fields/class-acf-field-flexible-content.php:630,
#: pro/fields/class-acf-field-repeater.php:186
msgid "Row"
msgstr "Riga"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Etichette verranno visualizzate come %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefisso Etichetta Campo"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "I valori verranno salvati come %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefisso Nomi Campo"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Campo sconosciuto"

#: pro/fields/class-acf-field-clone.php:1009
msgid "(no title)"
msgstr "(nessun titolo)"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Field Group sconosciuto"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tutti i campi dal %s field group"

#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contenuto Flessibile"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Aggiungi Riga"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layout"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Questo campo richiede almeno {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponibile (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} richiesto (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexible Content richiede almeno 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clicca il bottone \"%s\" qui sotto per iniziare a creare il layout"

#: pro/fields/class-acf-field-flexible-content.php:420,
#: pro/fields/class-acf-repeater-table.php:366
msgid "Drag to reorder"
msgstr "Trascinare per riordinare"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Rimuovi Layout"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Clicca per alternare"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Cancella Layout"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplica Layout"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Aggiungi Nuovo Layout"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:593
msgid "Label"
msgstr "Etichetta"

#: pro/fields/class-acf-field-flexible-content.php:609
msgid "Name"
msgstr "Nome"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Layout Minimi"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Layout Massimi"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Etichetta Bottone"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galleria"

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Aggiungi Immagine alla Galleria"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Selezione massima raggiunta"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Lunghezza"

#: pro/fields/class-acf-field-gallery.php:339
msgid "Edit"
msgstr "Modifica"

#: pro/fields/class-acf-field-gallery.php:340,
#: pro/fields/class-acf-field-gallery.php:495
msgid "Remove"
msgstr "Rimuovi"

#: pro/fields/class-acf-field-gallery.php:356
msgid "Title"
msgstr "Titolo"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Didascalia"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Testo Alt"

#: pro/fields/class-acf-field-gallery.php:392
msgid "Description"
msgstr "Descrizione"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Aggiungi a Galleria"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Azioni in blocco"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ordina per aggiornamento data"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ordina per data modifica"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ordina per titolo"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Ordine corrente inversa"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Chiudi"

#: pro/fields/class-acf-field-gallery.php:556
msgid "Return Format"
msgstr "Formato di ritorno"

#: pro/fields/class-acf-field-gallery.php:562
msgid "Image Array"
msgstr "Array Immagine"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Image URL"
msgstr "URL Immagine"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Image ID"
msgstr "ID Immagine"

#: pro/fields/class-acf-field-gallery.php:572
msgid "Library"
msgstr "Libreria"

#: pro/fields/class-acf-field-gallery.php:573
msgid "Limit the media library choice"
msgstr "Limitare la scelta alla libreria multimediale"

#: pro/fields/class-acf-field-gallery.php:578,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Tutti"

#: pro/fields/class-acf-field-gallery.php:579
msgid "Uploaded to post"
msgstr "Caricato al post"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Seleziona Minima"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Seleziona Massima"

#: pro/fields/class-acf-field-gallery.php:635
msgid "Minimum"
msgstr "Minimo"

#: pro/fields/class-acf-field-gallery.php:636,
#: pro/fields/class-acf-field-gallery.php:672
msgid "Restrict which images can be uploaded"
msgstr "Limita i tipi di immagine che possono essere caricati"

#: pro/fields/class-acf-field-gallery.php:639,
#: pro/fields/class-acf-field-gallery.php:675
msgid "Width"
msgstr "Larghezza"

#: pro/fields/class-acf-field-gallery.php:650,
#: pro/fields/class-acf-field-gallery.php:686
msgid "Height"
msgstr "Altezza"

#: pro/fields/class-acf-field-gallery.php:662,
#: pro/fields/class-acf-field-gallery.php:698
msgid "File size"
msgstr "Dimensione File"

#: pro/fields/class-acf-field-gallery.php:671
msgid "Maximum"
msgstr "Massimo"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Tipologie File permesse"

#: pro/fields/class-acf-field-gallery.php:708
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separata da virgole. Lascia bianco per tutti i tipi"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Inserisci"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Specificare dove vengono aggiunti nuovi allegati"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Aggiungere alla fine"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Anteporre all'inizio"

#: pro/fields/class-acf-field-gallery.php:741
msgid "Preview Size"
msgstr "Dimensione Anteprima"

#: pro/fields/class-acf-field-gallery.php:844
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-repeater.php:29
msgid "Repeater"
msgstr "Ripetitore"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Righe minime raggiunte ({min} righe)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Righe massime raggiunte ({max} righe)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:162
msgid "Sub Fields"
msgstr "Campi Sub"

#: pro/fields/class-acf-field-repeater.php:195
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Righe Minime"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Righe Massime"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Collassata"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Selezionare un campo secondario da visualizzare quando la riga è collassata"

#: pro/fields/class-acf-field-repeater.php:1045
msgid "Invalid nonce."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Trascinare per riordinare"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Aggiungi riga"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Rimuovi riga"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Pagina Principale"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Pagina Post"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Pagina Principale"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Pagina Post"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr "Pagina Opzioni"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Nessuna Pagina Opzioni esistente"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Disattivare Licenza"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Attiva Licenza"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informazioni Licenza"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per sbloccare gli aggiornamenti, si prega di inserire la chiave di licenza "
"qui sotto. Se non hai una chiave di licenza, si prega di vedere <a "
"href=\"%s\" target=\"_blank\">Dettagli e prezzi</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Chiave di licenza"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informazioni di aggiornamento"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versione corrente"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Ultima versione"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aggiornamento Disponibile"

#: pro/admin/views/html-settings-updates.php:91
msgid "No"
msgstr "No"

#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Si"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Avviso di Aggiornamento"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Inserisci il tuo codice di licenza per sbloccare gli aggiornamenti"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Aggiorna Plugin"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
