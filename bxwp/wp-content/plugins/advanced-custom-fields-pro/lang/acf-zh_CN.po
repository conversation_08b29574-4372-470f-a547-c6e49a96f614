# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-04-04T12:09:00+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: includes/class-acf-site-health.php:291
msgid "Update Source"
msgstr "更新来源"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "默认情况下，只有管理员用户可以编辑此设置。"

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "默认情况下，只有超级管理员用户才能编辑此设置。"

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "关闭并添加字段"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"用于处理分类法中元框内容的 PHP 函数名称。为了安全起见，该回调将在一个特殊的上"
"下文中执行，不能访问任何超级全局变量，如 $_POST 或 $_GET。"

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"当设置编辑屏幕的元框时，将调用一个 PHP 函数名称。为了安全起见，该回调将在一个"
"特殊的上下文中执行，不能访问任何超级全局变量，如 $_POST 或 $_GET。"

#: includes/class-acf-site-health.php:292
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "允许访问编辑器用户界面中的值"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "了解更多信息。"

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr "允许内容编辑器访问和 显示编辑器 UI 中使用块绑定或 ACF 简码的字段值。%s"

#: includes/Blocks/Bindings.php:64
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr "请求的 ACF 字段类型不支持块绑定或 ACF 简码中的输出。"

#: includes/api/api-template.php:1085 includes/Blocks/Bindings.php:72
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr "所请求的 ACF 字段不允许在绑定或 ACF 简码中输出。"

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr "请求的 ACF 字段类型不支持在绑定或 ACF 简码中输出。"

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[ACF 简码无法显示非公开文章中的字段］"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[本网站已禁用 ACF 简码］"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr "商人图标"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr "论坛图标"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr "YouTube 图标"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr "Yes （alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr "Xing 图标"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr "WordPress（备选）图标"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr "WhatsApp 图标"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr "写博客图标"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr "小工具菜单图标"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr "查看网站图标"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr "了解更多信息图标"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr "添加页面图标"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr "视频（alt3）图标"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr "视频（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr "视频（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr "更新图标"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr "通用访问（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr "推特（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr "Twitch 图标"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr "潮汐图标"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr "门票图标"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr "文本页面图标"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr "表格行删除图标"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr "表格行之前图标"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr "表格行后图标"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr "删除表格列图标"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr "表列图标前"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr "表列之后的图标"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr "超级英雄（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr "超级英雄图标"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr "Spotify 图标"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr "简码图标"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr "盾牌（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr "共享（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr "共享（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr "保存图标"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr "RSS 图标"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr "REST API 图标"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr "删除图标"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr "Reddit 图标"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr "隐私图标"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr "打印机图标"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr "跑道图标"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr "加号（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr "加号（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr "已检查插件图标"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr "Pinterest 图标"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr "宠物图标"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr "PDF 图标"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr "棕榈树图标"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr "打开文件夹图标"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr "无（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr "金钱（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr "菜单（alt3）图标"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr "菜单（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr "菜单（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr "电子表格图标"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr "互动图标"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr "文档图标"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr "默认图标"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr "位置（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr "LinkedIn 图标"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr "Instagram 图标"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr "插入前图标"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr "插入后图标"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr "插入图标"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr "信息轮廓图标"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr "图像（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr "图像（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr "向右旋转图标"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr "向左旋转图标"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr "旋转图标"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr "垂直翻转图标"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr "水平翻转图标"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr "裁剪图标"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "ID (alt) Icon"
msgstr "ID（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr "HTML 图标"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr "沙漏图标"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr "标题图标"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr "谷歌图标"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr "游戏图标"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr "退出全屏（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr "全屏（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr "状态图标"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr "图像图标"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr "图库图标"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr "聊天图标"

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr "音频图标"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr "其他图标"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr "食物图标"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr "退出图标"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr "摘录视图图标"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr "嵌入视频图标"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr "嵌入文章图标"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr "嵌入照片图标"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr "嵌入通用图标"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr "嵌入音频图标"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr "电子邮件（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr "省略图标"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr "无序列表图标"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr "RTL 图标"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr "有序列表 RTL 图标"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr "有序列表图标"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr "LTR 图标"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr "自定义字符图标"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr "编辑页面图标"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr "编辑大图标"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr "鼓槌图标"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr "数据库查看图标"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr "删除数据库图标"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr "数据库导入图标"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr "数据库导出图标"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr "数据库添加图标"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr "数据库图标"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr "封面图像图标"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr "音量打开图标"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr "关闭音量图标"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr "向前跳转图标"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr "后跳图标"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr "重复图标"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr "播放图标"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr "暂停图标"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr "前进图标"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr "后退图标"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr "列图标"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr "颜色选择器图标"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr "咖啡图标"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr "代码标准图标"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr "云上传图标"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr "云保存图标"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr "汽车图标"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr "相机（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr "计算器图标"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr "按钮图标"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr "商人图标"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr "跟踪图标"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr "主题图标"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr "回复图标"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "PM Icon"
msgstr "PM 图标"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr "好友图标"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr "社区图标"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr "BuddyPress 图标"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "bbPress Icon"
msgstr "bbPress 图标"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr "活动图标"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr "图书图标"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr "块默认图标"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr "贝尔图标"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr "啤酒图标"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr "银行图标"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr "向上箭头（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr "向上箭头（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr "向右箭头（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr "向右箭头（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr "向左箭头（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr "箭头左（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr "向下箭头（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr "箭头向下（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr "亚马逊图标"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr "宽对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr "右拉对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr "左拉对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr "全宽对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr "飞机图标"

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr "网站（alt3）图标"

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr "网站（alt2）图标"

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr "网站（alt）图标"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr "升级至 ACF PRO，只需点击几下即可创建选项页面"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "无效请求参数。"

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "很抱歉，您没有权限来这样做。"

#: includes/class-acf-site-health.php:648
msgid "Blocks Using Post Meta"
msgstr "使用文章元数据的块"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO logo"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO Logo"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr "当类型设置为 media_library 时，%s 需要一个有效的附件 ID。"

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr "%s 是 acf 的必备属性。"

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr "图标的值改为保存。"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr "图标的类型改为保存。"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr "Yes 图标"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr "WordPress 图标"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr "警告图标"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr "可见性图标"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr "保险库图标"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr "上传图标"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr "更新图标"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr "解锁图标"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr "通用访问图标"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr "撤销图标"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr "推特图标"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr "回收站图标"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr "翻译图标"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr "门票图标"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr "大拇指向上图标"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr "大拇指向下图标"

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr "文本图标"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr "推荐图标"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr "标签云图标"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr "标签图标"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr "平板图标"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr "商店图标"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr "粘贴图标"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr "半星图标"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr "星形填充图标"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr "星空图标"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr "Sos 图标"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr "排序图标"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr "笑脸图标"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr "智能手机图标"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr "幻灯片图标"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr "盾牌图标"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr "共享图标"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr "搜索图标"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr "屏幕选项图标"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr "日程图标"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr "重做图标"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr "随机化图标"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr "产品图标"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr "发布图标"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr "文章状态图标"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr "组合图标"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr "加号图标"

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr "播放列表视频图标"

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr "播放列表音频图标"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr "电话图标"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr "表演图标"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr "回形针图标"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr "无图标"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr "联网图标"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr "名签图标"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr "移动图标"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr "金钱图标"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr "减号图标"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr "迁移图标"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr "麦克风图标"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr "扩音器图标"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr "标记图标"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr "锁定图标"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr "位置图标"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr "列表视图图标"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr "灯泡图标"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr "左右图标"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr "布局图标"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr "笔记本电脑图标"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr "信息图标"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr "索引卡图标"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "ID Icon"
msgstr "ID 图标"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr "隐藏图标"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr "心形图标"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr "锤子图标"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr "群组图标"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr "网格视图图标"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr "表格图标"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr "旗帜图标"

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr "筛选图标"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr "反馈图标"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr "Facebook（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr "Facebook 图标"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr "外部图标"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr "电子邮件（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr "电子邮件图标"

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr "视频图标"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr "取消链接图标"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr "下划线图标"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr "文本颜色图标"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr "表格图标"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr "删除线图标"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr "拼写检查图标"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr "移除格式图标"

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr "引用图标"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr "粘贴文字图标"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr "粘贴文本图标"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr "段落图标"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr "缩进图标"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr "厨房水槽图标"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr "对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr "斜体图标"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr "插入更多图标"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr "缩进图标"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr "帮助图标"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr "展开图标"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr "合同图标"

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr "代码图标"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr "中断图标"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr "粗体图标"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr "编辑图标"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr "下载图标"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr "取消图标"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr "桌面图标"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr "仪表板图标"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr "云图标"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr "时钟图标"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr "剪贴板图标"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr "图表饼图标"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr "图表线条图标"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr "图表条形图标"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr "图表区域图标"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr "分类图标"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr "购物车图标"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr "胡萝卜图标"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr "相机图标"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr "日历（alt）图标"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr "日历图标"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr "商务人士图标"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr "建筑图标"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr "图书图标"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr "备份图标"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr "奖项图标"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr "艺术图标"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr "向上箭头图标"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr "向右箭头图标"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr "箭头左图标"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr "向下箭头图标"

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr "存档图标"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr "分析图标"

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr "右对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr "无对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr "向左对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr "居中对齐图标"

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr "相册图标"

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr "用户图标"

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr "工具图标"

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr "网站图标"

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr "设置图标"

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr "文章图标"

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr "插件图标"

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr "页面图标"

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr "网络图标"

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr "多站点图标"

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr "媒体图标"

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr "链接图标"

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr "主页图标"

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr "自定义图标"

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr "评论图标"

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr "折叠图标"

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr "外观图标"

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr "通用图标"

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr "图标拾取器需要一个值。"

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr "图标拾取器需要一个图标类型。"

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr "符合您搜索条件的可用图标已在下面的图标拾取器中更新。"

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr "未找到该搜索词的结果"

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr "数组"

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr "字符串"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr "指定图标的返回格式。%s"

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr "选择内容编辑器可以从哪里选择图标。"

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "您想使用的图标的 URL 或 svg 作为数据 URI"

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr "浏览媒体库"

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr "当前选择图片预览"

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr "单击以更改媒体库中的图标"

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr "搜索图标..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "媒体库"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "大水印"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"选择一个图标的交互式用户界面。从 Dash 图标、媒体库或独立的 URL 输入中进行选"
"择。"

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "图标选择器"

#: includes/class-acf-site-health.php:709
msgid "JSON Load Paths"
msgstr "JSON 加载路径"

#: includes/class-acf-site-health.php:703
msgid "JSON Save Paths"
msgstr "JSON 保存路径"

#: includes/class-acf-site-health.php:694
msgid "Registered ACF Forms"
msgstr "已注册的 ACF 表单"

#: includes/class-acf-site-health.php:688
msgid "Shortcode Enabled"
msgstr "已启用简码"

#: includes/class-acf-site-health.php:680
msgid "Field Settings Tabs Enabled"
msgstr "已启用字段设置选项卡"

#: includes/class-acf-site-health.php:672
msgid "Field Type Modal Enabled"
msgstr "已启用字段类型模式"

#: includes/class-acf-site-health.php:664
msgid "Admin UI Enabled"
msgstr "已启用管理用户界面"

#: includes/class-acf-site-health.php:655
msgid "Block Preloading Enabled"
msgstr "启用块预载"

#: includes/class-acf-site-health.php:643
msgid "Blocks Per ACF Block Version"
msgstr "每个 ACF 块版本的块"

#: includes/class-acf-site-health.php:638
msgid "Blocks Per API Version"
msgstr "每个 API 版本的区块"

#: includes/class-acf-site-health.php:611
msgid "Registered ACF Blocks"
msgstr "已注册的 ACF 块"

#: includes/class-acf-site-health.php:605
msgid "Light"
msgstr "轻型"

#: includes/class-acf-site-health.php:605
msgid "Standard"
msgstr "标准"

#: includes/class-acf-site-health.php:604
msgid "REST API Format"
msgstr "REST API 格式"

#: includes/class-acf-site-health.php:596
msgid "Registered Options Pages (PHP)"
msgstr "注册选项页（PHP）"

#: includes/class-acf-site-health.php:582
msgid "Registered Options Pages (JSON)"
msgstr "注册选项页面（JSON）"

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (UI)"
msgstr "注册选项页面（用户界面）"

#: includes/class-acf-site-health.php:547
msgid "Options Pages UI Enabled"
msgstr "已启用用户界面的选项页"

#: includes/class-acf-site-health.php:539
msgid "Registered Taxonomies (JSON)"
msgstr "注册分类法（JSON）"

#: includes/class-acf-site-health.php:527
msgid "Registered Taxonomies (UI)"
msgstr "注册分类法（用户界面）"

#: includes/class-acf-site-health.php:515
msgid "Registered Post Types (JSON)"
msgstr "已注册文章类型（JSON）"

#: includes/class-acf-site-health.php:503
msgid "Registered Post Types (UI)"
msgstr "已注册文章类型（UI）"

#: includes/class-acf-site-health.php:490
msgid "Post Types and Taxonomies Enabled"
msgstr "已启用的文章类型和分类法"

#: includes/class-acf-site-health.php:483
msgid "Number of Third Party Fields by Field Type"
msgstr "按字段类型划分的第三方字段数量"

#: includes/class-acf-site-health.php:478
msgid "Number of Fields by Field Type"
msgstr "按字段类型划分的字段数量"

#: includes/class-acf-site-health.php:445
msgid "Field Groups Enabled for GraphQL"
msgstr "为 GraphQL 启用的字段组"

#: includes/class-acf-site-health.php:432
msgid "Field Groups Enabled for REST API"
msgstr "为 REST API 启用的字段组"

#: includes/class-acf-site-health.php:420
msgid "Registered Field Groups (JSON)"
msgstr "注册字段组（JSON）"

#: includes/class-acf-site-health.php:408
msgid "Registered Field Groups (PHP)"
msgstr "注册字段组（PHP）"

#: includes/class-acf-site-health.php:396
msgid "Registered Field Groups (UI)"
msgstr "已注册字段组（UI）"

#: includes/class-acf-site-health.php:384
msgid "Active Plugins"
msgstr "活动插件"

#: includes/class-acf-site-health.php:358
msgid "Parent Theme"
msgstr "父主题"

#: includes/class-acf-site-health.php:347
msgid "Active Theme"
msgstr "活动主题"

#: includes/class-acf-site-health.php:338
msgid "Is Multisite"
msgstr "是否多站点"

#: includes/class-acf-site-health.php:333
msgid "MySQL Version"
msgstr "MySQL 版本"

#: includes/class-acf-site-health.php:328
msgid "WordPress Version"
msgstr "WordPress 版本"

#: includes/class-acf-site-health.php:321
msgid "Subscription Expiry Date"
msgstr "订阅到期日期"

#: includes/class-acf-site-health.php:313
msgid "License Status"
msgstr "许可证状态"

#: includes/class-acf-site-health.php:308
msgid "License Type"
msgstr "许可证类型"

#: includes/class-acf-site-health.php:303
msgid "Licensed URL"
msgstr "授权 URL"

#: includes/class-acf-site-health.php:297
msgid "License Activated"
msgstr "许可证已激活"

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr "免费"

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr "插件类型"

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr "插件版本"

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr "本节包含有关 ACF 配置的调试信息，这些信息可以提供给技术支持。"

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr "在您保存之前，需要注意此页面上的一个 ACF 字段。"

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"当我们检测到输出过程中已更改的值时，会记录此数据。%1$s 清除日志并在代码中转义"
"值后关闭 %2$s。如果我们再次检测到已更改的值，通知将重新出现。"

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "永久关闭"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "内容编辑器的说明。提交数据时显示。"

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "未选择任何术语"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "是否选择了任何术语"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "术语不包含"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "术语包含"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "术语不等于"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "术语等于"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "没有用户选择"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "有任何用户选择"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "用户不包含"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "用户包含"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "用户不等于"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "用户等于"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "未选择页面"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "有任何页面选择"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "页面不包含"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "页面包含"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "页面不等于"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "页面等于"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "没有关系选择"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "有任何关系选择"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "没有选择文章"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "有任何文章选择"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "文章不包含"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "文章包含"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "文章不等于"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "文章等于"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "关系不包含"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "关系包含"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "关系不等于"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "关系等于"

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF 字段"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO 功能"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "续订 PRO 即可解锁"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "更新 PRO 许可证"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "如果没有有效许可证，则无法编辑 PRO 字段。"

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr "请激活您的 ACF PRO 许可证以编辑分配给 ACF 块的字段组。"

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "请激活您的 ACF PRO 许可证才能编辑此选项页面。"

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"只有当 format_value 也为 true 时，才能返回转义 HTML 值。为安全起见，字段值不"
"会返回。"

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"只有当 format_value 也为 true 时，才能返回转义 HTML 值。为安全起见，未返回字"
"段值。"

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"当通过 <code>the_field</code> 或 ACF 简码进行渲染时，%1$s ACF 现在会自动转义"
"不安全的 HTML。我们检测到您的某些字段的输出已被此更改修改，但这可能不是一个破"
"坏性的更改。%2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "请联系您的网站管理员或开发者了解详情。"

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "了解更多信息"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "隐藏详细信息"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "显示详细信息"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - 渲染通过 %3$s"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "续订 ACF PRO 许可证"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "更新许可证"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "管理许可证"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "区块编辑器不支持「高」位置"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "升级到 ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF<a href=\"%s\" target=\"_blank\">选项页</a>是通过字段管理全局设置的自定义"
"管理页。您可以创建多个页面和子页面。"

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "添加选项页面"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "在编辑器中用作标题的占位符。"

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "标题占位符"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4个月免费"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(与 %s 重复)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "选择选项页面"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "重复分类法"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "创建分类法"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "复制文章类型"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "创建文章类型"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "链接字段组"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "添加字段"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "此字段"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "反馈"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "支持"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "开发和维护者"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "将此 %s 添加到选择字段组的位置规则中。"

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"启用双向设置允许您更新为此字段选择的每个值的目标字段中的值，添加或删除正在更"
"新的项目的文章 ID、分类法 ID 或用户 ID。有关更多信息，请阅读<a href=\"%s\" "
"target=\"_blank\">文档</a>。"

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"选择字段以将引用存储回正在更新的项目。您可以选择该字段。目标字段必须与该字段"
"的显示位置兼容。例如，如果此字段显示在分类法上，则您的目标字段应为分类法类型"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "目标字段"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "在选择值上更新一个字段，并引用回该 ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "双向"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s 字段"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "选择多个"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "WP Engine logo"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "小写字母、下划线和破折号，最多 32 字符。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "该分类法的指定术语的权限名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "指定术语功能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "用于删除本分类法条款的权限名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "删除术语功能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "用于编辑本分类法条款的权限名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "编辑条款功能"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "管理此分类法条款的权限名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "管理术语功能"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr "设置是否应将文章从搜索结果和分类法归档页面中排除。"

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "WP Engine 的更多工具"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "由 %s 团队专为使用 WordPress 构建的用户而构建"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "查看定价和升级"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "了解更多"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"利用 ACF 块和选项页面等功能以及循环、弹性内容、克隆和图库等复杂的字段类型，加"
"快您的工作流程并开发更好的网站。"

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "使用 ACF PRO 解锁高级功能并构建更多功能"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s 字段"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "无术语"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "无文章类型"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "无文章"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "无分类法"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "无字段组"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "无字段"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "无描述"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "任何文章状态"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr "此分类法关键字已被 ACF 以外的另一个分类法注册使用，因此无法使用。"

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr "此分类法关键字已被 ACF 中的另一个分类法使用，因此无法使用。"

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr "分类法关键字只能包含小写字母数字字符、下划线或破折号。"

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "分类法关键字必须小于 32 字符。"

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "回收站中未发现分类法"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "未找到分类法"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "搜索分类法"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "查看分类法"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "新分类法"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "编辑分类法"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "新增分类法"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "回收站中未发现文章类型"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "未找到文章类型"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "搜索文章类型"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "查看文章类型"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "新建文章类型"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "编辑文章类型"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "添加新文章类型"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"此文章类型关键字已被 ACF 以外的另一个文章类型关键字使用，因此无法使用。"

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr "此文章类型关键字已被 ACF 中的另一个文章类型使用，因此无法使用。"

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr "这个字段不能是 WordPress <a href=\"%s\" target=\"_blank\">保留字</a>。"

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr "文章类型关键字只能包含小写字母数字字符、下划线或破折号。"

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "文章类型关键字必须小于 20 字符。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "我们不建议在 ACF 块中使用此字段。"

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"显示 WordPress WYSIWYG 编辑器，如在「文章」和「页面」中看到的那样，允许丰富的"
"文本编辑体验，也允许多媒体内容。"

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "所见即所得编辑器"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr "允许选择一个或多个用户，用于创建数据对象之间的关系。"

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "专门用于存储网址的文本输入。"

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"切换按钮，允许您选择 1 或 0 的值（开或关、真或假等）。可以以风格化开关或复选"
"框的形式呈现。"

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr "用于选择时间的交互式用户界面。时间格式可通过字段设置进行自定义。"

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "用于存储文本段落的基本文本区输入。"

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "基本文本输入，用于存储单字符串值。"

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr "允许根据字段设置中指定的标准和选项选择一个或多个分类法术语。"

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"允许您在编辑屏幕中将字段分组为标签式部分。有助于保持字段的条理性和结构化。"

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr "下拉列表包含您指定的选项。"

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"选择一个或多个文章、页面或自定义文章类型项目的双栏接口，以创建与当前编辑的项"
"目之间的关系。包括搜索和筛选选项。"

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr "使用范围滑块元素在指定范围内选择数值的输入。"

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr "一组单选按钮输入，允许用户从您指定的值中进行单选。"

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"一种交互式且可定制的用户界面，用于选择一个或多个文章、页面或帖子类型项目，并"
"提供搜索选项。 "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "使用屏蔽字段提供密码的输入。"

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "按文章状态筛选"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"选择一个或多个文章、页面、自定义文章类型项目或归档 URL 的交互式下拉菜单，可使"
"用选项进行搜索。"

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"通过使用 WordPress 原生的 oEmbed 功能，嵌入视频、图片、推文、音频和其他内容的"
"互动组件。"

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "仅限于数值的输入。"

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr "用于显示与其他字段并列的信息。可为您的字段提供额外的上下文或说明。"

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr "允许您使用 WordPress 本地链接拾取器指定链接及其属性，如标题和目标。"

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr "使用 WordPress 本地媒体拾取器上传，或选择图片。"

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr "提供了一种将字段结构化成组的方法，以便更好地组织数据和编辑屏幕。"

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"使用谷歌地图选择位置的交互式用户界面。需要 Google Maps API 密钥和额外配置才能"
"正确显示。"

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr "使用 WordPress 本地媒体拾取器上传，或选择文件。"

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "专为存储地址而设计的文本输入。"

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"用于选择日期和时间的交互式用户界面。日期返回格式可通过字段设置进行自定义。"

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr "用于选择日期的交互式用户界面。日期返回格式可通过字段设置进行自定义。"

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "用于选择颜色或指定十六进制值的交互式用户界面。"

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr "一组复选框输入，允许用户选择一个或多个您指定的值。"

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr "一组按钮，带有您指定的值，用户可以从提供的值中选择一个选项。"

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"允许您将自定义字段分组并整理为可折叠的面板，在编辑内容时显示。它有助于保持大"
"型数据集的整洁。"

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"这提供了一个用于重复内容（如幻灯片、团队成员和「行动号召」磁贴）的方法，即作"
"为一组子字段的父字段，这些子字段可以重复显示。"

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"这为管理附件集合提供了一个交互式接口。大多数设置与图像字段类型类似。其他设置"
"允许您指定在图库中添加新附件的位置以及允许的附件最小 / 最大数量。"

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"这提供了一个简单、结构化、基于布局的编辑器。灵活的内容字段允许您通过使用布局"
"和子字段来设计可用区块，以完全可控的方式定义、创建和管理内容。"

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"这样，您就可以选择和显示现有的字段。它不会复制数据库中的任何字段，但会在运行"
"时加载并显示已选择的字段。克隆字段既可以用选择的字段替换自己，也可以将选择的"
"字段显示为一组子字段。"

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "克隆"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "专业版"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "高级"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON （较新）"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "原文"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "无效文章 ID。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "选择进行审核的文章类型无效。"

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "更多信息"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "教程"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "选择领域"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "尝试其他搜索词或浏览 %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "热门字段"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "没有「%s」的搜索结果"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "搜索字段..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "选择字段类型"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "热门"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "添加分类法"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "创建自定义分类法来分类文章类型内容"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "添加第一个分类法"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "分层分类法可以有后代（如分类）。"

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "使分类法在前台和管理后台可见。"

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "一个或多个可以用该分类法分类的文章类型。"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "类型"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "类型"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "类型"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr "可选的自定义控制器，用于替代「WP_REST_Terms_Controller」。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "在 REST API 中公开该文章类型。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "自定义查询变量名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr "可使用非漂亮的固定链接访问术语，例如，{query_var}={term_slug}。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "分层分类法 URL 中的父子术语。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "自定义 URL 中使用的别名"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "禁用此分类法的永久链接。"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr "使用分类法关键字重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "分类法关键字"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "选择要用于此分类法的固定链接类型。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "在文章类型的列表屏幕上显示分类法栏。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "显示管理栏"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "在快速 / 批量编辑面板中显示分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "快速编辑"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "在标签云小工具控件中列出分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "标签云"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr "用于对从元框中保存的分类法数据进行消毒的 PHP 函数名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "元框消毒回调"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "注册元框回调"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "无元框"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "自定义元框"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"控制内容编辑器屏幕上的元框。默认情况下，分层分类法显示「分类」元框，非分层分"
"类法显示「标签」元框。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "元框"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "分类元框"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "标签元框"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "标签的链接"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "描述块编辑器中使用的导航链接块样式变化。"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "链接到 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "标签链接"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr "为块编辑器中使用的导航块分配一个标题链接样式变化。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← 转到标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr "指定更新术语后链接回主索引的文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "返回项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← 转到 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "标签列表"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "为表格隐藏标题指定文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "标签列表导航"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "为表格分页隐藏标题指定文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "按分类筛选"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "将文本分配给文章列表中的筛选按钮。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "按项目筛选"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "按 %s 筛选"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr "默认情况下，描述并不突出；但某些主题可能会显示描述。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "描述「编辑标签」屏幕上的「描述字段」。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "描述字段描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"为创建一个层次结构指定一个父项。例如，「爵士乐」（Jazz）一词是「贝波普」"
"（Bebop）和「大乐队」（Big Band）的父词。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "描述「编辑标签」屏幕上的父字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "父字段说明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"「别名」是名称的 URL 友好版本。通常都是小写，只包含字母、数字和连字符。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "描述「编辑标签」屏幕上的别名字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "别名字段描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "名称在网站上的显示方式"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "描述编辑标签屏幕上的名称字段。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "名称字段说明"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "无标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr "当没有标签或分类时，在文章和媒体列表表中指定文本显示。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "无术语"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "无 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "未找到标记"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"当没有标签时，在分类法元框中指定文本显示，当没有项目分类法时，指定术语列表表"
"中使用的文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "未找到"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "为「最常用标签」的「标题字段」指定文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "最常用"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "从最常用的标签中选择"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"当 JavaScript 禁用时，指定元框中使用的「从最常用标签中选择」文本。仅用于非层"
"次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "从最常用的中选择"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "从最常用的 %s 中选择"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "添加或移除标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"当 JavaScript 禁用时，指定元框中使用的添加或移除项目文本。仅用于非层次分类法"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "添加或移除项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "添加或移除 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "用逗号分隔标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr "指定分类法元框中使用逗号分隔项目文本。仅用于非层次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "用逗号分隔项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "用逗号分隔 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "流行标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "指定流行项目文本。仅用于非层次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "热门项目"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "热门 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "搜索标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "指定搜索项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "父分类："

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "指定父项目文本，但在末尾加上冒号 (:) 添加。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "带冒号的父项目"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "父分类"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "指定父项目文本。仅用于层次分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "父项"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "父级 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "新标签名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "指定新项目名称文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "新项目名称"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "新 %s 名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "添加新标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "分配添加新项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "更新标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "指定更新项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "更新项目"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "更新 %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "查看标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "在管理栏中，用于在编辑时查看术语。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "编辑标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "编辑术语时位于编辑器屏幕顶部。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "所有标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "指定所有项目文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "指定菜单名称文本。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "菜单标签"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "使用 WordPress 启用和注册的活动分类法。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "分类法的描述性摘要。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "术语的描述性摘要。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "术语描述"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "单词，无空格。允许使用下划线和破折号。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "术语标题"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "默认术语的名称。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "术语名称"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr "为分类法创建一个不可删除的术语。默认情况下，文章不会选择该术语。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "默认术语"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr "该分类法中的术语是否应按照提供给 `wp_set_object_terms()` 的顺序排序。"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "术语排序"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "添加文章类型"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr "使用自定义文章类型，将 WordPress 的功能扩展到标准文章和页面之外。"

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "添加第一个文章类型"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "我知道我在做什么，请告诉我所有选项。"

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "高级配置"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "分层文章类型可以有后代（如页面）。"

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "分层"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "可在前台和管理后台看到。"

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "公开"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "仅限小写字母、下划线和破折号，最多 20 字符。"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "单数标签"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "电影"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "复数标签"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr "可选的自定义控制器，用于替代 `WP_REST_Posts_Controller`。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "控制器类"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "REST API URL 的命名空间部分。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "命名空间路由"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "文章类型 REST API URL 的基础 URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "基本 URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr "在 REST API 中公开该文章类型。使用区块编辑器时必须使用。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "在 REST API 中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "自定义查询变量名称。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "不支持查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "自定义查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr "可使用非漂亮的固定链接访问项目，例如：{post_type}={post_slug}。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "支持查询变量"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr "项目和项目的 URL 可以用查询字符串访问。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "可公开查询"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "存档 URL 的自定义别名。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "存档标题"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr "项目归档可在您的主题中使用归档模板文件进行自定义。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "支持归档等项目 URL 的分页。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "分页"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "文章类型项目的 RSS Feed URL。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "馈送 URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr "更改添加到`WP_Rewrite::$front`前缀 URL 的永久链接结构。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "前缀 URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "自定义 URL 中使用的别名。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL 标题"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "禁用该文章类型的永久链接。"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr "使用下面输入框中定义的自定义别名重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "无永久链接（防止 URL 重写）"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "自定义永久链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "文章类型关键字"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr "使用文章类型关键字作为别名重写 URL。您的永久链接结构将是"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "固定链接重写"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "删除用户时，删除该用户的项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "与用户一起删除"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "允许从「工具」>「导出」导出文章类型。"

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "可以导出"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "可选择提供用于权限的复数。"

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "复数能力名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr "选择另一种文章类型，作为该文章类型的权限基础。"

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "单项能力名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"默认情况下，文章类型的权限将继承「文章」权限名称，如 edit_post、 "
"delete_post。允许使用文章类型特定的权限，如 edit_{singular}、 "
"delete_{plural}。"

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "重命名功能"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "从搜索中排除"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"允许项目添加到「外观」>「菜单」屏幕中的菜单。必须在「屏幕选项」中打开。"

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "外观菜单支持"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "在管理栏的「新建」菜单中显示为项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "在管理栏中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "自定义元框回调"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "菜单图标"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "侧边栏菜单中的位置。"

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "菜单位置"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"默认情况下，文章类型将在管理菜单中获得一个新的顶级项目。如果此处提供了一个现"
"有的顶级项目，那么该文章类型将作为子菜单项目添加到该项目下。"

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "管理菜单父菜单"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "侧边栏菜单中的管理员编辑器导航。"

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "在管理菜单中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "项目可在管理菜单中进行管理。"

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "在用户界面中显示"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "文章的链接。"

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "导航链接块的描述样式变化。"

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "项目链接描述"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "指向 %s 的链接。"

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "文章链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "导航链接块样式变化的标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "项目链接"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s 链接"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "文章已更新。"

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "在项目更新后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "项目已更新"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s 已更新。"

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "计划发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "在计划发布项目后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "项目已计划"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s 已安排。"

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "文章已还原为草稿。"

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "在将项目还原为草稿后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "项目已还原为草稿"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s 已还原为草稿。"

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "私下发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "在私人发布项目后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "私人发布的项目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s 私人发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "文章已发布"

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "在发布一个项目后的编辑器通知中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "项目已发布"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s 已发布。"

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "文章列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr "由屏幕阅读器在文章类型列表屏幕上用于项目列表。"

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "项目列表"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s 列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "文章列表导航"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr "用于屏幕阅读器在文章类型列表屏幕上的筛选器列表分页。"

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "项目列表导航"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s 列表导航"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "按日期筛选文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr "屏幕阅读器在「文章类型列表」屏幕上用于按日期筛选标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "按日期筛选项目"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "按日期筛选 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "筛选文章列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr "用于屏幕阅读器在「文章类型列表」屏幕上的「筛选链接」标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "筛选项目列表"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "筛选 %s 列表"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "在媒体模态窗中显示上传到此项目的所有媒体。"

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "上传到此项目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "上传到此 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "插入到文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "当按钮标签添加媒体到内容时。"

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "插入媒体按钮"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "插入 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "用作特色图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "作为按钮标签选择将图片作为精选图片使用。"

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "使用精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "删除精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "在移除精选图片时使用按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "移除精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "设置精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "作为设置精选图片时的按钮标签。"

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "设置精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "精选图片"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "在编辑器中用于精选图像元框的标题。"

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "精选图片元框"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "文章属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "在用于属性元框标题的编辑器中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "属性元框"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s 属性"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "文章归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"将带有此标签的「文章类型存档」项目添加到在启用存档的情况下将项目添加到 CPT 中"
"的现有菜单时显示的文章列表。仅当在「实时预览」模式下编辑菜单并且提供了自定义"
"存档段时才会出现。"

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "档案导航菜单"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s 归档"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "回收站中未发现文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr "当回收站中没有文章时，在文章列表屏幕顶部输入文章。"

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "回收站中未发现项目"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "回收站中未发现 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "未找到文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr "当没有文章显示时，在「文章」页面顶部键入「列表」。"

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "未找到项目"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "未找到 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "搜索文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "在搜索项目时出现在项目屏幕顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "搜索项目"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "搜索 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "父页面："

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "对于文章类型列表屏幕中的分层类型。"

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "父项目前缀"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "父级 %s："

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "新文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "新项目"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "新 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "添加新文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "当添加新项目时，显示在编辑器屏幕顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "添加新项目"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "添加新 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "查看文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"出现在「所有文章」视图的管理栏中，前提是该文章类型支持归档，且主页不是该文章"
"类型的归档。"

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "查看项目"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "查看文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "在管理栏中编辑项目时查看项目。"

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "查看项目"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "查看 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "编辑文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "编辑项目时在编辑器屏幕顶部。"

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "编辑项目"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "编辑 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "所有文章"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "在管理后台的文章类型子菜单中。"

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "所有项目"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "所有 %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "文章类型的管理菜单名称。"

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "菜单名称"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "使用单数和复数标签重新生成所有标签"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "再生"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "活动文章类型已启用并与 WordPress 一起注册。"

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "文章类型的描述性摘要。"

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "添加自定义"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "启用内容编辑器中的各种功能。"

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "文章格式"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "编辑器"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackback"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "选择现有分类法对文章类型的项目进行分类。"

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "浏览字段"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "无须导入"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ".自定义文章类型 UI 插件可以停用。"

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "已从自定义文章类型 UI 导入 %d 项 -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "导入分类法失败。"

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "导入文章类型失败。"

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "没有从自定义文章类型 UI 插件选择导入。"

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "已导入 %s 个项目"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"导入与已存在的文章类型或分类标准具有相同关键字的文章类型或分类标准时，导入的"
"文章类型或分类标准将覆盖现有文章类型或分类标准的设置。"

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "从自定义文章类型 UI 导入"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"以下代码可用于注册本地版本的选择项目。在本地存储字段组、文章类型或分类法可带"
"来许多好处，如更快的加载时间、版本控制和动态字段 / 设置。只需将以下代码复制并"
"粘贴到主题的 functions.php 文件或包含在外部文件中，然后停用或删除 ACF 管理中"
"的项目即可。"

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "导出 - 生成 PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "导出"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "选择分类法"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "选择文章类型"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "已导出 %s 个项目。"

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "分类"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "标记"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "已创建 %s 分类法"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "已更新 %s 分类法"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "分类法草稿已更新。"

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "分类法计划更新。"

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "分类法已提交。"

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "分类法已保存。"

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "分类法已删除。"

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "分类法已更新。"

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr "此分类法无法注册，因为其关键字正被另一个分类法使用。"

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "%s 个分类法已同步。"

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "%s 个分类法已复制。"

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "%s 个分类法已停用。"

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "%s 个分类法已激活。"

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "术语"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "%s 个文章类型已同步。"

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "%s 个文章类型已复制。"

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "%s 个文章类型已停用。"

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "%s 个文章类型已激活。"

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "文章类型"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "高级设置"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "基本设置"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr "此文章类型无法注册，因为其关键字被另一个文章类型注册或主题注册使用。"

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "页面"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "链接现有字段组"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "已创建 %s 文章类型"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "添加字段到 %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "已更新 %s 文章类型"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "已更新文章类型草稿。"

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "已计划文章类型。"

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "已提交文章类型。"

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "已保存文章类型。"

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "已更新文章类型。"

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "已删除文章类型。"

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "键入搜索..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "仅 PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "字段组成功链接。"

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"使用自定义文章类型用户界面导入文章类型和分类法注册，并使用 ACF 管理它们。<a "
"href=\"%s\">开始</a>。"

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "分类法"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "文章类型"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "完成"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "字段组"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "选择一个或多个字段组..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "请选择要链接的字段组。"

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "字段组已成功链接。"

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "注册失败"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr "此项目无法注册，因为它的关键字正被另一个项目注册，或被另一个主题注册。"

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "权限"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "可见性"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "标签"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "字段设置选项卡"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[预览时禁用 ACF 简码值]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "关闭模态窗"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "字段移至其他组"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "关闭模态窗"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "在此标签开始一个新的标签组。"

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "新标签组"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "使用 select2 风格化复选框"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "保存其他选项"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "允许其他选择"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "添加全部切换"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "保存自定义值"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "允许自定义值"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr "复选框自定义值不能为空。取消选中任何空值。"

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "更新"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "高级自定义字段 LOGO"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "保存设置"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "字段组标题"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "添加标题"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr "ACF 新手？请查看我们的<a href=\"%s\" target=\"_blank\">入门指南</a>。"

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "添加字段组"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF 使用<a href=\"%s\" target=\"_blank\">字段组</a>将自定义字段分组在一起，然"
"后将这些字段附加到编辑屏幕。"

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "添加第一个字段组"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "选项页面"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF 块"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "画廊字段"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "弹性内容字段"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "循环字段"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "使用 ACF PRO 解锁额外功能"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "删除字段组"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "于 %1$s 在 %2$s 创建"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "组设置"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "位置规则"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"从 30 多种字段类型中进行选择。<a href=\"%s\" target=\"_blank\">了解更多</a>。"

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"开始为您的文章、页面、自定义文章类型和其他 WordPress 内容创建新的自定义字段。"

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "添加第一个字段"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "添加字段"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "演示文稿"

#: includes/fields.php:383
msgid "Validation"
msgstr "验证"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "常规"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "导入 JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "导出为 JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "%s 个字段组已停用。"

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "%s 个字段组已激活。"

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "停用"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "停用此项目"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "启用"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "激活此项目"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "移动字段组到移至回收站？"

#: acf.php:504 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "未启用"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:562
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自"
"定义字段 PRO。"

#: acf.php:560
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自"
"定义字段。"

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - 我们检测到在 ACF 初始化之前检索 ACF 字段值的一次或多"
"次调用。不支持此操作，并且可能会导致数据格式错误或丢失。 <a href=\"%2$s\" "
"target=\"_blank\">了解如何解决此问题</a>。"

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s 必须有一个 %2$s 角色的用户。"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s 必须具有有效的用户 ID。"

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "无效要求。"

#: includes/fields/class-acf-field-select.php:635
msgid "%1$s is not one of %2$s"
msgstr "%1$s 不是 %2$s 之一"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s 必须有术语 %2$s。"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s 必须是文章类型 %2$s。"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s 必须有一个有效的文章 ID。"

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s 需要一个有效的附件 ID。"

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "在 REST API 中显示"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "启用透明度"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA 数组"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA 字符串"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "十六进制字符串"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "升级到专业版"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "已启用"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "「%s」不是有效的邮件地址"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "颜色值"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "选择默认颜色"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "清除颜色"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "块"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "选项"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "用户"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "菜单项目"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "小工具"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "附件"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "分类法"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "文章"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "最后更新： %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "抱歉，该文章无法进行差异比较。"

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "无效的字段组参数。"

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "等待保存"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "已保存"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "导入"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "查看更改"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "位于 %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "位于插件: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "位于主题: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "各种"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "同步更改"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "加载差异"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "查看本地 JSON 更改"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "访问网站"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "查看详情"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "版本 %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "信息"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">服务台</a>。我们服务台上的支持专业人员将协助"
"您解决更深入的技术难题。"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">讨论</a>。我们的社区论坛上有一个活跃且友好的"
"社区，他们也许能够帮助您了解 ACF 世界的“操作方法”。"

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">文档</a>。我们详尽的文档包含您可能遇到的大多"
"数情况的参考和指南。"

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"我们热衷于支持，并希望您通过ACF充分利用自己的网站。如果遇到任何困难，可以在几"
"个地方找到帮助："

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "帮助和支持"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr "如果您需要帮助，请使用“帮助和支持”选项卡进行联系。"

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"在创建您的第一个字段组之前，我们建议您先阅读<a href=\"%s\" target=\"_blank\">"
"入门</a>指南，以熟悉插件的原理和最佳实践。"

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields 插件提供了一个可视化的表单生成器，用于自定义带有额外字"
"段的WordPress编辑屏幕，以及一个直观的API，用于在任何主题模板文件中显示自定义"
"字段的值。"

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "概述"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "位置类型「%s」 已经注册。"

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "类「%s」不存在。"

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "无效 nonce。"

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "加载字段出错。"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>错误</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "小工具"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "用户角色"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "评论"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "文章格式"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "菜单项"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "文章状态"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "菜单"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "菜单位置"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "菜单"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "文章分类法"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "子页面（有父页面）"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "父页面（有子页面）"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "顶层页面（无父页面）"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "文章页面"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "首页"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "页面类型"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "查看后端"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "查看前端"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "登录"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "当前用户"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "页面模板"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "注册"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "添加 / 编辑"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "用户表格"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "页面父级"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "超级管理员"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "当前用户角色"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "默认模板"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "文章模板"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "文章分类"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "所有 %s 格式"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "附件"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "需要 %s 值"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "如果显示此字段"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "条件逻辑"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "和"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "本地 JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "克隆字段"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr "请同时检查所有高级附加组件 (%s) 是否已更新到最新版本。"

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "此版本包含对您数据库的改进，需要升级。"

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "感谢您升级到 %1$s v%2$s！"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "需要升级数据库"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "选项页面"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "画廊"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "灵活的内容"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "中继器"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "返回所有工具"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"如果编辑界面上出现多个字段组，将使用第一个字段组的选项（顺序号最小的那个）。"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>选择</b>项目，从编辑屏幕中<b>隐藏</b>它们。"

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "在屏幕上隐藏"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "发送跟踪"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "标记"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "分类"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "页面属性"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "格式"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "作者"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "别名"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "修订"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "评论"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "讨论"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "摘要"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "内容编辑器"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "固定链接"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "显示在字段组列表"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "顺序较低的字段组将优先显示"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "顺序号"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "下字段"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "下方标签"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "指令位置"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "标签位置"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "侧面"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "正常（内容之后）"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "高（标题之后）"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "位置"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "无缝（无元框）"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "标准（WP 元框）"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "样式"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "类型"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "关键字"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "订购"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "关闭字段"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "类"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "宽度"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "包装属性"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "需要"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "说明"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "字段类型"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "单词，无空格。允许使用下划线和破折号"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "字段名称"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "这是将出现在 EDIT 页面上的名称"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "字段标签"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "删除"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "删除字段"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "移动"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "移动字段到另一个组"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "复制字段"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "编辑字段"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "拖动以重新排序"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "如果"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "无可用更新。"

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "数据库升级完成。<a href=\"%s\">查看新内容</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "阅读升级任务..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "升级失败。"

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "升级完成。"

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "将数据升级到 %s 版本"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr "强烈建议您在继续之前备份您的数据库。您确定现在要运行升级程序吗？"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "请选择至少一个网站进行升级。"

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr "数据库升级完成。<a href=\"%s\">返回网络控制面板</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "网站已更新"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "网站需要将数据库从 %1$s 升级到 %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "网站"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "升级网站"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr "以下网站需要升级数据库。选中要升级的网站，然后单击 %s。"

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "添加规则组"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "创建一组规则以确定自定义字段在哪个编辑界面上显示"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "规则"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "复制"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "复制到剪贴板"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"选择您要导出的项目，然后选择导出方法。导出为 JSON 以导出到 .json 文件，然后可"
"以将其导入到另一个 ACF 安装中。生成 PHP 以导出到 PHP 代码，您可以将其放置在主"
"题中。"

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "选择字段组"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "无字段组选择"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "生成 PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "导出字段组"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "导入文件为空"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "文件类型不正确"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "上传文件时出错。请重试"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"选择您要导入的高级自定义字段 JSON 文件。当您单击下面的导入按钮时，ACF 将导入"
"该文件中的项目。"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "导入字段组"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "同步"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "选择 %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "复制"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "复制此项目"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "支持"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "文档"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "描述"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "可同步"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "%s 个字段组已同步。"

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s 个字段组已复制。"

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "激活 <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "审查网站和升级"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "升级数据库"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "自定义字段"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "移动字段"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "请为此字段选择目的地"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "现在 %1$s 字段可以在 %2$s 字段组中找到。"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "移动完成。"

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "已启用"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "字段关键字"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "设置"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "位置"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "无"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "复制"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "（此字段）"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "检查"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "移动自定义字段"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "没有可用的切换字段"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "字段组标题为必填项"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "在保存更改之前，不能移动此字段"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "字段名开头不得使用字符串「field_」。"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "字段组草稿更新。"

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "字段组已计划。"

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "字段组已提交。"

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "字段组已保存。"

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "字段组已发布。"

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "字段组已删除。"

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "字段组已更新。"

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "工具"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "不等于"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "等于"

#: includes/locations.php:104
msgid "Forms"
msgstr "表单"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "页面"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "文章"

#: includes/fields.php:328
msgid "Relational"
msgstr "关系式"

#: includes/fields.php:327
msgid "Choice"
msgstr "选择"

#: includes/fields.php:325
msgid "Basic"
msgstr "基础"

#: includes/fields.php:276
msgid "Unknown"
msgstr "未知"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "字段类型不存在"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "检测到垃圾邮件"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "发布更新"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "更新"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "验证电子邮件"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "内容"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "标题"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "编辑字段组"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "选择小于"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "选择大于"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "值小于"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "值大于"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "值包含"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "值符合样板"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "值不等于"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "值等于"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "没有值"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "有任何值"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "取消"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "您确定吗？"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d 字段需要注意"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 字段需要注意"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "验证失败"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "验证成功"

#: includes/media.php:54
msgid "Restricted"
msgstr "受限"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "折叠详细信息"

#: includes/media.php:52
msgid "Expand Details"
msgstr "展开详细信息"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "上传到此文章"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "更新"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "编辑"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "如果您离开此页面，您所做的更改将丢失"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "文件类型必须为 %s。"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "或"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "文件大小不得超过 %s。"

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "文件大小必须至少 %s。"

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "图像高度不得超过 %dpx。"

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "图像高度必须至少 %dpx。"

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "图像宽度不得超过 %dpx。"

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "图像宽度必须至少 %dpx。"

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(无标题）"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "全尺寸"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "大尺寸"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "中号"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "缩略图"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(无标签）"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "设置文本区域高度"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "行"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "文本区域"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "额外添加复选框以切换所有选项"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "将「自定义」值保存到字段的选项中"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "允许添加「自定义」值"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "添加新选择"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "切换所有"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "允许存档 URL"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "归档"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "页面链接"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "添加"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "字段名称"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s 添加"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s 已存在"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "用户无法添加新的 %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "术语 ID"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "术语对象"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "从文章术语中加载值"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "加载术语"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "将选择术语连接到文章"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "保存术语"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "允许在编辑时创建新术语"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "创建术语"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "单选按钮"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "单值"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "多选"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "复选框"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "多值"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "选择该字段的外观"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "外观"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "选择分类法显示"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "无 %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "值必须等于或小于 %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "值必须等于或大于 %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "值必须是数字"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "数值"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "将「其他」值保存到字段的选择中"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "添加「其他」选项以允许自定义值"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "其他"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "单选按钮"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr "定义上一个手风琴停止的终点。该手风琴将不可见。"

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "允许打开此手风琴而不关闭其他手风琴。"

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "多重展开"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "在页面加载时将此手风琴显示为打开。"

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "打开"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "手风琴"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "限制哪些文件可以上传"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "文件 ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "文件 URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "文件阵列"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "添加文件"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "未选择文件"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "文件名"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "更新文件"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "编辑文件"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "选择文件"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "文件"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "密码"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "指定返回值"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "使用 Ajax 来懒加载选择？"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "在新行中输入每个默认值"

#: includes/fields/class-acf-field-select.php:227 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "选择"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "加载失败"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "正在搜索..."

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "加载更多结果..."

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "您只能选择 %d 项"

#: includes/fields/class-acf-field-select.php:96
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "您只能选择 1 项"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "请删除 %d 个字符"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "请删除 1 个字符"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "请输入 %d 或者更多字符"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "请输入 1 个或更多字符"

#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "未找到匹配项"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:88
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "有 %d 个结果，请使用上下箭头键浏览。"

#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "有一个结果，按输入选择。"

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "选择"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "用户 ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "用户对象"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "用户数组"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "所有用户角色"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "按角色筛选"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "用户"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "分隔符"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "选择颜色"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "默认"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "清除"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "颜色选择器"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "选择"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "完成"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "现在"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "时区"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "微秒"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "毫秒"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "秒"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "分"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "小时"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "时间"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "选择时间"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "日期时间选择器"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "终点"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "左对齐"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "顶部对齐"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "位置"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "标签"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "值必须是有效的 URL"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "链接 URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "链接阵列"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "在新窗口 / 标签中打开"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "选择链接"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "链接"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "邮箱"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "步长"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "最大值"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "最小值"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "范围"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "两者（数组）"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "标签"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "值"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "垂直"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "水平"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "red : 红色"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr "为获得更多控制权，您可以像这样指定值和标签："

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "在新行中输入每个选项。"

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "选择"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "按钮组"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "允许空值"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "父级"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "在点击字段之前，TinyMCE 不会被初始化。"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "延迟初始化"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "显示媒体上传按钮"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "工具栏"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "仅文本"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "仅可视化"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "可视化和文本"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "标签"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "点击初始化 TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "文本"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "可视化"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "值不得超过 %d 字符"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "留空表示无限制"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "字符限制"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "出现在输入后"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "附加"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "出现在输入之前"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "预输入"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "出现在输入内容中"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "占位符文本"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "创建新文章时显示"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "文本"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s 至少需要 %2$s 选项"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "文章 ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "文章对象"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "最大文章数"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "最小文章数"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "精选图片"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "选中的元素将显示在每个结果中"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "元素"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "分类法"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "文章类型"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "筛选"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "所有分类法"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "按分类筛选"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "所有文章类型"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "按文章类型筛选"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "搜索..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "选择分类法"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "选择文章类型"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "未找到匹配结果"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "载入"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "达到最大值 ( {max} 值 )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "关系"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "逗号分隔列表。所有类型留空"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "允许的文件类型"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "最大值"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "文件大小"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "限制哪些图片可以上传"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "最少"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "上传到文章"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "全部"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "限制媒体库选择"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "文件库"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "预览大小"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "图片 ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "图片 URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "图像阵列"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "在前端指定返回值"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "返回值"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "添加图片"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "没有选择图片"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "移除"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "编辑"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "全部图片"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "更新图片"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "编辑图片"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "选择图片"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "图片"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "允许 HTML 标记显示为可见文本，而不是渲染"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "转义 HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "无格式化"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "自动添加 &lt;br&gt"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "自动添加段落"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "控制如何渲染新行"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "新行"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "星期开始于"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "保存数值时使用的格式"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "保存格式"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "星期"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "上一页"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "下一页"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "今天"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "已完成"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "日期选择器"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "宽度"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "嵌入大小"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "输入 URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "未启用时显示的文本"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "关闭文本"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "已启用时显示的文本"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "启用文本"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "风格化用户界面"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "默认值"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "在复选框旁边显示文本"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "信息"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
msgid "No"
msgstr "没有"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:339
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
msgid "Yes"
msgstr "确认"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "真 / 假"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "行"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "表格"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "块"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "指定用于渲染选择字段的样式"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "布局"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "子字段"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "组合"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "自定义地图高度"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "高度"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "设置初始缩放级别"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "缩放"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "将初始地图居中"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "居中"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "搜索地址..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "查找当前位置"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "清除位置"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "搜索"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "抱歉，此浏览器不支持地理位置定位"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "谷歌地图"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "通过模板函数返回的格式"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "返回格式"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "自定义："

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "编辑文章时显示的格式"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "显示格式"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "时间选择器"

#. translators: counts for inactive field groups
#: acf.php:510
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "未激活 <span class=\"count\">(%s)</span>"

#: acf.php:471
msgid "No Fields found in Trash"
msgstr "回收站中未发现字段"

#: acf.php:470
msgid "No Fields found"
msgstr "未找到字段"

#: acf.php:469
msgid "Search Fields"
msgstr "搜索字段"

#: acf.php:468
msgid "View Field"
msgstr "查看字段"

#: acf.php:467 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "新建字段"

#: acf.php:466
msgid "Edit Field"
msgstr "编辑字段"

#: acf.php:465
msgid "Add New Field"
msgstr "添加新字段"

#: acf.php:463
msgid "Field"
msgstr "字段"

#: acf.php:462 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "字段"

#: acf.php:437
msgid "No Field Groups found in Trash"
msgstr "回收站中未发现字段组"

#: acf.php:436
msgid "No Field Groups found"
msgstr "未找到字段组"

#: acf.php:435
msgid "Search Field Groups"
msgstr "搜索字段组"

#: acf.php:434
msgid "View Field Group"
msgstr "查看字段组"

#: acf.php:433
msgid "New Field Group"
msgstr "新建字段组"

#: acf.php:432
msgid "Edit Field Group"
msgstr "编辑字段组"

#: acf.php:431
msgid "Add New Field Group"
msgstr "添加新字段组"

#: acf.php:430 acf.php:464
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "添加新的"

#: acf.php:429
msgid "Field Group"
msgstr "字段组"

#: acf.php:428 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "字段组"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "【高级自定义字段 ACF】使用强大、专业和直观的字段自定义WordPress。"

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "高级自定义字段"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields 专业版"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr ""

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "选项已更新"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""

#: pro/updates.php:279
msgid "Check Again"
msgstr "重新检查"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "发布"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"这个选项页上还没有自定义字段群组。<a href=\"%s\">创建自定义字段群组</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>错误</b>，不能连接到更新服务器"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "显示"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "添加行"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
#, fuzzy
#| msgid "layout"
msgid "layout"
msgid_plural "layouts"
msgstr[0] "布局"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "布局"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "这个字段需要至少 {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} 可用 (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} 需要 (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "灵活内容字段需要至少一个布局"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "点击下面的 \"%s\" 按钮创建布局"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "添加布局"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "删除布局"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "删除布局"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "复制布局"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "添加新布局"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "添加布局"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "最小"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "最大"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "最小布局"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "最大布局"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "按钮标签"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "添加图片到相册"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "已到最大选择"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "长度"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "标题"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "添加到相册"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "批量动作"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "按上传日期排序"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "按修改日期排序"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "按标题排序"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "颠倒当前排序"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "关闭"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "最小选择"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "最大选择"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "允许的文字类型"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "已到最小行数 ({min} 行)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "已到最大行数 ({max} 行)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "最大行数"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "拖拽排序"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "添加行"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "删除行"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "首页"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "文章页"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "首页"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "文章页"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "还没有选项页面"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "关闭许可证"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "激活许可证"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "许可证号"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "更新信息"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "当前版本"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "最新版本"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "可用更新"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "更新通知"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "在上面输入许可证号解锁更新"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "更新插件"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr ""
