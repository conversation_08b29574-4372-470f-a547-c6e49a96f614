<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="open_sansbold_italic" horiz-adv-x="1128" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="532" />
<glyph unicode="&#xfb01;" horiz-adv-x="1352" d="M0 0zM-45 -492q-104 0 -174 25v242q61 -21 115 -21q61 0 107 40t65 130l204 965h-163l30 145l183 84l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49l-80 -224q-69 31 -133 31q-57 0 -92 -40t-47 -105l-12 -62h219l-49 -229h-220l-215 -1010q-77 -371 -403 -371z M1065 1380q0 87 47.5 131.5t134.5 44.5q73 0 111 -31t38 -89q0 -80 -44 -129.5t-136 -49.5q-151 0 -151 123zM1081 0h-301l237 1118h301z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1352" d="M0 0zM-45 -492q-104 0 -174 25v242q61 -21 115 -21q61 0 107 40t65 130l204 965h-163l30 145l183 84l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49l-80 -224q-69 31 -133 31q-57 0 -92 -40t-47 -105l-12 -62h219l-49 -229h-220l-215 -1010q-77 -371 -403 -371z M1081 0h-301l330 1556h301z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2048" d="M-45 -492q-104 0 -174 25v242q61 -21 115 -21q61 0 107 40t65 130l204 965h-163l30 145l183 84l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49l-80 -224q-69 31 -133 31q-57 0 -92 -40t-47 -105l-12 -62h395l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49 l-79 -224q-69 31 -134 31q-57 0 -91.5 -40t-47.5 -105l-12 -62h219l-49 -229h-219l-215 -1010q-77 -371 -404 -371q-104 0 -174 25v242q61 -21 115 -21q136 0 172 170l205 965h-396l-215 -1010q-77 -371 -403 -371zM1778 0h-301l237 1118h301zM1761 1380q0 87 48 131.5 t135 44.5q73 0 111 -31t38 -89q0 -80 -44 -129.5t-136 -49.5q-152 0 -152 123z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2048" d="M-45 -492q-104 0 -174 25v242q61 -21 115 -21q61 0 107 40t65 130l204 965h-163l30 145l183 84l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49l-80 -224q-69 31 -133 31q-57 0 -92 -40t-47 -105l-12 -62h395l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49 l-79 -224q-69 31 -134 31q-57 0 -91.5 -40t-47.5 -105l-12 -62h219l-49 -229h-219l-215 -1010q-77 -371 -404 -371q-104 0 -174 25v242q61 -21 115 -21q136 0 172 170l205 965h-396l-215 -1010q-77 -371 -403 -371zM1778 0h-301l329 1556h301z" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="1044" />
<glyph unicode=" "  horiz-adv-x="532" />
<glyph unicode="&#x09;" horiz-adv-x="532" />
<glyph unicode="&#xa0;" horiz-adv-x="532" />
<glyph unicode="!" horiz-adv-x="586" d="M391 485h-241l157 977h340zM25 115q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5z" />
<glyph unicode="&#x22;" horiz-adv-x="928" d="M549 1462l-152 -528h-196l71 528h277zM954 1462l-151 -528h-199l74 528h276z" />
<glyph unicode="#" horiz-adv-x="1323" d="M1036 846l-69 -232h258l-19 -206h-297l-116 -408h-220l117 408h-194l-115 -408h-215l113 408h-238l18 206h277l70 232h-252l18 209h289l119 407h217l-117 -407h199l116 407h215l-116 -407h239l-18 -209h-279zM553 614h197l69 232h-196z" />
<glyph unicode="$" d="M1034 496q0 -184 -125.5 -291.5t-367.5 -124.5l-39 -199h-140l44 201q-209 12 -355 86v266q198 -107 404 -117l71 322q-163 61 -241 151t-78 214q0 173 127 279.5t350 121.5l35 151h139l-33 -151q166 -22 295 -90l-106 -232q-132 65 -242 74l-63 -299q131 -51 195 -99.5 t97 -113t33 -149.5zM594 322q63 9 102 45t39 98q0 46 -24.5 75.5t-59.5 43.5zM633 1157q-62 -7 -96.5 -41t-34.5 -94q0 -79 80 -111z" />
<glyph unicode="%" horiz-adv-x="1753" d="M518 1274q-63 0 -110.5 -128.5t-47.5 -277.5q0 -96 56 -96q65 0 112 131t47 275q0 96 -57 96zM821 1165q0 -166 -56 -310t-151 -217t-217 -73q-139 0 -210.5 83.5t-71.5 236.5q0 169 55.5 311.5t148.5 214.5t216 72q137 0 211.5 -80t74.5 -238zM1554 1462l-1083 -1462 h-240l1088 1462h235zM1376 690q-39 0 -75 -56t-59 -154t-23 -195t55 -97q41 0 77 55t59.5 154.5t23.5 196.5q0 96 -58 96zM1679 590q0 -167 -54 -313.5t-148 -220.5t-215 -74q-144 0 -216.5 78.5t-72.5 222.5q0 177 53 322.5t148 219.5t219 74q137 0 211.5 -78.5 t74.5 -230.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1450" d="M1325 0h-350l-72 98q-175 -118 -403 -118q-209 0 -320.5 97.5t-111.5 280.5q0 145 78.5 248.5t273.5 200.5q-76 130 -76 258q0 195 117.5 307.5t316.5 112.5q169 0 266 -82.5t97 -224.5q0 -280 -365 -426l195 -263q44 57 80.5 121.5t78.5 173.5h300q-133 -313 -310 -497z M541 623q-88 -51 -123 -104.5t-35 -131.5q0 -65 45.5 -108t116.5 -43q115 0 221 59zM662 920q113 59 155.5 111t42.5 112q0 57 -30 82.5t-70 25.5q-66 0 -102.5 -46.5t-36.5 -119.5q0 -46 12 -92t29 -73z" />
<glyph unicode="'" horiz-adv-x="522" d="M549 1462l-152 -528h-196l71 528h277z" />
<glyph unicode="(" horiz-adv-x="694" d="M74 281q0 339 122.5 626.5t381.5 554.5h262q-255 -278 -377.5 -573.5t-122.5 -618.5q0 -308 117 -594h-234q-149 266 -149 605z" />
<glyph unicode=")" horiz-adv-x="694" d="M618 858q0 -342 -124 -630.5t-379 -551.5h-262q499 545 499 1192q0 307 -116 594h233q149 -264 149 -604z" />
<glyph unicode="*" horiz-adv-x="1116" d="M885 1522l-113 -353l387 29l-18 -254l-338 43l160 -336l-246 -73l-90 337l-197 -278l-207 164l275 248l-326 92l86 237l338 -174l33 369z" />
<glyph unicode="+" d="M475 612h-366v219h366v369h219v-369h367v-219h-367v-364h-219v364z" />
<glyph unicode="," horiz-adv-x="569" d="M377 238l8 -23q-118 -255 -262 -479h-225q74 167 194 502h285z" />
<glyph unicode="-" horiz-adv-x="659" d="M41 424l53 250h524l-53 -250h-524z" />
<glyph unicode="." horiz-adv-x="584" d="M25 115q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5z" />
<glyph unicode="/" horiz-adv-x="862" d="M1014 1462l-809 -1462h-295l809 1462h295z" />
<glyph unicode="0" d="M1110 1012q0 -470 -168.5 -751t-472.5 -281q-198 0 -300.5 122t-102.5 365q0 297 84 537t228 360.5t333 120.5q399 0 399 -473zM684 1235q-80 0 -149.5 -104t-117.5 -302t-48 -368q0 -115 27.5 -173.5t97.5 -58.5q81 0 150.5 106t116 301t46.5 386q0 111 -30.5 162 t-92.5 51z" />
<glyph unicode="1" d="M688 0h-305l180 829q35 152 76 287q-9 -8 -61.5 -47t-262.5 -170l-133 215l566 348h249z" />
<glyph unicode="2" d="M913 0h-962l43 213l477 424q180 159 248.5 254.5t68.5 179.5q0 75 -41 114.5t-110 39.5q-66 0 -135.5 -33.5t-171.5 -118.5l-146 203q132 112 252 159.5t250 47.5q190 0 301 -98t111 -259q0 -107 -41 -201t-122.5 -188t-266.5 -245l-269 -222v-10h568z" />
<glyph unicode="3" d="M1104 1149q0 -156 -94.5 -262t-261.5 -135v-4q131 -26 198.5 -106.5t67.5 -201.5q0 -133 -74 -238t-212 -163.5t-327 -58.5q-239 0 -387 79v267q84 -50 182 -75.5t191 -25.5q158 0 243 63.5t85 176.5q0 172 -258 172h-138l46 221h73q167 0 263 62t96 172q0 67 -43 104 t-121 37q-134 0 -287 -100l-127 204q124 81 232.5 113.5t246.5 32.5q190 0 298 -90.5t108 -243.5z" />
<glyph unicode="4" d="M1028 303h-170l-63 -303h-293l63 303h-590l48 234l770 925h311l-195 -919h170zM616 543l58 248q12 58 40 164t42 141h-6q-35 -63 -132 -181l-313 -372h311z" />
<glyph unicode="5" d="M623 922q183 0 289 -103t106 -287q0 -167 -71.5 -292t-208.5 -192.5t-330 -67.5q-117 0 -218.5 23t-162.5 58v269q174 -99 352 -99q154 0 241 71t87 194q0 94 -57.5 141t-166.5 47q-102 0 -213 -33l-104 78l207 733h755l-55 -262h-489l-88 -293q72 15 127 15z" />
<glyph unicode="6" d="M88 469q0 202 61 395.5t167.5 335t256.5 213.5t357 72q125 0 223 -27l-51 -246q-84 25 -191 25q-194 0 -313.5 -108t-185.5 -345h4q115 166 311 166q157 0 242.5 -97t85.5 -273q0 -169 -71 -313.5t-190.5 -215.5t-277.5 -71q-212 0 -320 127t-108 362zM530 227 q99 0 161.5 94t62.5 236q0 71 -33.5 113.5t-102.5 42.5q-60 0 -114.5 -35.5t-87.5 -95.5t-33 -160q0 -91 40 -143t107 -52z" />
<glyph unicode="7" d="M78 0l737 1202h-629l56 260h975l-41 -194l-752 -1268h-346z" />
<glyph unicode="8" d="M721 1485q123 0 215.5 -42t141 -118t48.5 -174q0 -134 -80.5 -233.5t-230.5 -151.5q217 -141 217 -365q0 -122 -63.5 -218.5t-181 -149.5t-273.5 -53q-214 0 -336.5 100t-122.5 270q0 298 348 426q-165 132 -165 299q0 119 58 212.5t168 145.5t257 52zM582 643 q-116 -45 -173 -107t-57 -153q0 -81 50 -128.5t135 -47.5q93 0 147.5 53.5t54.5 138.5q0 73 -36.5 131.5t-120.5 112.5zM694 1260q-76 0 -121 -46.5t-45 -119.5q0 -132 123 -201q185 72 185 221q0 68 -39.5 107t-102.5 39z" />
<glyph unicode="9" d="M1092 1001q0 -280 -99 -533t-264 -370.5t-403 -117.5q-128 0 -240 32v256q111 -41 227 -41q121 0 207.5 49t144 138.5t99.5 257.5h-4q-111 -158 -295 -158q-163 0 -252.5 103.5t-89.5 285.5q0 166 73 305.5t196 208t286 68.5q203 0 308.5 -123t105.5 -361zM645 1237 q-65 0 -115.5 -42t-78 -114t-27.5 -153q0 -87 37.5 -131.5t105.5 -44.5q60 0 111.5 36.5t82 100t30.5 158.5q0 84 -35.5 137t-110.5 53z" />
<glyph unicode=":" horiz-adv-x="584" d="M207 940q0 92 55.5 145.5t149.5 53.5q68 0 108.5 -38.5t40.5 -107.5q0 -86 -54.5 -140t-144.5 -54q-72 0 -113.5 36.5t-41.5 104.5zM25 115q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5z" />
<glyph unicode=";" horiz-adv-x="584" d="M385 215q-118 -255 -262 -479h-225q74 167 194 502h285zM207 940q0 92 55.5 145.5t149.5 53.5q68 0 108.5 -38.5t40.5 -107.5q0 -86 -54.5 -140t-144.5 -54q-72 0 -113.5 36.5t-41.5 104.5z" />
<glyph unicode="&#x3c;" d="M1061 203l-952 438v143l952 496v-240l-643 -317l643 -281v-239z" />
<glyph unicode="=" d="M109 807v217h952v-217h-952zM109 418v219h952v-219h-952z" />
<glyph unicode="&#x3e;" d="M109 442l643 281l-643 317v240l952 -496v-143l-952 -438v239z" />
<glyph unicode="?" horiz-adv-x="940" d="M260 485l14 78q19 103 73.5 177t172.5 155q124 84 157.5 127t33.5 96q0 119 -133 119q-50 0 -106.5 -16t-201.5 -84l-92 221q230 125 445 125q177 0 280 -87.5t103 -244.5q0 -83 -28.5 -149.5t-82.5 -123t-190 -147.5q-64 -43 -96.5 -73t-52.5 -64.5t-38 -108.5h-258z M166 115q0 91 55 144.5t150 53.5q68 0 108.5 -38t40.5 -107q0 -87 -55 -141t-143 -54q-74 0 -115 38t-41 104z" />
<glyph unicode="@" horiz-adv-x="1753" d="M1733 840q0 -173 -64 -321t-177.5 -231t-254.5 -83q-88 0 -144.5 38.5t-72.5 108.5h-6q-50 -77 -113 -112t-147 -35q-127 0 -198 79.5t-71 229.5q0 147 67.5 276.5t187.5 205t268 75.5q185 0 327 -55l-106 -420q-11 -44 -19 -76.5t-8 -64.5q0 -68 58 -68q66 0 124 64 t92.5 171t34.5 214q0 213 -123.5 325.5t-359.5 112.5q-203 0 -366.5 -94t-255 -266t-91.5 -392q0 -243 134 -380.5t376 -137.5q117 0 219.5 20t221.5 66v-186q-230 -90 -465 -90q-217 0 -378 85.5t-246 241.5t-85 359q0 279 120.5 497t343 341.5t497.5 123.5 q318 0 499 -163.5t181 -458.5zM995 889q-82 0 -145.5 -51.5t-100 -137t-36.5 -174.5q0 -65 24.5 -102t69.5 -37q141 0 213 270l57 222q-36 10 -82 10z" />
<glyph unicode="A" horiz-adv-x="1286" d="M842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333z" />
<glyph unicode="B" horiz-adv-x="1270" d="M788 1462q229 0 346 -81.5t117 -243.5q0 -150 -83 -247.5t-236 -129.5v-6q100 -26 159.5 -96.5t59.5 -180.5q0 -229 -153 -353t-423 -124h-522l309 1462h426zM545 883h149q121 0 181.5 48.5t60.5 139.5q0 137 -170 137h-152zM412 256h180q117 0 183.5 58t66.5 161 q0 162 -183 162h-165z" />
<glyph unicode="C" horiz-adv-x="1253" d="M905 1227q-132 0 -237.5 -81t-169.5 -238.5t-64 -338.5q0 -167 68.5 -248t218.5 -81q146 0 338 77v-260q-199 -77 -400 -77q-254 0 -395 149.5t-141 423.5q0 262 104 482.5t278 335t400 114.5q125 0 222 -22.5t208 -82.5l-118 -250q-106 59 -175 78t-137 19z" />
<glyph unicode="D" horiz-adv-x="1386" d="M1323 909q0 -280 -98 -486.5t-283.5 -314.5t-437.5 -108h-451l309 1462h396q270 0 417.5 -143t147.5 -410zM518 256q148 0 258 76t172 223.5t62 337.5q0 154 -72.5 234.5t-208.5 80.5h-115l-202 -952h106z" />
<glyph unicode="E" horiz-adv-x="1110" d="M870 0h-817l309 1462h818l-54 -254h-512l-67 -321h477l-55 -254h-477l-80 -377h512z" />
<glyph unicode="F" horiz-adv-x="1087" d="M358 0h-305l309 1462h814l-54 -254h-508l-79 -377h473l-56 -253h-473z" />
<glyph unicode="G" horiz-adv-x="1413" d="M754 821h563l-162 -762q-134 -46 -248.5 -62.5t-242.5 -16.5q-259 0 -400 147t-141 422q0 268 107 484.5t301 334t448 117.5q218 0 410 -99l-115 -251q-74 40 -148 64t-161 24q-153 0 -273.5 -83t-189 -236.5t-68.5 -330.5q0 -172 72.5 -252.5t222.5 -80.5q76 0 170 24 l66 299h-267z" />
<glyph unicode="H" horiz-adv-x="1434" d="M1135 0h-306l134 631h-471l-134 -631h-305l309 1462h306l-121 -573h471l121 573h305z" />
<glyph unicode="I" horiz-adv-x="659" d="M53 0l312 1462h305l-312 -1462h-305z" />
<glyph unicode="J" horiz-adv-x="678" d="M-135 -430q-94 0 -187 27v253q88 -20 164 -20q99 0 160.5 60.5t89.5 191.5l293 1380h305l-303 -1423q-52 -245 -175.5 -357t-346.5 -112z" />
<glyph unicode="K" horiz-adv-x="1255" d="M1141 0h-338l-211 592l-125 -70l-109 -522h-305l309 1462h306l-152 -702l158 205l409 497h361l-594 -700z" />
<glyph unicode="L" horiz-adv-x="1061" d="M53 0l309 1462h306l-256 -1206h512l-54 -256h-817z" />
<glyph unicode="M" horiz-adv-x="1802" d="M838 369l551 1093h423l-309 -1462h-280l145 692q53 247 105 441h-5l-569 -1133h-281l-61 1133h-4q-11 -88 -38 -231t-187 -902h-275l309 1462h404l68 -1093h4z" />
<glyph unicode="N" horiz-adv-x="1546" d="M1247 0h-342l-356 1106h-6l-4 -32q-32 -216 -66 -386l-145 -688h-275l309 1462h357l340 -1077h4q12 76 39 217t180 860h274z" />
<glyph unicode="O" horiz-adv-x="1495" d="M1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246t56 357 q0 142 -65 219.5t-183 77.5z" />
<glyph unicode="P" horiz-adv-x="1188" d="M522 774h56q142 0 223.5 69t81.5 185q0 180 -195 180h-74zM1190 1036q0 -241 -169.5 -378.5t-467.5 -137.5h-86l-109 -520h-305l309 1462h338q242 0 366 -106.5t124 -319.5z" />
<glyph unicode="Q" horiz-adv-x="1495" d="M1432 938q0 -316 -122.5 -555.5t-334.5 -337.5l254 -393h-359l-178 328h-26q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87 t155.5 246t56 357q0 142 -65 219.5t-183 77.5z" />
<glyph unicode="R" horiz-adv-x="1247" d="M530 813h78q131 0 204 57t73 174q0 82 -47.5 123t-149.5 41h-74zM477 561l-119 -561h-305l309 1462h359q237 0 356 -102t119 -299q0 -158 -83 -271.5t-239 -168.5l261 -621h-332l-207 561h-119z" />
<glyph unicode="S" horiz-adv-x="1085" d="M946 432q0 -209 -148 -330.5t-401 -121.5q-221 0 -356 90v274q193 -108 358 -108q112 0 175 42.5t63 116.5q0 43 -13.5 75.5t-38.5 60.5t-124 102q-138 99 -194 196t-56 209q0 129 62 230.5t176.5 158t263.5 56.5q217 0 397 -99l-109 -233q-156 74 -288 74 q-83 0 -136 -45t-53 -119q0 -61 33 -106.5t148 -120.5q121 -80 181 -176.5t60 -225.5z" />
<glyph unicode="T" horiz-adv-x="1087" d="M571 0h-305l254 1204h-352l55 258h1010l-55 -258h-353z" />
<glyph unicode="U" horiz-adv-x="1415" d="M1434 1462l-201 -946q-57 -266 -218 -401t-419 -135q-212 0 -333.5 113.5t-121.5 307.5q0 72 15 138l196 923h305l-194 -919q-17 -74 -17 -125q0 -178 189 -178q123 0 195 76.5t104 228.5l194 917h306z" />
<glyph unicode="V" horiz-adv-x="1208" d="M535 299q78 221 110 283l432 880h316l-748 -1462h-334l-127 1462h295l51 -880q4 -45 4 -133q-2 -103 -6 -150h7z" />
<glyph unicode="W" horiz-adv-x="1831" d="M1006 1018q-46 -146 -115 -299l-324 -719h-338l-45 1462h287l6 -798q0 -52 -4 -173t-10 -174h6q22 64 67 180.5t60 145.5l369 819h270l21 -873q0 -146 -9 -272h6q43 129 131 349l330 796h309l-647 -1462h-346l-22 721l-2 139q0 88 4 158h-4z" />
<glyph unicode="X" horiz-adv-x="1241" d="M1124 0h-331l-172 543l-396 -543h-342l576 764l-238 698h320l153 -518l363 518h344l-545 -725z" />
<glyph unicode="Y" horiz-adv-x="1155" d="M627 870l374 592h342l-618 -903l-119 -559h-303l119 559l-236 903h312z" />
<glyph unicode="Z" horiz-adv-x="1098" d="M920 0h-981l38 201l777 1005h-543l53 256h936l-41 -202l-782 -1004h596z" />
<glyph unicode="[" horiz-adv-x="678" d="M436 -324h-473l381 1786h473l-45 -211h-215l-291 -1364h215z" />
<glyph unicode="\" horiz-adv-x="862" d="M481 1462l224 -1462h-267l-217 1462h260z" />
<glyph unicode="]" horiz-adv-x="678" d="M-92 -113h213l291 1364h-215l45 211h473l-381 -1786h-471z" />
<glyph unicode="^" horiz-adv-x="1081" d="M20 520l619 950h147l277 -950h-223l-174 633l-402 -633h-244z" />
<glyph unicode="_" horiz-adv-x="819" d="M635 -324h-821l30 140h822z" />
<glyph unicode="`" horiz-adv-x="1135" d="M934 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="a" horiz-adv-x="1217" d="M406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44z" />
<glyph unicode="b" horiz-adv-x="1219" d="M813 1139q146 0 230.5 -108t84.5 -298t-68 -367.5t-187 -281.5t-263 -104q-194 0 -276 163h-8l-58 -143h-231l330 1556h301l-62 -288q-41 -182 -84 -299h8q78 98 142.5 134t140.5 36zM692 895q-68 0 -130 -65t-102 -180.5t-40 -250.5q0 -80 37 -128t102 -48q67 0 128 69 t98.5 189.5t37.5 237.5q0 176 -131 176z" />
<glyph unicode="c" horiz-adv-x="989" d="M506 -20q-201 0 -308.5 107.5t-107.5 303.5q0 212 74.5 385.5t209.5 268t308 94.5q182 0 328 -72l-92 -229q-54 23 -106 40t-118 17q-85 0 -153.5 -64t-107 -175.5t-38.5 -239.5q0 -96 45.5 -144.5t126.5 -48.5q76 0 141 23.5t134 58.5v-246q-152 -79 -336 -79z" />
<glyph unicode="d" horiz-adv-x="1217" d="M406 -20q-147 0 -231.5 107t-84.5 300q0 196 71.5 374.5t188.5 278t258 99.5q82 0 141.5 -37t112.5 -127h8l2 28q6 110 25 195l76 358h301l-330 -1556h-229l14 145h-4q-71 -87 -148.5 -126t-170.5 -39zM532 223q66 0 128.5 68.5t100.5 182.5t38 245q0 80 -37.5 128 t-102.5 48q-68 0 -129.5 -72t-98 -190t-36.5 -234q0 -176 137 -176z" />
<glyph unicode="e" horiz-adv-x="1141" d="M696 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95zM532 -20q-210 0 -326 113t-116 319q0 207 82.5 377.5t223.5 260t319 89.5q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-477 -101.5h-51l-2 -21v-20q0 -91 51.5 -143.5t147.5 -52.5 q87 0 158 19t172 67v-227q-172 -86 -390 -86z" />
<glyph unicode="f" horiz-adv-x="764" d="M-45 -492q-104 0 -174 25v242q61 -21 115 -21q61 0 107 40t65 130l204 965h-163l30 145l183 84l18 84q41 190 138.5 277.5t273.5 87.5q131 0 235 -49l-80 -224q-69 31 -133 31q-57 0 -92 -40t-47 -105l-12 -62h219l-49 -229h-220l-215 -1010q-77 -371 -403 -371z" />
<glyph unicode="g" horiz-adv-x="1108" d="M1186 1116l-35 -166l-174 -41q16 -52 16 -118q0 -195 -121 -308.5t-329 -113.5q-59 0 -99 10q-84 -27 -84 -78q0 -34 30 -49t89 -23l137 -18q163 -21 237.5 -84.5t74.5 -183.5q0 -211 -156 -323t-446 -112q-208 0 -324.5 75.5t-116.5 207.5q0 102 68.5 175.5t214.5 121.5 q-74 47 -74 133q0 71 44.5 122.5t146.5 98.5q-65 49 -96 112t-31 153q0 199 125.5 315.5t341.5 116.5q83 0 166 -23h395zM365 -6q-106 -14 -160.5 -57t-54.5 -109q0 -115 194 -115q151 0 228 45t77 127q0 39 -32.5 60t-137.5 35zM614 948q-77 0 -124.5 -76.5t-47.5 -191.5 q0 -119 103 -119q75 0 121.5 76.5t46.5 193.5t-99 117z" />
<glyph unicode="h" horiz-adv-x="1237" d="M977 0h-301l137 653q16 68 16 119q0 123 -108 123q-92 0 -167 -114t-118 -318l-98 -463h-301l330 1556h301q-39 -181 -60 -278t-86 -309h8q62 77 138 123.5t176 46.5q138 0 213.5 -83.5t75.5 -238.5q0 -73 -23 -180z" />
<glyph unicode="i" horiz-adv-x="608" d="M322 1380q0 87 47.5 131.5t134.5 44.5q73 0 111 -31t38 -89q0 -80 -44 -129.5t-136 -49.5q-151 0 -151 123zM338 0h-301l237 1118h301z" />
<glyph unicode="j" horiz-adv-x="608" d="M-90 -492q-104 0 -174 25v242q61 -21 114 -21q137 0 173 170l253 1194h302l-265 -1239q-77 -371 -403 -371zM324 1380q0 87 47.5 131.5t134.5 44.5q73 0 111 -31t38 -89q0 -80 -44 -129.5t-136 -49.5q-151 0 -151 123z" />
<glyph unicode="k" horiz-adv-x="1163" d="M920 1118h344l-498 -504l285 -614h-336l-183 420l-120 -72l-74 -348h-301l330 1556h301l-148 -694q-8 -41 -29 -117l-28 -102h4z" />
<glyph unicode="l" horiz-adv-x="608" d="M338 0h-301l330 1556h301z" />
<glyph unicode="m" horiz-adv-x="1853" d="M844 1139q219 0 262 -228h6q68 110 160.5 169t197.5 59q136 0 207.5 -85t71.5 -237q0 -76 -23 -180l-133 -637h-301l138 653q16 68 16 119q0 123 -98 123q-92 0 -166.5 -112t-118.5 -318l-96 -465h-301l137 653q16 68 16 119q0 123 -98 123q-92 0 -167 -114t-118 -318 l-98 -463h-301l237 1118h230l-21 -207h6q146 228 355 228z" />
<glyph unicode="n" horiz-adv-x="1237" d="M977 0h-301l137 653q16 68 16 119q0 123 -108 123q-92 0 -167 -114t-118 -318l-98 -463h-301l237 1118h230l-21 -207h6q146 228 355 228q138 0 213.5 -83.5t75.5 -238.5q0 -73 -23 -180z" />
<glyph unicode="o" horiz-adv-x="1198" d="M805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5q196 0 310 -118 t114 -325z" />
<glyph unicode="p" horiz-adv-x="1219" d="M813 1139q146 0 230.5 -107.5t84.5 -300.5q0 -191 -68.5 -367.5t-187.5 -280t-262 -103.5q-83 0 -143 37t-111 126h-8q-12 -159 -43 -295l-72 -340h-301l342 1610h230l-17 -170h9q138 191 317 191zM692 895q-68 0 -131.5 -67.5t-102 -180t-38.5 -248.5q0 -80 37 -128 t102 -48q67 0 128 69t98.5 189.5t37.5 237.5q0 176 -131 176z" />
<glyph unicode="q" horiz-adv-x="1217" d="M391 -20q-88 0 -156 47.5t-106.5 138.5t-38.5 219q0 198 72 377.5t189 278t257 98.5q86 0 152.5 -37.5t124.5 -126.5h8l57 143h232l-342 -1610h-301q47 218 73 337.5t84 304.5h-8q-72 -94 -143 -132t-154 -38zM535 223q64 0 127.5 70t100 181t36.5 245q0 80 -37.5 128 t-102.5 48q-68 0 -129.5 -72t-98 -190t-36.5 -234q0 -88 36.5 -132t103.5 -44z" />
<glyph unicode="r" horiz-adv-x="862" d="M842 1139q59 0 96 -11l-66 -290q-45 16 -100 16q-116 0 -203.5 -91.5t-124.5 -262.5l-106 -500h-301l237 1118h230l-21 -207h6q147 228 353 228z" />
<glyph unicode="s" horiz-adv-x="969" d="M829 369q0 -188 -124.5 -288.5t-346.5 -100.5q-107 0 -186.5 15t-148.5 50v248q157 -90 319 -90q80 0 131 32.5t51 88.5q0 43 -37 77t-131 86q-121 68 -169 135.5t-48 159.5q0 170 110.5 263.5t315.5 93.5q201 0 363 -95l-99 -215q-140 84 -258 84q-57 0 -92 -25.5 t-35 -68.5q0 -39 32 -68.5t120 -74.5q123 -63 178 -137t55 -170z" />
<glyph unicode="t" horiz-adv-x="840" d="M514 223q65 0 162 35v-225q-111 -53 -266 -53q-150 0 -220.5 63t-70.5 195q0 50 12 112l115 539h-152l29 147l196 84l132 236h194l-49 -238h283l-50 -229h-282l-115 -539q-6 -30 -6 -53q0 -74 88 -74z" />
<glyph unicode="u" horiz-adv-x="1237" d="M262 1118h301l-137 -653q-16 -68 -16 -119q0 -123 108 -123q92 0 167 114t118 318l98 463h301l-237 -1118h-230l21 207h-6q-145 -227 -355 -227q-138 0 -211 82.5t-73 238.5q0 93 24 213z" />
<glyph unicode="v" horiz-adv-x="1049" d="M455 301q55 153 92 223l297 594h323l-604 -1118h-323l-138 1118h295l45 -586q7 -133 7 -231h6z" />
<glyph unicode="w" horiz-adv-x="1614" d="M856 860q-62 -178 -123 -319l-233 -541h-324l-51 1118h281l4 -495l-4 -167l-7 -171h4q6 20 14 41.5t51 136.5t46 119l231 536h328v-536q0 -142 -10 -297h6l28 80q73 208 95 258l219 495h307l-530 -1118h-330l-6 520q0 155 10 340h-6z" />
<glyph unicode="x" horiz-adv-x="1087" d="M379 573l-225 545h321l115 -334l244 334h354l-467 -561l244 -557h-326l-125 342l-264 -342h-350z" />
<glyph unicode="y" horiz-adv-x="1063" d="M102 1118h295l56 -518q14 -122 14 -293h6q20 51 44 119.5t65 153.5l260 538h327l-680 -1278q-177 -332 -483 -332q-90 0 -147 19v240q68 -13 116 -13q84 0 147.5 48t117.5 149l26 49z" />
<glyph unicode="z" horiz-adv-x="932" d="M748 0h-795l35 180l575 705h-397l51 233h750l-43 -200l-566 -685h439z" />
<glyph unicode="{" horiz-adv-x="727" d="M201 319q0 140 -209 140l45 229q122 0 192.5 41.5t92.5 138.5l61 285q38 170 131 239.5t270 69.5h84l-49 -225q-90 -2 -130.5 -34.5t-55.5 -106.5l-66 -297q-45 -207 -276 -236v-8q85 -26 126.5 -82.5t41.5 -134.5q0 -44 -15 -113l-36 -178q-7 -28 -7 -51q0 -54 33.5 -74 t91.5 -20v-226h-53q-167 0 -253.5 63.5t-86.5 184.5q0 57 14 125l39 184q15 69 15 86z" />
<glyph unicode="|" d="M455 1550h219v-2015h-219v2015z" />
<glyph unicode="}" horiz-adv-x="727" d="M256 1462q340 0 340 -248q0 -56 -14 -124l-39 -185q-15 -69 -15 -86q0 -139 209 -139l-45 -229q-122 0 -192.5 -42t-91.5 -139l-62 -284q-37 -170 -130.5 -240t-270.5 -70h-45v226q93 3 137 35.5t59 105.5l66 297q25 111 95 166t181 69v9q-168 51 -168 217q0 43 15 112 l37 179q6 30 6 51q0 54 -36.5 74t-109.5 20l41 225h33z" />
<glyph unicode="~" d="M342 672q-54 0 -116.5 -33t-116.5 -88v231q101 109 256 109q64 0 117 -14t139 -50q64 -27 111 -41t95 -14q51 0 112 30.5t122 90.5v-231q-103 -109 -256 -109q-59 0 -109 11.5t-147 51.5q-89 38 -127 47t-80 9z" />
<glyph unicode="&#xa1;" horiz-adv-x="586" d="M182 606h242l-158 -977h-340zM549 977q0 -92 -55.5 -145.5t-149.5 -53.5q-68 0 -108.5 38t-40.5 108q0 85 54 139.5t144 54.5q73 0 114.5 -37t41.5 -104z" />
<glyph unicode="&#xa2;" d="M575 -20h-188l49 210q-134 36 -203 136t-69 258q0 193 62.5 355t178 262.5t267.5 123.5l33 158h188l-35 -158q118 -14 225 -65l-92 -230q-53 23 -105 40t-118 17q-133 0 -216 -143t-83 -336q0 -96 45 -144t127 -48q75 0 140 23.5t134 58.5v-246q-136 -71 -299 -80z" />
<glyph unicode="&#xa3;" d="M872 1485q195 0 369 -86l-113 -232q-141 68 -237 68q-75 0 -123 -39.5t-68 -132.5l-47 -229h299l-45 -220h-299l-18 -84q-42 -195 -209 -270h655l-55 -260h-993l49 246q196 48 244 264l22 104h-192l45 220h192l49 247q41 197 162 300.5t313 103.5z" />
<glyph unicode="&#xa4;" d="M190 723q0 102 54 197l-129 127l147 147l127 -127q91 53 197 53q105 0 196 -55l127 129l150 -143l-129 -129q53 -89 53 -199q0 -107 -53 -199l125 -125l-146 -145l-127 125q-95 -51 -196 -51q-115 0 -199 51l-125 -123l-145 145l127 125q-54 93 -54 197zM397 723 q0 -77 54.5 -132.5t134.5 -55.5q81 0 136.5 55t55.5 133q0 80 -56.5 135t-135.5 55q-78 0 -133.5 -56t-55.5 -134z" />
<glyph unicode="&#xa5;" d="M608 872l371 590h311l-506 -747h203l-39 -178h-252l-28 -138h252l-37 -178h-252l-47 -221h-291l47 221h-252l37 178h252l29 138h-252l39 178h196l-192 747h297z" />
<glyph unicode="&#xa6;" d="M455 1550h219v-815h-219v815zM455 350h219v-815h-219v815z" />
<glyph unicode="&#xa7;" horiz-adv-x="995" d="M150 760q0 89 47.5 163t154.5 142q-42 34 -70 84.5t-28 107.5q0 149 117 234.5t313 85.5q172 0 344 -88l-82 -193q-147 84 -282 84q-144 0 -144 -106q0 -43 40.5 -76t127.5 -72q242 -106 242 -303q0 -188 -193 -303q38 -35 64 -85.5t26 -108.5q0 -161 -126 -253.5 t-345 -92.5q-204 0 -336 75v224q172 -105 345 -105q99 0 144.5 35t45.5 92q0 39 -33 72.5t-127 79.5q-117 57 -181 131t-64 176zM506 967q-51 -25 -82 -70.5t-31 -99.5t43.5 -96.5t143.5 -88.5q49 31 75.5 78.5t26.5 95.5q0 109 -176 181z" />
<glyph unicode="&#xa8;" horiz-adv-x="1135" d="M397 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM799 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xa9;" horiz-adv-x="1704" d="M932 1010q-111 0 -163 -73t-52 -214q0 -134 55.5 -203t159.5 -69q43 0 108.5 15.5t124.5 43.5v-191q-131 -57 -262 -57q-196 0 -307 122.5t-111 336.5q0 225 117.5 351t325.5 126q142 0 284 -72l-75 -174q-114 58 -205 58zM125 731q0 200 100 375t275 276t377 101 q199 0 373.5 -99t276 -275.5t101.5 -377.5q0 -199 -98.5 -373t-272.5 -276t-380 -102q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM266 731q0 -164 81.5 -305t224 -223t305.5 -82q167 0 308 83t221.5 223.5t80.5 303.5t-80.5 303.5t-222 223.5t-307.5 83 q-164 0 -306.5 -82.5t-223.5 -223.5t-81 -304z" />
<glyph unicode="&#xaa;" horiz-adv-x="772" d="M369 752q-103 0 -160 70t-57 198q0 117 46 228t123 171t177 60q120 0 180 -103h6l39 90h154l-158 -702h-154l8 92h-2q-80 -104 -202 -104zM442 903q45 0 84 41.5t65.5 120t26.5 154.5q0 106 -88 106q-73 0 -123.5 -96t-50.5 -215q0 -111 86 -111z" />
<glyph unicode="&#xab;" horiz-adv-x="1151" d="M72 569l401 463l191 -155l-279 -334l135 -350l-246 -103l-202 461v18zM559 569l402 463l190 -155l-279 -334l136 -350l-246 -103l-203 461v18z" />
<glyph unicode="&#xac;" d="M1061 248h-219v364h-733v219h952v-583z" />
<glyph unicode="&#xad;" horiz-adv-x="659" d="M41 424zM41 424l53 250h524l-53 -250h-524z" />
<glyph unicode="&#xae;" horiz-adv-x="1704" d="M1237 899q0 -86 -44 -149.5t-130 -96.5l197 -360h-254l-138 297h-67v-297h-230v874h308q173 0 265.5 -67.5t92.5 -200.5zM801 758h51q72 0 113 31t41 92q0 59 -35.5 88.5t-116.5 29.5h-53v-241zM125 731q0 200 100 375t275 276t377 101q199 0 373.5 -99t276 -275.5 t101.5 -377.5q0 -199 -98.5 -373t-272.5 -276t-380 -102q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM266 731q0 -164 81.5 -305t224 -223t305.5 -82q167 0 308 83t221.5 223.5t80.5 303.5t-80.5 303.5t-222 223.5t-307.5 83q-164 0 -306.5 -82.5t-223.5 -223.5t-81 -304z " />
<glyph unicode="&#xaf;" horiz-adv-x="1024" d="M1030 1556h-1036l45 201h1036z" />
<glyph unicode="&#xb0;" horiz-adv-x="877" d="M164 1137q0 93 46.5 173.5t127.5 126.5t172 46q93 0 173.5 -47t126.5 -127t46 -172q0 -93 -46 -173t-126 -125.5t-174 -45.5q-93 0 -173 45t-126.5 125t-46.5 174zM354 1137q0 -63 45.5 -108.5t110.5 -45.5q66 0 111 46t45 108q0 63 -45.5 110t-110.5 47t-110.5 -47.5 t-45.5 -109.5z" />
<glyph unicode="&#xb1;" d="M475 674h-366v219h366v369h219v-369h367v-219h-367v-365h-219v365zM109 0v219h952v-219h-952z" />
<glyph unicode="&#xb2;" horiz-adv-x="776" d="M707 586h-648l35 166l273 219q111 91 141 122t44.5 59t14.5 56q0 42 -25.5 62t-60.5 20q-86 0 -188 -82l-100 158q74 57 156 87t192 30q123 0 196.5 -63t73.5 -160q0 -70 -22 -123t-70 -103.5t-189 -152.5l-129 -95h347z" />
<glyph unicode="&#xb3;" horiz-adv-x="776" d="M813 1270q0 -87 -51 -145.5t-166 -88.5v-4q154 -33 154 -176q0 -131 -107 -209t-285 -78q-75 0 -145.5 15.5t-120.5 40.5v192q125 -72 254 -72q76 0 125 30.5t49 88.5q0 37 -26 62.5t-88 25.5h-127l34 160h90q84 0 132.5 28t48.5 85q0 40 -26 60t-71 20q-86 0 -188 -66 l-82 150q142 92 313 92q130 0 206.5 -55.5t76.5 -155.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1135" d="M483 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xb5;" horiz-adv-x="1249" d="M424 348q0 -60 31.5 -92.5t79.5 -32.5q90 0 162.5 106.5t117.5 319.5l98 469h301l-237 -1118h-229l18 176h-6q-117 -196 -266 -196q-51 0 -89.5 19.5t-58.5 47.5h-6q-8 -66 -21.5 -139t-82.5 -400h-304l342 1610h301l-135 -645q-16 -70 -16 -125z" />
<glyph unicode="&#xb6;" horiz-adv-x="1341" d="M1202 -260h-162v1616h-166v-1616h-161v819q-62 -18 -146 -18q-216 0 -318 125t-102 376q0 256 107.5 385t343.5 129h604v-1816z" />
<glyph unicode="&#xb7;" horiz-adv-x="584" d="M131 553zM131 695q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="420" d="M262 -250q0 -116 -83 -179t-234 -63q-86 0 -152 23v168q63 -23 125 -23q102 0 102 82q0 34 -31 56.5t-110 31.5l96 154h185l-39 -72q141 -49 141 -178z" />
<glyph unicode="&#xb9;" horiz-adv-x="776" d="M528 1462h207l-186 -876h-246l84 397q24 109 55 207q-16 -15 -80 -60l-131 -81l-102 165z" />
<glyph unicode="&#xba;" horiz-adv-x="754" d="M809 1194q0 -128 -48.5 -232.5t-132.5 -157t-196 -52.5q-134 0 -202 75t-68 211q0 197 104 319t277 122q129 0 197.5 -73.5t68.5 -211.5zM522 1315q-64 0 -107.5 -89.5t-43.5 -199.5q0 -111 80 -111q63 0 105 85.5t42 207.5q0 107 -76 107z" />
<glyph unicode="&#xbb;" horiz-adv-x="1151" d="M1079 535l-401 -463l-191 155l279 334l-135 350l246 103l202 -461v-18zM592 535l-402 -463l-190 155l279 334l-136 350l246 103l203 -461v-18z" />
<glyph unicode="&#xbc;" horiz-adv-x="1804" d="M97 0zM1500 1462l-1084 -1462h-239l1087 1462h236zM496 1462h207l-186 -876h-246l84 397q24 109 55 207q-16 -15 -80 -60l-131 -81l-102 165zM1573 152h-119l-32 -151h-238l33 151h-373l31 174l475 557h260l-121 -563h119zM1252 320l58 231l22 74q-13 -20 -43 -58 t-211 -247h174z" />
<glyph unicode="&#xbd;" horiz-adv-x="1804" d="M97 0zM1588 1h-648l35 166l273 219q111 91 141 122t44.5 59t14.5 56q0 42 -25.5 62t-60.5 20q-86 0 -188 -82l-100 158q74 57 156 87t192 30q123 0 196.5 -63t73.5 -160q0 -70 -22 -123t-70 -103.5t-189 -152.5l-129 -95h347zM496 1462h207l-186 -876h-246l84 397 q24 109 55 207q-16 -15 -80 -60l-131 -81l-102 165zM1500 1462l-1084 -1462h-239l1087 1462h236z" />
<glyph unicode="&#xbe;" horiz-adv-x="1804" d="M133 0zM1633 1462l-1084 -1462h-239l1087 1462h236zM1634 152h-119l-32 -151h-238l33 151h-373l31 174l475 557h260l-121 -563h119zM1313 320l58 231l22 74q-13 -20 -43 -58t-211 -247h174zM854 1270q0 -87 -51 -145.5t-166 -88.5v-4q154 -33 154 -176q0 -131 -107 -209 t-285 -78q-75 0 -145.5 15.5t-120.5 40.5v192q125 -72 254 -72q76 0 125 30.5t49 88.5q0 37 -26 62.5t-88 25.5h-127l34 160h90q84 0 132.5 28t48.5 85q0 40 -26 60t-71 20q-86 0 -188 -66l-82 150q142 92 313 92q130 0 206.5 -55.5t76.5 -155.5z" />
<glyph unicode="&#xbf;" horiz-adv-x="940" d="M678 606l-14 -78q-19 -105 -76.5 -180t-169.5 -151q-122 -83 -156.5 -126t-34.5 -98q0 -118 133 -118q50 0 106.5 16t201.5 84l92 -221q-221 -125 -445 -125q-177 0 -280 87.5t-103 244.5q0 82 28.5 148.5t83.5 124t189 146.5q93 62 128 106.5t51 106.5l8 33h258z M772 977q0 -92 -55.5 -145.5t-149.5 -53.5q-68 0 -108.5 38t-40.5 108q0 86 54.5 140t143.5 54q73 0 114.5 -37t41.5 -104z" />
<glyph unicode="&#xc0;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM965 1579h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xc1;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM735 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xc2;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM1235 1579h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z " />
<glyph unicode="&#xc3;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM999 1579q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5 t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xc4;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM516 1720q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM918 1720q0 78 42 118t120 40 q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xc5;" horiz-adv-x="1286" d="M0 0zM842 348h-473l-172 -348h-320l766 1468h373l147 -1468h-297zM827 608l-26 350q-10 131 -10 253v36q-44 -120 -109 -254l-188 -385h333zM1087 1567q0 -107 -70 -173.5t-184 -66.5q-110 0 -179 63.5t-69 174.5q0 109 68.5 173t179.5 64q110 0 182 -65t72 -170z M930 1565q0 45 -27.5 70.5t-69.5 25.5t-69 -25.5t-27 -70.5t24 -71t72 -26q42 0 69.5 26t27.5 71z" />
<glyph unicode="&#xc6;" horiz-adv-x="1833" d="M1593 0h-817l74 348h-426l-219 -348h-328l922 1462h1104l-54 -254h-512l-67 -321h477l-55 -254h-478l-79 -377h512zM905 608l127 600h-80l-364 -600h317z" />
<glyph unicode="&#xc7;" horiz-adv-x="1253" d="M123 0zM905 1227q-132 0 -237.5 -81t-169.5 -238.5t-64 -338.5q0 -167 68.5 -248t218.5 -81q146 0 338 77v-260q-199 -77 -400 -77q-254 0 -395 149.5t-141 423.5q0 262 104 482.5t278 335t400 114.5q125 0 222 -22.5t208 -82.5l-118 -250q-106 59 -175 78t-137 19z M825 -250q0 -116 -83 -179t-234 -63q-86 0 -152 23v168q63 -23 125 -23q102 0 102 82q0 34 -31 56.5t-110 31.5l96 154h185l-39 -72q141 -49 141 -178z" />
<glyph unicode="&#xc8;" horiz-adv-x="1110" d="M53 0zM870 0h-817l309 1462h818l-54 -254h-512l-67 -321h477l-55 -254h-477l-80 -377h512zM906 1579h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xc9;" horiz-adv-x="1110" d="M53 0zM870 0h-817l309 1462h818l-54 -254h-512l-67 -321h477l-55 -254h-477l-80 -377h512zM608 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xca;" horiz-adv-x="1110" d="M53 0zM870 0h-817l309 1462h818l-54 -254h-512l-67 -321h477l-55 -254h-477l-80 -377h512zM1177 1579h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xcb;" horiz-adv-x="1110" d="M53 0zM870 0h-817l309 1462h818l-54 -254h-512l-67 -321h477l-55 -254h-477l-80 -377h512zM438 1720q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM840 1720q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5 t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xcc;" horiz-adv-x="659" d="M53 0zM53 0l312 1462h305l-312 -1462h-305zM667 1579h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xcd;" horiz-adv-x="659" d="M53 0zM53 0l312 1462h305l-312 -1462h-305zM414 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xce;" horiz-adv-x="659" d="M53 0zM53 0l312 1462h305l-312 -1462h-305zM937 1579h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xcf;" horiz-adv-x="659" d="M53 0zM53 0l312 1462h305l-312 -1462h-305zM222 1720q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM624 1720q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xd0;" horiz-adv-x="1386" d="M1323 909q0 -280 -98 -486.5t-283.5 -314.5t-437.5 -108h-451l125 596h-141l55 254h139l131 612h396q270 0 417.5 -143t147.5 -410zM518 256q148 0 258 76t172 223.5t62 337.5q0 154 -72.5 234.5t-208.5 80.5h-115l-75 -358h237l-55 -254h-238l-71 -340h106z" />
<glyph unicode="&#xd1;" horiz-adv-x="1546" d="M53 0zM1247 0h-342l-356 1106h-6l-4 -32q-32 -216 -66 -386l-145 -688h-275l309 1462h357l340 -1077h4q12 76 39 217t180 860h274zM1114 1579q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36 t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xd2;" horiz-adv-x="1495" d="M123 0zM1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246 t56 357q0 142 -65 219.5t-183 77.5zM1053 1579h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xd3;" horiz-adv-x="1495" d="M123 0zM1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246 t56 357q0 142 -65 219.5t-183 77.5zM753 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xd4;" horiz-adv-x="1495" d="M123 0zM1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246 t56 357q0 142 -65 219.5t-183 77.5zM1308 1579h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xd5;" horiz-adv-x="1495" d="M123 0zM1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246 t56 357q0 142 -65 219.5t-183 77.5zM1071 1579q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xd6;" horiz-adv-x="1495" d="M123 0zM1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q255 0 395 -144t140 -403zM872 1227q-121 0 -222 -91.5t-158.5 -251.5t-57.5 -347q0 -147 66.5 -222t187.5 -75t220.5 87t155.5 246 t56 357q0 142 -65 219.5t-183 77.5zM585 1720q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM987 1720q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xd7;" d="M428 723l-299 301l152 154l301 -299l305 299l153 -150l-305 -305l301 -303l-149 -152l-305 301l-301 -299l-150 152z" />
<glyph unicode="&#xd8;" horiz-adv-x="1495" d="M1432 938q0 -283 -99 -506.5t-271 -337.5t-396 -114q-180 0 -304 71l-108 -137l-154 115l121 151q-98 138 -98 357q0 265 99 487.5t273 341.5t402 119q182 0 305 -76l105 131l151 -117l-117 -145q91 -134 91 -340zM870 1233q-126 0 -229 -91.5t-160 -252.5t-57 -352 q0 -32 8 -101l596 754q-69 43 -158 43zM1133 930l-5 80l-589 -740q59 -37 153 -37q124 0 226 89t158.5 247.5t56.5 360.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1415" d="M141 0zM1434 1462l-201 -946q-57 -266 -218 -401t-419 -135q-212 0 -333.5 113.5t-121.5 307.5q0 72 15 138l196 923h305l-194 -919q-17 -74 -17 -125q0 -178 189 -178q123 0 195 76.5t104 228.5l194 917h306zM1002 1579h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311 q36 -148 115 -303v-25z" />
<glyph unicode="&#xda;" horiz-adv-x="1415" d="M141 0zM1434 1462l-201 -946q-57 -266 -218 -401t-419 -135q-212 0 -333.5 113.5t-121.5 307.5q0 72 15 138l196 923h305l-194 -919q-17 -74 -17 -125q0 -178 189 -178q123 0 195 76.5t104 228.5l194 917h306zM757 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5 t-194 -158.5h-209v25z" />
<glyph unicode="&#xdb;" horiz-adv-x="1415" d="M141 0zM1434 1462l-201 -946q-57 -266 -218 -401t-419 -135q-212 0 -333.5 113.5t-121.5 307.5q0 72 15 138l196 923h305l-194 -919q-17 -74 -17 -125q0 -178 189 -178q123 0 195 76.5t104 228.5l194 917h306zM1284 1579h-198q-63 53 -162 168q-105 -88 -232 -168h-217 v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xdc;" horiz-adv-x="1415" d="M141 0zM1434 1462l-201 -946q-57 -266 -218 -401t-419 -135q-212 0 -333.5 113.5t-121.5 307.5q0 72 15 138l196 923h305l-194 -919q-17 -74 -17 -125q0 -178 189 -178q123 0 195 76.5t104 228.5l194 917h306zM565 1720q0 78 42.5 118t119.5 40q133 0 133 -108 q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM967 1720q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xdd;" horiz-adv-x="1155" d="M186 0zM627 870l374 592h342l-618 -903l-119 -559h-303l119 559l-236 903h312zM606 1604q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xde;" horiz-adv-x="1188" d="M1143 807q0 -243 -170.5 -378.5t-466.5 -135.5h-86l-62 -293h-305l309 1462h306l-50 -229h35q242 0 366 -106.5t124 -319.5zM475 547h55q139 0 222.5 66.5t83.5 185.5q0 180 -195 180h-74z" />
<glyph unicode="&#xdf;" horiz-adv-x="1350" d="M846 1567q208 0 331 -90t123 -240q0 -114 -49 -192t-178 -152q-73 -42 -96 -68.5t-23 -54.5q0 -23 22 -49.5t79 -69.5q107 -83 144.5 -150.5t37.5 -150.5q0 -170 -123.5 -270t-337.5 -100q-187 0 -297 61v240q128 -78 258 -78q101 0 148 33t47 86q0 40 -26.5 75 t-108.5 97q-94 72 -129 130t-35 126q0 84 45 145t162 127q66 37 104.5 76t38.5 96q0 62 -39.5 98.5t-124.5 36.5q-96 0 -156 -51.5t-85 -171.5l-254 -1219q-43 -198 -147 -288.5t-277 -90.5q-90 0 -160 25v242q61 -21 115 -21q133 0 170 178l254 1207q47 224 182 326 t385 102z" />
<glyph unicode="&#xe0;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM869 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xe1;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM598 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xe2;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM1120 1240h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xe3;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM884 1241q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xe4;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM397 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM799 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xe5;" horiz-adv-x="1217" d="M90 0zM406 -20q-147 0 -231.5 106.5t-84.5 298.5q0 198 72 377.5t189 278t257 98.5q97 0 167.5 -42t109.5 -122h8l57 143h232l-238 -1118h-229l14 145h-4q-134 -165 -319 -165zM524 223q69 0 133 67t103 181.5t39 259.5q0 71 -38.5 117.5t-101.5 46.5q-68 0 -129.5 -72 t-98 -190t-36.5 -234q0 -88 33.5 -132t95.5 -44zM1023 1479q0 -107 -70 -173.5t-184 -66.5q-110 0 -179 63.5t-69 174.5q0 109 68.5 173t179.5 64q110 0 182 -65t72 -170zM866 1477q0 45 -27.5 70.5t-69.5 25.5t-69 -25.5t-27 -70.5t24 -71t72 -26q42 0 69.5 26t27.5 71z " />
<glyph unicode="&#xe6;" horiz-adv-x="1786" d="M1206 -20q-109 0 -179.5 27t-117.5 87l-16 -94h-188l14 145h-6q-71 -88 -146.5 -126.5t-167.5 -38.5q-146 0 -227.5 109t-81.5 296q0 200 68.5 375.5t185 277t258.5 101.5q96 0 160.5 -38.5t114.5 -125.5h6l57 143h188l-18 -90q44 49 120.5 80t168.5 31 q157 0 246.5 -83.5t89.5 -221.5q0 -187 -167 -288.5t-476 -101.5h-52l-2 -19v-19q0 -96 55.5 -147.5t159.5 -51.5q66 0 152 23t162 63v-227q-179 -86 -361 -86zM518 223q72 0 134 68t99 184.5t37 243.5q0 80 -33 128t-102 48q-68 0 -128 -69t-95 -185.5t-35 -241.5 q0 -84 32.5 -130t90.5 -46zM1341 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95z" />
<glyph unicode="&#xe7;" horiz-adv-x="989" d="M90 0zM506 -20q-201 0 -308.5 107.5t-107.5 303.5q0 212 74.5 385.5t209.5 268t308 94.5q182 0 328 -72l-92 -229q-54 23 -106 40t-118 17q-85 0 -153.5 -64t-107 -175.5t-38.5 -239.5q0 -96 45.5 -144.5t126.5 -48.5q76 0 141 23.5t134 58.5v-246q-152 -79 -336 -79z M653 -250q0 -116 -83 -179t-234 -63q-86 0 -152 23v168q63 -23 125 -23q102 0 102 82q0 34 -31 56.5t-110 31.5l96 154h185l-39 -72q141 -49 141 -178z" />
<glyph unicode="&#xe8;" horiz-adv-x="1141" d="M90 0zM696 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95zM532 -20q-210 0 -326 113t-116 319q0 207 82.5 377.5t223.5 260t319 89.5q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-477 -101.5h-51l-2 -21v-20q0 -91 51.5 -143.5 t147.5 -52.5q87 0 158 19t172 67v-227q-172 -86 -390 -86zM849 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xe9;" horiz-adv-x="1141" d="M90 0zM696 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95zM532 -20q-210 0 -326 113t-116 319q0 207 82.5 377.5t223.5 260t319 89.5q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-477 -101.5h-51l-2 -21v-20q0 -91 51.5 -143.5 t147.5 -52.5q87 0 158 19t172 67v-227q-172 -86 -390 -86zM528 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xea;" horiz-adv-x="1141" d="M90 0zM696 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95zM532 -20q-210 0 -326 113t-116 319q0 207 82.5 377.5t223.5 260t319 89.5q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-477 -101.5h-51l-2 -21v-20q0 -91 51.5 -143.5 t147.5 -52.5q87 0 158 19t172 67v-227q-172 -86 -390 -86zM1101 1241h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xeb;" horiz-adv-x="1141" d="M90 0zM696 922q-88 0 -166 -80t-102 -195h45q155 0 241.5 48.5t86.5 131.5q0 95 -105 95zM532 -20q-210 0 -326 113t-116 319q0 207 82.5 377.5t223.5 260t319 89.5q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-477 -101.5h-51l-2 -21v-20q0 -91 51.5 -143.5 t147.5 -52.5q87 0 158 19t172 67v-227q-172 -86 -390 -86zM365 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM767 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xec;" horiz-adv-x="608" d="M37 0zM338 0h-301l237 1118h301zM579 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xed;" horiz-adv-x="608" d="M37 0zM338 0h-301l237 1118h301zM291 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xee;" horiz-adv-x="608" d="M36 0zM338 0h-301l237 1118h301zM845 1241h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xef;" horiz-adv-x="608" d="M37 0zM338 0h-301l237 1118h301zM126 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM528 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xf0;" horiz-adv-x="1182" d="M618 1309q-34 34 -124 80l118 186q134 -61 232 -139l237 131l76 -152l-192 -106q81 -107 113 -235t32 -279q0 -249 -69.5 -432.5t-203.5 -283t-323 -99.5q-216 0 -329 110t-113 316q0 165 64.5 301t180.5 212t265 76q83 0 151.5 -31t114.5 -94h6q-20 213 -117 310 l-231 -131l-88 147zM528 205q66 0 122.5 55.5t89 148.5t32.5 193q0 77 -38.5 122.5t-108.5 45.5q-73 0 -130 -53t-88.5 -143t-31.5 -197q0 -81 39 -126.5t114 -45.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1237" d="M37 0zM977 0h-301l137 653q16 68 16 119q0 123 -108 123q-92 0 -167 -114t-118 -318l-98 -463h-301l237 1118h230l-21 -207h6q146 228 355 228q138 0 213.5 -83.5t75.5 -238.5q0 -73 -23 -180zM909 1241q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28 t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xf2;" horiz-adv-x="1198" d="M90 0zM805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5 q196 0 310 -118t114 -325zM845 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311q36 -148 115 -303v-25z" />
<glyph unicode="&#xf3;" horiz-adv-x="1198" d="M90 0zM805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5 q196 0 310 -118t114 -325zM571 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xf4;" horiz-adv-x="1198" d="M90 0zM805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5 q196 0 310 -118t114 -325zM1109 1241h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xf5;" horiz-adv-x="1198" d="M90 0zM805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5 q196 0 310 -118t114 -325zM865 1241q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#xf6;" horiz-adv-x="1198" d="M90 0zM805 696q0 197 -143 197q-75 0 -134.5 -61t-97 -179t-37.5 -243q0 -185 150 -185q75 0 135 61.5t93.5 171t33.5 238.5zM1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-195 0 -311.5 117.5t-116.5 312.5q0 213 71.5 379.5t206.5 258t316 91.5 q196 0 310 -118t114 -325zM386 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM788 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xf7;" d="M109 612v219h952v-219h-952zM444 373q0 76 37 113.5t103 37.5t102.5 -39t36.5 -112q0 -70 -37 -111t-102 -41t-102.5 39t-37.5 113zM444 1071q0 75 37 113.5t103 38.5q67 0 103 -40.5t36 -111.5q0 -70 -37 -110.5t-102 -40.5t-102.5 39t-37.5 112z" />
<glyph unicode="&#xf8;" horiz-adv-x="1198" d="M1108 696q0 -211 -70.5 -374t-203.5 -252.5t-316 -89.5q-123 0 -225 53l-109 -135l-141 108l119 148q-72 107 -72 256q0 213 71.5 379.5t206.5 258t316 91.5q131 0 227 -56l70 88l145 -110l-84 -105q66 -107 66 -260zM662 903q-81 0 -144.5 -62.5t-98 -169.5t-34.5 -233 v-12l365 453q-35 24 -88 24zM543 215q114 0 193 133t79 318v16l-358 -444q11 -8 35.5 -15.5t50.5 -7.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1237" d="M111 0zM262 1118h301l-137 -653q-16 -68 -16 -119q0 -123 108 -123q92 0 167 114t118 318l98 463h301l-237 -1118h-230l21 207h-6q-145 -227 -355 -227q-138 0 -211 82.5t-73 238.5q0 93 24 213zM845 1241h-184q-71 69 -138.5 153.5t-103.5 153.5v21h311 q36 -148 115 -303v-25z" />
<glyph unicode="&#xfa;" horiz-adv-x="1237" d="M111 0zM262 1118h301l-137 -653q-16 -68 -16 -119q0 -123 108 -123q92 0 167 114t118 318l98 463h301l-237 -1118h-230l21 207h-6q-145 -227 -355 -227q-138 0 -211 82.5t-73 238.5q0 93 24 213zM610 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209 v25z" />
<glyph unicode="&#xfb;" horiz-adv-x="1237" d="M111 0zM262 1118h301l-137 -653q-16 -68 -16 -119q0 -123 108 -123q92 0 167 114t118 318l98 463h301l-237 -1118h-230l21 207h-6q-145 -227 -355 -227q-138 0 -211 82.5t-73 238.5q0 93 24 213zM1143 1241h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25 q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#xfc;" horiz-adv-x="1237" d="M111 0zM262 1118h301l-137 -653q-16 -68 -16 -119q0 -123 108 -123q92 0 167 114t118 318l98 463h301l-237 -1118h-230l21 207h-6q-145 -227 -355 -227q-138 0 -211 82.5t-73 238.5q0 93 24 213zM411 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5 t-121 -43.5q-135 0 -135 110zM813 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#xfd;" horiz-adv-x="1063" d="M0 0zM102 1118h295l56 -518q14 -122 14 -293h6q20 51 44 119.5t65 153.5l260 538h327l-680 -1278q-177 -332 -483 -332q-90 0 -147 19v240q68 -13 116 -13q84 0 147.5 48t117.5 149l26 49zM497 1266q79 88 222 303h335v-17q-46 -56 -154 -152.5t-194 -158.5h-209v25z" />
<glyph unicode="&#xfe;" horiz-adv-x="1219" d="M813 1139q150 0 232.5 -106.5t82.5 -301.5q0 -199 -69 -381t-182 -276t-250 -94q-178 0 -271 163h-8q-12 -159 -43 -295l-72 -340h-301l435 2048h301l-66 -307q-29 -131 -80 -280h8q131 170 283 170zM682 895q-71 0 -130 -65t-95.5 -184.5t-36.5 -246.5q0 -80 33.5 -128 t105.5 -48q69 0 129 65t97.5 183.5t37.5 247.5q0 88 -37.5 132t-103.5 44z" />
<glyph unicode="&#xff;" horiz-adv-x="1063" d="M0 0zM102 1118h295l56 -518q14 -122 14 -293h6q20 51 44 119.5t65 153.5l260 538h327l-680 -1278q-177 -332 -483 -332q-90 0 -147 19v240q68 -13 116 -13q84 0 147.5 48t117.5 149l26 49zM310 1382q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5 q-135 0 -135 110zM712 1382q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5q-135 0 -135 110z" />
<glyph unicode="&#x131;" horiz-adv-x="608" d="M338 0h-301l237 1118h301z" />
<glyph unicode="&#x152;" horiz-adv-x="1845" d="M1606 0h-760q-93 -20 -180 -20q-256 0 -399.5 147.5t-143.5 409.5q0 265 99 487.5t273 341.5t402 119q140 0 209 -23h809l-53 -254h-512l-68 -321h477l-55 -254h-477l-80 -377h512zM688 240q88 0 158 32l194 916q-62 39 -168 39q-121 0 -222 -91.5t-158.5 -251.5 t-57.5 -347q0 -147 66.5 -222t187.5 -75z" />
<glyph unicode="&#x153;" horiz-adv-x="1806" d="M1198 -20q-116 0 -208 38.5t-138 106.5q-63 -68 -147 -106.5t-207 -38.5q-187 0 -297.5 117t-110.5 317q0 216 69 380.5t200 254.5t309 90q209 0 313 -160q154 160 399 160q177 0 276 -81.5t99 -223.5q0 -187 -167 -288.5t-476 -101.5h-51l-2 -21v-20q0 -91 51 -143.5 t147 -52.5q87 0 158 19t172 67v-227q-93 -46 -185.5 -66t-203.5 -20zM645 893q-71 0 -127 -60.5t-90.5 -176.5t-34.5 -242q0 -91 36.5 -140t109.5 -49q109 0 179 134.5t70 336.5q0 96 -37 146.5t-106 50.5zM1362 922q-88 0 -165.5 -78.5t-102.5 -196.5h45q155 0 241 48.5 t86 131.5q0 95 -104 95z" />
<glyph unicode="&#x178;" horiz-adv-x="1155" d="M186 0zM627 870l374 592h342l-618 -903l-119 -559h-303l119 559l-236 903h312zM432 1720q0 78 42.5 118t119.5 40q133 0 133 -108q0 -73 -39 -116.5t-121 -43.5q-135 0 -135 110zM834 1720q0 78 42 118t120 40q65 0 99 -28t34 -80q0 -73 -39.5 -116.5t-120.5 -43.5 q-135 0 -135 110z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1135" d="M1120 1241h-198q-63 53 -162 168q-105 -88 -232 -168h-217v25q63 57 153 147t142 156h338q22 -54 74 -142.5t102 -160.5v-25z" />
<glyph unicode="&#x2da;" horiz-adv-x="1182" d="M1034 1479q0 -107 -70 -173.5t-184 -66.5q-110 0 -179 63.5t-69 174.5q0 109 68.5 173t179.5 64q110 0 182 -65t72 -170zM877 1477q0 45 -27.5 70.5t-69.5 25.5t-69 -25.5t-27 -70.5t24 -71t72 -26q42 0 69.5 26t27.5 71z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1135" d="M866 1241q-49 0 -86.5 16.5t-69.5 36t-61.5 36t-62.5 16.5q-31 0 -55.5 -28t-38.5 -79h-177q59 309 281 309q49 0 87.5 -16.5t71.5 -36t62 -35.5t60 -16q34 0 58 25.5t46 80.5h172q-66 -309 -287 -309z" />
<glyph unicode="&#x2000;" horiz-adv-x="953" />
<glyph unicode="&#x2001;" horiz-adv-x="1907" />
<glyph unicode="&#x2002;" horiz-adv-x="953" />
<glyph unicode="&#x2003;" horiz-adv-x="1907" />
<glyph unicode="&#x2004;" horiz-adv-x="635" />
<glyph unicode="&#x2005;" horiz-adv-x="476" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="659" d="M41 424l53 250h524l-53 -250h-524z" />
<glyph unicode="&#x2011;" horiz-adv-x="659" d="M41 424l53 250h524l-53 -250h-524z" />
<glyph unicode="&#x2012;" horiz-adv-x="659" d="M41 424l53 250h524l-53 -250h-524z" />
<glyph unicode="&#x2013;" horiz-adv-x="983" d="M41 436l49 230h852l-49 -230h-852z" />
<glyph unicode="&#x2014;" horiz-adv-x="1966" d="M41 436l49 230h1835l-49 -230h-1835z" />
<glyph unicode="&#x2018;" horiz-adv-x="440" d="M123 961l-8 22q103 227 262 479h225q-91 -213 -194 -501h-285z" />
<glyph unicode="&#x2019;" horiz-adv-x="440" d="M586 1462l8 -22q-103 -227 -262 -479h-226q89 206 195 501h285z" />
<glyph unicode="&#x201a;" horiz-adv-x="569" d="M377 238l8 -23q-103 -227 -262 -479h-225q88 207 194 502h285z" />
<glyph unicode="&#x201c;" horiz-adv-x="887" d="M569 961l-8 22q103 227 262 479h226q-97 -227 -195 -501h-285zM123 961l-8 22q103 227 262 479h225q-91 -213 -194 -501h-285z" />
<glyph unicode="&#x201d;" horiz-adv-x="887" d="M586 1462l8 -22q-103 -227 -262 -479h-226q89 206 195 501h285zM1032 1462l8 -22q-103 -227 -262 -479h-225q23 53 46.5 111t148.5 390h284z" />
<glyph unicode="&#x201e;" horiz-adv-x="1018" d="M377 238l8 -23q-103 -227 -262 -479h-225q88 207 194 502h285zM825 238l9 -23q-100 -221 -263 -479h-225q24 57 49 118.5t146 383.5h284z" />
<glyph unicode="&#x2022;" horiz-adv-x="739" d="M104 686q0 106 42.5 194t120 136.5t182.5 48.5q120 0 182.5 -67t62.5 -191q0 -177 -91.5 -277t-248.5 -100q-117 0 -183.5 67t-66.5 189z" />
<glyph unicode="&#x2026;" horiz-adv-x="1706" d="M25 0zM25 115q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5zM586 115q0 90 53.5 144t150.5 54q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5zM1147 115q0 90 53.5 144t150.5 54 q68 0 109 -38t41 -107q0 -87 -55 -141t-144 -54q-73 0 -114 37.5t-41 104.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="664" d="M72 569l401 463l191 -155l-279 -334l135 -350l-246 -103l-202 461v18z" />
<glyph unicode="&#x203a;" horiz-adv-x="664" d="M592 535l-402 -463l-190 155l279 334l-136 350l246 103l203 -461v-18z" />
<glyph unicode="&#x2044;" horiz-adv-x="256" d="M791 1462l-1084 -1462h-239l1087 1462h236z" />
<glyph unicode="&#x205f;" horiz-adv-x="476" />
<glyph unicode="&#x2074;" horiz-adv-x="776" d="M776 737h-119l-32 -151h-238l33 151h-373l31 174l475 557h260l-121 -563h119zM455 905l58 231l22 74q-13 -20 -43 -58t-211 -247h174z" />
<glyph unicode="&#x20ac;" d="M899 1237q-97 0 -176 -74.5t-135 -212.5h348l-39 -176h-360q-11 -34 -25 -115h299l-37 -178h-280q0 -120 44.5 -181.5t147.5 -61.5q133 0 283 63v-258q-126 -63 -330 -63q-446 0 -446 501h-152l37 178h127q9 67 22 115h-125l39 176h135q87 252 250.5 393.5t374.5 141.5 q100 0 179 -23t165 -80l-125 -223q-87 49 -131 63.5t-90 14.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="1534" d="M471 741h-168v572h-197v149h564v-149h-199v-572zM1047 741l-166 529h-7l5 -111v-418h-164v721h248l159 -510l170 510h240v-721h-168v408l4 121h-6l-174 -529h-141z" />
<glyph unicode="&#xe000;" horiz-adv-x="1120" d="M0 1120h1120v-1120h-1120v1120z" />
<glyph horiz-adv-x="1217" d="M0 0z" />
<hkern u1="&#x22;" u2="&#x178;" k="-20" />
<hkern u1="&#x22;" u2="&#x153;" k="123" />
<hkern u1="&#x22;" u2="&#xfc;" k="61" />
<hkern u1="&#x22;" u2="&#xfb;" k="61" />
<hkern u1="&#x22;" u2="&#xfa;" k="61" />
<hkern u1="&#x22;" u2="&#xf9;" k="61" />
<hkern u1="&#x22;" u2="&#xf8;" k="123" />
<hkern u1="&#x22;" u2="&#xf6;" k="123" />
<hkern u1="&#x22;" u2="&#xf5;" k="123" />
<hkern u1="&#x22;" u2="&#xf4;" k="123" />
<hkern u1="&#x22;" u2="&#xf3;" k="123" />
<hkern u1="&#x22;" u2="&#xf2;" k="123" />
<hkern u1="&#x22;" u2="&#xeb;" k="123" />
<hkern u1="&#x22;" u2="&#xea;" k="123" />
<hkern u1="&#x22;" u2="&#xe9;" k="123" />
<hkern u1="&#x22;" u2="&#xe8;" k="123" />
<hkern u1="&#x22;" u2="&#xe7;" k="123" />
<hkern u1="&#x22;" u2="&#xe6;" k="82" />
<hkern u1="&#x22;" u2="&#xe5;" k="82" />
<hkern u1="&#x22;" u2="&#xe4;" k="82" />
<hkern u1="&#x22;" u2="&#xe3;" k="82" />
<hkern u1="&#x22;" u2="&#xe2;" k="82" />
<hkern u1="&#x22;" u2="&#xe1;" k="82" />
<hkern u1="&#x22;" u2="&#xe0;" k="123" />
<hkern u1="&#x22;" u2="&#xdd;" k="-20" />
<hkern u1="&#x22;" u2="&#xc5;" k="143" />
<hkern u1="&#x22;" u2="&#xc4;" k="143" />
<hkern u1="&#x22;" u2="&#xc3;" k="143" />
<hkern u1="&#x22;" u2="&#xc2;" k="143" />
<hkern u1="&#x22;" u2="&#xc1;" k="143" />
<hkern u1="&#x22;" u2="&#xc0;" k="143" />
<hkern u1="&#x22;" u2="u" k="61" />
<hkern u1="&#x22;" u2="s" k="61" />
<hkern u1="&#x22;" u2="r" k="61" />
<hkern u1="&#x22;" u2="q" k="123" />
<hkern u1="&#x22;" u2="p" k="61" />
<hkern u1="&#x22;" u2="o" k="123" />
<hkern u1="&#x22;" u2="n" k="61" />
<hkern u1="&#x22;" u2="m" k="61" />
<hkern u1="&#x22;" u2="g" k="61" />
<hkern u1="&#x22;" u2="e" k="123" />
<hkern u1="&#x22;" u2="d" k="123" />
<hkern u1="&#x22;" u2="c" k="123" />
<hkern u1="&#x22;" u2="a" k="82" />
<hkern u1="&#x22;" u2="Y" k="-20" />
<hkern u1="&#x22;" u2="W" k="-41" />
<hkern u1="&#x22;" u2="V" k="-41" />
<hkern u1="&#x22;" u2="T" k="-41" />
<hkern u1="&#x22;" u2="A" k="143" />
<hkern u1="&#x27;" u2="&#x178;" k="-20" />
<hkern u1="&#x27;" u2="&#x153;" k="123" />
<hkern u1="&#x27;" u2="&#xfc;" k="61" />
<hkern u1="&#x27;" u2="&#xfb;" k="61" />
<hkern u1="&#x27;" u2="&#xfa;" k="61" />
<hkern u1="&#x27;" u2="&#xf9;" k="61" />
<hkern u1="&#x27;" u2="&#xf8;" k="123" />
<hkern u1="&#x27;" u2="&#xf6;" k="123" />
<hkern u1="&#x27;" u2="&#xf5;" k="123" />
<hkern u1="&#x27;" u2="&#xf4;" k="123" />
<hkern u1="&#x27;" u2="&#xf3;" k="123" />
<hkern u1="&#x27;" u2="&#xf2;" k="123" />
<hkern u1="&#x27;" u2="&#xeb;" k="123" />
<hkern u1="&#x27;" u2="&#xea;" k="123" />
<hkern u1="&#x27;" u2="&#xe9;" k="123" />
<hkern u1="&#x27;" u2="&#xe8;" k="123" />
<hkern u1="&#x27;" u2="&#xe7;" k="123" />
<hkern u1="&#x27;" u2="&#xe6;" k="82" />
<hkern u1="&#x27;" u2="&#xe5;" k="82" />
<hkern u1="&#x27;" u2="&#xe4;" k="82" />
<hkern u1="&#x27;" u2="&#xe3;" k="82" />
<hkern u1="&#x27;" u2="&#xe2;" k="82" />
<hkern u1="&#x27;" u2="&#xe1;" k="82" />
<hkern u1="&#x27;" u2="&#xe0;" k="123" />
<hkern u1="&#x27;" u2="&#xdd;" k="-20" />
<hkern u1="&#x27;" u2="&#xc5;" k="143" />
<hkern u1="&#x27;" u2="&#xc4;" k="143" />
<hkern u1="&#x27;" u2="&#xc3;" k="143" />
<hkern u1="&#x27;" u2="&#xc2;" k="143" />
<hkern u1="&#x27;" u2="&#xc1;" k="143" />
<hkern u1="&#x27;" u2="&#xc0;" k="143" />
<hkern u1="&#x27;" u2="u" k="61" />
<hkern u1="&#x27;" u2="s" k="61" />
<hkern u1="&#x27;" u2="r" k="61" />
<hkern u1="&#x27;" u2="q" k="123" />
<hkern u1="&#x27;" u2="p" k="61" />
<hkern u1="&#x27;" u2="o" k="123" />
<hkern u1="&#x27;" u2="n" k="61" />
<hkern u1="&#x27;" u2="m" k="61" />
<hkern u1="&#x27;" u2="g" k="61" />
<hkern u1="&#x27;" u2="e" k="123" />
<hkern u1="&#x27;" u2="d" k="123" />
<hkern u1="&#x27;" u2="c" k="123" />
<hkern u1="&#x27;" u2="a" k="82" />
<hkern u1="&#x27;" u2="Y" k="-20" />
<hkern u1="&#x27;" u2="W" k="-41" />
<hkern u1="&#x27;" u2="V" k="-41" />
<hkern u1="&#x27;" u2="T" k="-41" />
<hkern u1="&#x27;" u2="A" k="143" />
<hkern u1="&#x28;" u2="J" k="-184" />
<hkern u1="&#x2c;" u2="&#x178;" k="123" />
<hkern u1="&#x2c;" u2="&#x152;" k="102" />
<hkern u1="&#x2c;" u2="&#xdd;" k="123" />
<hkern u1="&#x2c;" u2="&#xdc;" k="41" />
<hkern u1="&#x2c;" u2="&#xdb;" k="41" />
<hkern u1="&#x2c;" u2="&#xda;" k="41" />
<hkern u1="&#x2c;" u2="&#xd9;" k="41" />
<hkern u1="&#x2c;" u2="&#xd8;" k="102" />
<hkern u1="&#x2c;" u2="&#xd6;" k="102" />
<hkern u1="&#x2c;" u2="&#xd5;" k="102" />
<hkern u1="&#x2c;" u2="&#xd4;" k="102" />
<hkern u1="&#x2c;" u2="&#xd3;" k="102" />
<hkern u1="&#x2c;" u2="&#xd2;" k="102" />
<hkern u1="&#x2c;" u2="&#xc7;" k="102" />
<hkern u1="&#x2c;" u2="Y" k="123" />
<hkern u1="&#x2c;" u2="W" k="123" />
<hkern u1="&#x2c;" u2="V" k="123" />
<hkern u1="&#x2c;" u2="U" k="41" />
<hkern u1="&#x2c;" u2="T" k="143" />
<hkern u1="&#x2c;" u2="Q" k="102" />
<hkern u1="&#x2c;" u2="O" k="102" />
<hkern u1="&#x2c;" u2="G" k="102" />
<hkern u1="&#x2c;" u2="C" k="102" />
<hkern u1="&#x2d;" u2="T" k="82" />
<hkern u1="&#x2e;" u2="&#x178;" k="123" />
<hkern u1="&#x2e;" u2="&#x152;" k="102" />
<hkern u1="&#x2e;" u2="&#xdd;" k="123" />
<hkern u1="&#x2e;" u2="&#xdc;" k="41" />
<hkern u1="&#x2e;" u2="&#xdb;" k="41" />
<hkern u1="&#x2e;" u2="&#xda;" k="41" />
<hkern u1="&#x2e;" u2="&#xd9;" k="41" />
<hkern u1="&#x2e;" u2="&#xd8;" k="102" />
<hkern u1="&#x2e;" u2="&#xd6;" k="102" />
<hkern u1="&#x2e;" u2="&#xd5;" k="102" />
<hkern u1="&#x2e;" u2="&#xd4;" k="102" />
<hkern u1="&#x2e;" u2="&#xd3;" k="102" />
<hkern u1="&#x2e;" u2="&#xd2;" k="102" />
<hkern u1="&#x2e;" u2="&#xc7;" k="102" />
<hkern u1="&#x2e;" u2="Y" k="123" />
<hkern u1="&#x2e;" u2="W" k="123" />
<hkern u1="&#x2e;" u2="V" k="123" />
<hkern u1="&#x2e;" u2="U" k="41" />
<hkern u1="&#x2e;" u2="T" k="143" />
<hkern u1="&#x2e;" u2="Q" k="102" />
<hkern u1="&#x2e;" u2="O" k="102" />
<hkern u1="&#x2e;" u2="G" k="102" />
<hkern u1="&#x2e;" u2="C" k="102" />
<hkern u1="A" u2="&#x201d;" k="143" />
<hkern u1="A" u2="&#x2019;" k="143" />
<hkern u1="A" u2="&#x178;" k="123" />
<hkern u1="A" u2="&#x152;" k="41" />
<hkern u1="A" u2="&#xdd;" k="123" />
<hkern u1="A" u2="&#xd8;" k="41" />
<hkern u1="A" u2="&#xd6;" k="41" />
<hkern u1="A" u2="&#xd5;" k="41" />
<hkern u1="A" u2="&#xd4;" k="41" />
<hkern u1="A" u2="&#xd3;" k="41" />
<hkern u1="A" u2="&#xd2;" k="41" />
<hkern u1="A" u2="&#xc7;" k="41" />
<hkern u1="A" u2="Y" k="123" />
<hkern u1="A" u2="W" k="82" />
<hkern u1="A" u2="V" k="82" />
<hkern u1="A" u2="T" k="143" />
<hkern u1="A" u2="Q" k="41" />
<hkern u1="A" u2="O" k="41" />
<hkern u1="A" u2="J" k="-266" />
<hkern u1="A" u2="G" k="41" />
<hkern u1="A" u2="C" k="41" />
<hkern u1="A" u2="&#x27;" k="143" />
<hkern u1="A" u2="&#x22;" k="143" />
<hkern u1="B" u2="&#x201e;" k="82" />
<hkern u1="B" u2="&#x201a;" k="82" />
<hkern u1="B" u2="&#x178;" k="20" />
<hkern u1="B" u2="&#xdd;" k="20" />
<hkern u1="B" u2="&#xc5;" k="41" />
<hkern u1="B" u2="&#xc4;" k="41" />
<hkern u1="B" u2="&#xc3;" k="41" />
<hkern u1="B" u2="&#xc2;" k="41" />
<hkern u1="B" u2="&#xc1;" k="41" />
<hkern u1="B" u2="&#xc0;" k="41" />
<hkern u1="B" u2="Z" k="20" />
<hkern u1="B" u2="Y" k="20" />
<hkern u1="B" u2="X" k="41" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="20" />
<hkern u1="B" u2="T" k="61" />
<hkern u1="B" u2="A" k="41" />
<hkern u1="B" u2="&#x2e;" k="82" />
<hkern u1="B" u2="&#x2c;" k="82" />
<hkern u1="C" u2="&#x152;" k="41" />
<hkern u1="C" u2="&#xd8;" k="41" />
<hkern u1="C" u2="&#xd6;" k="41" />
<hkern u1="C" u2="&#xd5;" k="41" />
<hkern u1="C" u2="&#xd4;" k="41" />
<hkern u1="C" u2="&#xd3;" k="41" />
<hkern u1="C" u2="&#xd2;" k="41" />
<hkern u1="C" u2="&#xc7;" k="41" />
<hkern u1="C" u2="Q" k="41" />
<hkern u1="C" u2="O" k="41" />
<hkern u1="C" u2="G" k="41" />
<hkern u1="C" u2="C" k="41" />
<hkern u1="D" u2="&#x201e;" k="82" />
<hkern u1="D" u2="&#x201a;" k="82" />
<hkern u1="D" u2="&#x178;" k="20" />
<hkern u1="D" u2="&#xdd;" k="20" />
<hkern u1="D" u2="&#xc5;" k="41" />
<hkern u1="D" u2="&#xc4;" k="41" />
<hkern u1="D" u2="&#xc3;" k="41" />
<hkern u1="D" u2="&#xc2;" k="41" />
<hkern u1="D" u2="&#xc1;" k="41" />
<hkern u1="D" u2="&#xc0;" k="41" />
<hkern u1="D" u2="Z" k="20" />
<hkern u1="D" u2="Y" k="20" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="D" u2="W" k="20" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="61" />
<hkern u1="D" u2="A" k="41" />
<hkern u1="D" u2="&#x2e;" k="82" />
<hkern u1="D" u2="&#x2c;" k="82" />
<hkern u1="E" u2="J" k="-123" />
<hkern u1="F" u2="&#x201e;" k="123" />
<hkern u1="F" u2="&#x201a;" k="123" />
<hkern u1="F" u2="&#xc5;" k="41" />
<hkern u1="F" u2="&#xc4;" k="41" />
<hkern u1="F" u2="&#xc3;" k="41" />
<hkern u1="F" u2="&#xc2;" k="41" />
<hkern u1="F" u2="&#xc1;" k="41" />
<hkern u1="F" u2="&#xc0;" k="41" />
<hkern u1="F" u2="A" k="41" />
<hkern u1="F" u2="&#x3f;" k="-41" />
<hkern u1="F" u2="&#x2e;" k="123" />
<hkern u1="F" u2="&#x2c;" k="123" />
<hkern u1="K" u2="&#x152;" k="41" />
<hkern u1="K" u2="&#xd8;" k="41" />
<hkern u1="K" u2="&#xd6;" k="41" />
<hkern u1="K" u2="&#xd5;" k="41" />
<hkern u1="K" u2="&#xd4;" k="41" />
<hkern u1="K" u2="&#xd3;" k="41" />
<hkern u1="K" u2="&#xd2;" k="41" />
<hkern u1="K" u2="&#xc7;" k="41" />
<hkern u1="K" u2="Q" k="41" />
<hkern u1="K" u2="O" k="41" />
<hkern u1="K" u2="G" k="41" />
<hkern u1="K" u2="C" k="41" />
<hkern u1="L" u2="&#x201d;" k="164" />
<hkern u1="L" u2="&#x2019;" k="164" />
<hkern u1="L" u2="&#x178;" k="61" />
<hkern u1="L" u2="&#x152;" k="41" />
<hkern u1="L" u2="&#xdd;" k="61" />
<hkern u1="L" u2="&#xdc;" k="20" />
<hkern u1="L" u2="&#xdb;" k="20" />
<hkern u1="L" u2="&#xda;" k="20" />
<hkern u1="L" u2="&#xd9;" k="20" />
<hkern u1="L" u2="&#xd8;" k="41" />
<hkern u1="L" u2="&#xd6;" k="41" />
<hkern u1="L" u2="&#xd5;" k="41" />
<hkern u1="L" u2="&#xd4;" k="41" />
<hkern u1="L" u2="&#xd3;" k="41" />
<hkern u1="L" u2="&#xd2;" k="41" />
<hkern u1="L" u2="&#xc7;" k="41" />
<hkern u1="L" u2="Y" k="61" />
<hkern u1="L" u2="W" k="41" />
<hkern u1="L" u2="V" k="41" />
<hkern u1="L" u2="U" k="20" />
<hkern u1="L" u2="T" k="41" />
<hkern u1="L" u2="Q" k="41" />
<hkern u1="L" u2="O" k="41" />
<hkern u1="L" u2="G" k="41" />
<hkern u1="L" u2="C" k="41" />
<hkern u1="L" u2="&#x27;" k="164" />
<hkern u1="L" u2="&#x22;" k="164" />
<hkern u1="O" u2="&#x201e;" k="82" />
<hkern u1="O" u2="&#x201a;" k="82" />
<hkern u1="O" u2="&#x178;" k="20" />
<hkern u1="O" u2="&#xdd;" k="20" />
<hkern u1="O" u2="&#xc5;" k="41" />
<hkern u1="O" u2="&#xc4;" k="41" />
<hkern u1="O" u2="&#xc3;" k="41" />
<hkern u1="O" u2="&#xc2;" k="41" />
<hkern u1="O" u2="&#xc1;" k="41" />
<hkern u1="O" u2="&#xc0;" k="41" />
<hkern u1="O" u2="Z" k="20" />
<hkern u1="O" u2="Y" k="20" />
<hkern u1="O" u2="X" k="41" />
<hkern u1="O" u2="W" k="20" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="T" k="61" />
<hkern u1="O" u2="A" k="41" />
<hkern u1="O" u2="&#x2e;" k="82" />
<hkern u1="O" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x201e;" k="266" />
<hkern u1="P" u2="&#x201a;" k="266" />
<hkern u1="P" u2="&#xc5;" k="102" />
<hkern u1="P" u2="&#xc4;" k="102" />
<hkern u1="P" u2="&#xc3;" k="102" />
<hkern u1="P" u2="&#xc2;" k="102" />
<hkern u1="P" u2="&#xc1;" k="102" />
<hkern u1="P" u2="&#xc0;" k="102" />
<hkern u1="P" u2="Z" k="20" />
<hkern u1="P" u2="X" k="41" />
<hkern u1="P" u2="A" k="102" />
<hkern u1="P" u2="&#x2e;" k="266" />
<hkern u1="P" u2="&#x2c;" k="266" />
<hkern u1="Q" u2="&#x201e;" k="82" />
<hkern u1="Q" u2="&#x201a;" k="82" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="&#xc5;" k="41" />
<hkern u1="Q" u2="&#xc4;" k="41" />
<hkern u1="Q" u2="&#xc3;" k="41" />
<hkern u1="Q" u2="&#xc2;" k="41" />
<hkern u1="Q" u2="&#xc1;" k="41" />
<hkern u1="Q" u2="&#xc0;" k="41" />
<hkern u1="Q" u2="Z" k="20" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="X" k="41" />
<hkern u1="Q" u2="W" k="20" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="A" k="41" />
<hkern u1="Q" u2="&#x2e;" k="82" />
<hkern u1="Q" u2="&#x2c;" k="82" />
<hkern u1="T" u2="&#x201e;" k="123" />
<hkern u1="T" u2="&#x201a;" k="123" />
<hkern u1="T" u2="&#x2014;" k="82" />
<hkern u1="T" u2="&#x2013;" k="82" />
<hkern u1="T" u2="&#x153;" k="143" />
<hkern u1="T" u2="&#x152;" k="41" />
<hkern u1="T" u2="&#xfd;" k="41" />
<hkern u1="T" u2="&#xfc;" k="102" />
<hkern u1="T" u2="&#xfb;" k="102" />
<hkern u1="T" u2="&#xfa;" k="102" />
<hkern u1="T" u2="&#xf9;" k="102" />
<hkern u1="T" u2="&#xf8;" k="143" />
<hkern u1="T" u2="&#xf6;" k="143" />
<hkern u1="T" u2="&#xf5;" k="143" />
<hkern u1="T" u2="&#xf4;" k="143" />
<hkern u1="T" u2="&#xf3;" k="143" />
<hkern u1="T" u2="&#xf2;" k="143" />
<hkern u1="T" u2="&#xeb;" k="143" />
<hkern u1="T" u2="&#xea;" k="143" />
<hkern u1="T" u2="&#xe9;" k="143" />
<hkern u1="T" u2="&#xe8;" k="143" />
<hkern u1="T" u2="&#xe7;" k="143" />
<hkern u1="T" u2="&#xe6;" k="164" />
<hkern u1="T" u2="&#xe5;" k="164" />
<hkern u1="T" u2="&#xe4;" k="164" />
<hkern u1="T" u2="&#xe3;" k="164" />
<hkern u1="T" u2="&#xe2;" k="164" />
<hkern u1="T" u2="&#xe1;" k="164" />
<hkern u1="T" u2="&#xe0;" k="143" />
<hkern u1="T" u2="&#xd8;" k="41" />
<hkern u1="T" u2="&#xd6;" k="41" />
<hkern u1="T" u2="&#xd5;" k="41" />
<hkern u1="T" u2="&#xd4;" k="41" />
<hkern u1="T" u2="&#xd3;" k="41" />
<hkern u1="T" u2="&#xd2;" k="41" />
<hkern u1="T" u2="&#xc7;" k="41" />
<hkern u1="T" u2="&#xc5;" k="143" />
<hkern u1="T" u2="&#xc4;" k="143" />
<hkern u1="T" u2="&#xc3;" k="143" />
<hkern u1="T" u2="&#xc2;" k="143" />
<hkern u1="T" u2="&#xc1;" k="143" />
<hkern u1="T" u2="&#xc0;" k="143" />
<hkern u1="T" u2="z" k="82" />
<hkern u1="T" u2="y" k="41" />
<hkern u1="T" u2="x" k="41" />
<hkern u1="T" u2="w" k="41" />
<hkern u1="T" u2="v" k="41" />
<hkern u1="T" u2="u" k="102" />
<hkern u1="T" u2="s" k="123" />
<hkern u1="T" u2="r" k="102" />
<hkern u1="T" u2="q" k="143" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="o" k="143" />
<hkern u1="T" u2="n" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="g" k="143" />
<hkern u1="T" u2="e" k="143" />
<hkern u1="T" u2="d" k="143" />
<hkern u1="T" u2="c" k="143" />
<hkern u1="T" u2="a" k="164" />
<hkern u1="T" u2="T" k="-41" />
<hkern u1="T" u2="Q" k="41" />
<hkern u1="T" u2="O" k="41" />
<hkern u1="T" u2="G" k="41" />
<hkern u1="T" u2="C" k="41" />
<hkern u1="T" u2="A" k="143" />
<hkern u1="T" u2="&#x3f;" k="-41" />
<hkern u1="T" u2="&#x2e;" k="123" />
<hkern u1="T" u2="&#x2d;" k="82" />
<hkern u1="T" u2="&#x2c;" k="123" />
<hkern u1="U" u2="&#x201e;" k="41" />
<hkern u1="U" u2="&#x201a;" k="41" />
<hkern u1="U" u2="&#xc5;" k="20" />
<hkern u1="U" u2="&#xc4;" k="20" />
<hkern u1="U" u2="&#xc3;" k="20" />
<hkern u1="U" u2="&#xc2;" k="20" />
<hkern u1="U" u2="&#xc1;" k="20" />
<hkern u1="U" u2="&#xc0;" k="20" />
<hkern u1="U" u2="A" k="20" />
<hkern u1="U" u2="&#x2e;" k="41" />
<hkern u1="U" u2="&#x2c;" k="41" />
<hkern u1="V" u2="&#x201e;" k="102" />
<hkern u1="V" u2="&#x201a;" k="102" />
<hkern u1="V" u2="&#x153;" k="41" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfc;" k="20" />
<hkern u1="V" u2="&#xfb;" k="20" />
<hkern u1="V" u2="&#xfa;" k="20" />
<hkern u1="V" u2="&#xf9;" k="20" />
<hkern u1="V" u2="&#xf8;" k="41" />
<hkern u1="V" u2="&#xf6;" k="41" />
<hkern u1="V" u2="&#xf5;" k="41" />
<hkern u1="V" u2="&#xf4;" k="41" />
<hkern u1="V" u2="&#xf3;" k="41" />
<hkern u1="V" u2="&#xf2;" k="41" />
<hkern u1="V" u2="&#xeb;" k="41" />
<hkern u1="V" u2="&#xea;" k="41" />
<hkern u1="V" u2="&#xe9;" k="41" />
<hkern u1="V" u2="&#xe8;" k="41" />
<hkern u1="V" u2="&#xe7;" k="41" />
<hkern u1="V" u2="&#xe6;" k="41" />
<hkern u1="V" u2="&#xe5;" k="41" />
<hkern u1="V" u2="&#xe4;" k="41" />
<hkern u1="V" u2="&#xe3;" k="41" />
<hkern u1="V" u2="&#xe2;" k="41" />
<hkern u1="V" u2="&#xe1;" k="41" />
<hkern u1="V" u2="&#xe0;" k="41" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc5;" k="82" />
<hkern u1="V" u2="&#xc4;" k="82" />
<hkern u1="V" u2="&#xc3;" k="82" />
<hkern u1="V" u2="&#xc2;" k="82" />
<hkern u1="V" u2="&#xc1;" k="82" />
<hkern u1="V" u2="&#xc0;" k="82" />
<hkern u1="V" u2="u" k="20" />
<hkern u1="V" u2="s" k="20" />
<hkern u1="V" u2="r" k="20" />
<hkern u1="V" u2="q" k="41" />
<hkern u1="V" u2="p" k="20" />
<hkern u1="V" u2="o" k="41" />
<hkern u1="V" u2="n" k="20" />
<hkern u1="V" u2="m" k="20" />
<hkern u1="V" u2="g" k="20" />
<hkern u1="V" u2="e" k="41" />
<hkern u1="V" u2="d" k="41" />
<hkern u1="V" u2="c" k="41" />
<hkern u1="V" u2="a" k="41" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="82" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2e;" k="102" />
<hkern u1="V" u2="&#x2c;" k="102" />
<hkern u1="W" u2="&#x201e;" k="102" />
<hkern u1="W" u2="&#x201a;" k="102" />
<hkern u1="W" u2="&#x153;" k="41" />
<hkern u1="W" u2="&#x152;" k="20" />
<hkern u1="W" u2="&#xfc;" k="20" />
<hkern u1="W" u2="&#xfb;" k="20" />
<hkern u1="W" u2="&#xfa;" k="20" />
<hkern u1="W" u2="&#xf9;" k="20" />
<hkern u1="W" u2="&#xf8;" k="41" />
<hkern u1="W" u2="&#xf6;" k="41" />
<hkern u1="W" u2="&#xf5;" k="41" />
<hkern u1="W" u2="&#xf4;" k="41" />
<hkern u1="W" u2="&#xf3;" k="41" />
<hkern u1="W" u2="&#xf2;" k="41" />
<hkern u1="W" u2="&#xeb;" k="41" />
<hkern u1="W" u2="&#xea;" k="41" />
<hkern u1="W" u2="&#xe9;" k="41" />
<hkern u1="W" u2="&#xe8;" k="41" />
<hkern u1="W" u2="&#xe7;" k="41" />
<hkern u1="W" u2="&#xe6;" k="41" />
<hkern u1="W" u2="&#xe5;" k="41" />
<hkern u1="W" u2="&#xe4;" k="41" />
<hkern u1="W" u2="&#xe3;" k="41" />
<hkern u1="W" u2="&#xe2;" k="41" />
<hkern u1="W" u2="&#xe1;" k="41" />
<hkern u1="W" u2="&#xe0;" k="41" />
<hkern u1="W" u2="&#xd8;" k="20" />
<hkern u1="W" u2="&#xd6;" k="20" />
<hkern u1="W" u2="&#xd5;" k="20" />
<hkern u1="W" u2="&#xd4;" k="20" />
<hkern u1="W" u2="&#xd3;" k="20" />
<hkern u1="W" u2="&#xd2;" k="20" />
<hkern u1="W" u2="&#xc7;" k="20" />
<hkern u1="W" u2="&#xc5;" k="82" />
<hkern u1="W" u2="&#xc4;" k="82" />
<hkern u1="W" u2="&#xc3;" k="82" />
<hkern u1="W" u2="&#xc2;" k="82" />
<hkern u1="W" u2="&#xc1;" k="82" />
<hkern u1="W" u2="&#xc0;" k="82" />
<hkern u1="W" u2="u" k="20" />
<hkern u1="W" u2="s" k="20" />
<hkern u1="W" u2="r" k="20" />
<hkern u1="W" u2="q" k="41" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="o" k="41" />
<hkern u1="W" u2="n" k="20" />
<hkern u1="W" u2="m" k="20" />
<hkern u1="W" u2="g" k="20" />
<hkern u1="W" u2="e" k="41" />
<hkern u1="W" u2="d" k="41" />
<hkern u1="W" u2="c" k="41" />
<hkern u1="W" u2="a" k="41" />
<hkern u1="W" u2="Q" k="20" />
<hkern u1="W" u2="O" k="20" />
<hkern u1="W" u2="G" k="20" />
<hkern u1="W" u2="C" k="20" />
<hkern u1="W" u2="A" k="82" />
<hkern u1="W" u2="&#x3f;" k="-41" />
<hkern u1="W" u2="&#x2e;" k="102" />
<hkern u1="W" u2="&#x2c;" k="102" />
<hkern u1="X" u2="&#x152;" k="41" />
<hkern u1="X" u2="&#xd8;" k="41" />
<hkern u1="X" u2="&#xd6;" k="41" />
<hkern u1="X" u2="&#xd5;" k="41" />
<hkern u1="X" u2="&#xd4;" k="41" />
<hkern u1="X" u2="&#xd3;" k="41" />
<hkern u1="X" u2="&#xd2;" k="41" />
<hkern u1="X" u2="&#xc7;" k="41" />
<hkern u1="X" u2="Q" k="41" />
<hkern u1="X" u2="O" k="41" />
<hkern u1="X" u2="G" k="41" />
<hkern u1="X" u2="C" k="41" />
<hkern u1="Y" u2="&#x201e;" k="123" />
<hkern u1="Y" u2="&#x201a;" k="123" />
<hkern u1="Y" u2="&#x153;" k="102" />
<hkern u1="Y" u2="&#x152;" k="41" />
<hkern u1="Y" u2="&#xfc;" k="61" />
<hkern u1="Y" u2="&#xfb;" k="61" />
<hkern u1="Y" u2="&#xfa;" k="61" />
<hkern u1="Y" u2="&#xf9;" k="61" />
<hkern u1="Y" u2="&#xf8;" k="102" />
<hkern u1="Y" u2="&#xf6;" k="102" />
<hkern u1="Y" u2="&#xf5;" k="102" />
<hkern u1="Y" u2="&#xf4;" k="102" />
<hkern u1="Y" u2="&#xf3;" k="102" />
<hkern u1="Y" u2="&#xf2;" k="102" />
<hkern u1="Y" u2="&#xeb;" k="102" />
<hkern u1="Y" u2="&#xea;" k="102" />
<hkern u1="Y" u2="&#xe9;" k="102" />
<hkern u1="Y" u2="&#xe8;" k="102" />
<hkern u1="Y" u2="&#xe7;" k="102" />
<hkern u1="Y" u2="&#xe6;" k="102" />
<hkern u1="Y" u2="&#xe5;" k="102" />
<hkern u1="Y" u2="&#xe4;" k="102" />
<hkern u1="Y" u2="&#xe3;" k="102" />
<hkern u1="Y" u2="&#xe2;" k="102" />
<hkern u1="Y" u2="&#xe1;" k="102" />
<hkern u1="Y" u2="&#xe0;" k="102" />
<hkern u1="Y" u2="&#xd8;" k="41" />
<hkern u1="Y" u2="&#xd6;" k="41" />
<hkern u1="Y" u2="&#xd5;" k="41" />
<hkern u1="Y" u2="&#xd4;" k="41" />
<hkern u1="Y" u2="&#xd3;" k="41" />
<hkern u1="Y" u2="&#xd2;" k="41" />
<hkern u1="Y" u2="&#xc7;" k="41" />
<hkern u1="Y" u2="&#xc5;" k="123" />
<hkern u1="Y" u2="&#xc4;" k="123" />
<hkern u1="Y" u2="&#xc3;" k="123" />
<hkern u1="Y" u2="&#xc2;" k="123" />
<hkern u1="Y" u2="&#xc1;" k="123" />
<hkern u1="Y" u2="&#xc0;" k="123" />
<hkern u1="Y" u2="z" k="41" />
<hkern u1="Y" u2="u" k="61" />
<hkern u1="Y" u2="s" k="82" />
<hkern u1="Y" u2="r" k="61" />
<hkern u1="Y" u2="q" k="102" />
<hkern u1="Y" u2="p" k="61" />
<hkern u1="Y" u2="o" k="102" />
<hkern u1="Y" u2="n" k="61" />
<hkern u1="Y" u2="m" k="61" />
<hkern u1="Y" u2="g" k="41" />
<hkern u1="Y" u2="e" k="102" />
<hkern u1="Y" u2="d" k="102" />
<hkern u1="Y" u2="c" k="102" />
<hkern u1="Y" u2="a" k="102" />
<hkern u1="Y" u2="Q" k="41" />
<hkern u1="Y" u2="O" k="41" />
<hkern u1="Y" u2="G" k="41" />
<hkern u1="Y" u2="C" k="41" />
<hkern u1="Y" u2="A" k="123" />
<hkern u1="Y" u2="&#x3f;" k="-41" />
<hkern u1="Y" u2="&#x2e;" k="123" />
<hkern u1="Y" u2="&#x2c;" k="123" />
<hkern u1="Z" u2="&#x152;" k="20" />
<hkern u1="Z" u2="&#xd8;" k="20" />
<hkern u1="Z" u2="&#xd6;" k="20" />
<hkern u1="Z" u2="&#xd5;" k="20" />
<hkern u1="Z" u2="&#xd4;" k="20" />
<hkern u1="Z" u2="&#xd3;" k="20" />
<hkern u1="Z" u2="&#xd2;" k="20" />
<hkern u1="Z" u2="&#xc7;" k="20" />
<hkern u1="Z" u2="Q" k="20" />
<hkern u1="Z" u2="O" k="20" />
<hkern u1="Z" u2="G" k="20" />
<hkern u1="Z" u2="C" k="20" />
<hkern u1="[" u2="J" k="-184" />
<hkern u1="a" u2="&#x201d;" k="20" />
<hkern u1="a" u2="&#x2019;" k="20" />
<hkern u1="a" u2="&#x27;" k="20" />
<hkern u1="a" u2="&#x22;" k="20" />
<hkern u1="b" u2="&#x201d;" k="20" />
<hkern u1="b" u2="&#x2019;" k="20" />
<hkern u1="b" u2="&#xfd;" k="41" />
<hkern u1="b" u2="z" k="20" />
<hkern u1="b" u2="y" k="41" />
<hkern u1="b" u2="x" k="41" />
<hkern u1="b" u2="w" k="41" />
<hkern u1="b" u2="v" k="41" />
<hkern u1="b" u2="&#x27;" k="20" />
<hkern u1="b" u2="&#x22;" k="20" />
<hkern u1="c" u2="&#x201d;" k="-41" />
<hkern u1="c" u2="&#x2019;" k="-41" />
<hkern u1="c" u2="&#x27;" k="-41" />
<hkern u1="c" u2="&#x22;" k="-41" />
<hkern u1="e" u2="&#x201d;" k="20" />
<hkern u1="e" u2="&#x2019;" k="20" />
<hkern u1="e" u2="&#xfd;" k="41" />
<hkern u1="e" u2="z" k="20" />
<hkern u1="e" u2="y" k="41" />
<hkern u1="e" u2="x" k="41" />
<hkern u1="e" u2="w" k="41" />
<hkern u1="e" u2="v" k="41" />
<hkern u1="e" u2="&#x27;" k="20" />
<hkern u1="e" u2="&#x22;" k="20" />
<hkern u1="f" u2="&#x201d;" k="-123" />
<hkern u1="f" u2="&#x2019;" k="-123" />
<hkern u1="f" u2="&#x27;" k="-123" />
<hkern u1="f" u2="&#x22;" k="-123" />
<hkern u1="h" u2="&#x201d;" k="20" />
<hkern u1="h" u2="&#x2019;" k="20" />
<hkern u1="h" u2="&#x27;" k="20" />
<hkern u1="h" u2="&#x22;" k="20" />
<hkern u1="k" u2="&#x153;" k="41" />
<hkern u1="k" u2="&#xf8;" k="41" />
<hkern u1="k" u2="&#xf6;" k="41" />
<hkern u1="k" u2="&#xf5;" k="41" />
<hkern u1="k" u2="&#xf4;" k="41" />
<hkern u1="k" u2="&#xf3;" k="41" />
<hkern u1="k" u2="&#xf2;" k="41" />
<hkern u1="k" u2="&#xeb;" k="41" />
<hkern u1="k" u2="&#xea;" k="41" />
<hkern u1="k" u2="&#xe9;" k="41" />
<hkern u1="k" u2="&#xe8;" k="41" />
<hkern u1="k" u2="&#xe7;" k="41" />
<hkern u1="k" u2="&#xe0;" k="41" />
<hkern u1="k" u2="q" k="41" />
<hkern u1="k" u2="o" k="41" />
<hkern u1="k" u2="e" k="41" />
<hkern u1="k" u2="d" k="41" />
<hkern u1="k" u2="c" k="41" />
<hkern u1="m" u2="&#x201d;" k="20" />
<hkern u1="m" u2="&#x2019;" k="20" />
<hkern u1="m" u2="&#x27;" k="20" />
<hkern u1="m" u2="&#x22;" k="20" />
<hkern u1="n" u2="&#x201d;" k="20" />
<hkern u1="n" u2="&#x2019;" k="20" />
<hkern u1="n" u2="&#x27;" k="20" />
<hkern u1="n" u2="&#x22;" k="20" />
<hkern u1="o" u2="&#x201d;" k="20" />
<hkern u1="o" u2="&#x2019;" k="20" />
<hkern u1="o" u2="&#xfd;" k="41" />
<hkern u1="o" u2="z" k="20" />
<hkern u1="o" u2="y" k="41" />
<hkern u1="o" u2="x" k="41" />
<hkern u1="o" u2="w" k="41" />
<hkern u1="o" u2="v" k="41" />
<hkern u1="o" u2="&#x27;" k="20" />
<hkern u1="o" u2="&#x22;" k="20" />
<hkern u1="p" u2="&#x201d;" k="20" />
<hkern u1="p" u2="&#x2019;" k="20" />
<hkern u1="p" u2="&#xfd;" k="41" />
<hkern u1="p" u2="z" k="20" />
<hkern u1="p" u2="y" k="41" />
<hkern u1="p" u2="x" k="41" />
<hkern u1="p" u2="w" k="41" />
<hkern u1="p" u2="v" k="41" />
<hkern u1="p" u2="&#x27;" k="20" />
<hkern u1="p" u2="&#x22;" k="20" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x153;" k="41" />
<hkern u1="r" u2="&#xf8;" k="41" />
<hkern u1="r" u2="&#xf6;" k="41" />
<hkern u1="r" u2="&#xf5;" k="41" />
<hkern u1="r" u2="&#xf4;" k="41" />
<hkern u1="r" u2="&#xf3;" k="41" />
<hkern u1="r" u2="&#xf2;" k="41" />
<hkern u1="r" u2="&#xeb;" k="41" />
<hkern u1="r" u2="&#xea;" k="41" />
<hkern u1="r" u2="&#xe9;" k="41" />
<hkern u1="r" u2="&#xe8;" k="41" />
<hkern u1="r" u2="&#xe7;" k="41" />
<hkern u1="r" u2="&#xe6;" k="41" />
<hkern u1="r" u2="&#xe5;" k="41" />
<hkern u1="r" u2="&#xe4;" k="41" />
<hkern u1="r" u2="&#xe3;" k="41" />
<hkern u1="r" u2="&#xe2;" k="41" />
<hkern u1="r" u2="&#xe1;" k="41" />
<hkern u1="r" u2="&#xe0;" k="41" />
<hkern u1="r" u2="q" k="41" />
<hkern u1="r" u2="o" k="41" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="e" k="41" />
<hkern u1="r" u2="d" k="41" />
<hkern u1="r" u2="c" k="41" />
<hkern u1="r" u2="a" k="41" />
<hkern u1="r" u2="&#x27;" k="-82" />
<hkern u1="r" u2="&#x22;" k="-82" />
<hkern u1="t" u2="&#x201d;" k="-41" />
<hkern u1="t" u2="&#x2019;" k="-41" />
<hkern u1="t" u2="&#x27;" k="-41" />
<hkern u1="t" u2="&#x22;" k="-41" />
<hkern u1="v" u2="&#x201e;" k="82" />
<hkern u1="v" u2="&#x201d;" k="-82" />
<hkern u1="v" u2="&#x201a;" k="82" />
<hkern u1="v" u2="&#x2019;" k="-82" />
<hkern u1="v" u2="&#x3f;" k="-41" />
<hkern u1="v" u2="&#x2e;" k="82" />
<hkern u1="v" u2="&#x2c;" k="82" />
<hkern u1="v" u2="&#x27;" k="-82" />
<hkern u1="v" u2="&#x22;" k="-82" />
<hkern u1="w" u2="&#x201e;" k="82" />
<hkern u1="w" u2="&#x201d;" k="-82" />
<hkern u1="w" u2="&#x201a;" k="82" />
<hkern u1="w" u2="&#x2019;" k="-82" />
<hkern u1="w" u2="&#x3f;" k="-41" />
<hkern u1="w" u2="&#x2e;" k="82" />
<hkern u1="w" u2="&#x2c;" k="82" />
<hkern u1="w" u2="&#x27;" k="-82" />
<hkern u1="w" u2="&#x22;" k="-82" />
<hkern u1="x" u2="&#x153;" k="41" />
<hkern u1="x" u2="&#xf8;" k="41" />
<hkern u1="x" u2="&#xf6;" k="41" />
<hkern u1="x" u2="&#xf5;" k="41" />
<hkern u1="x" u2="&#xf4;" k="41" />
<hkern u1="x" u2="&#xf3;" k="41" />
<hkern u1="x" u2="&#xf2;" k="41" />
<hkern u1="x" u2="&#xeb;" k="41" />
<hkern u1="x" u2="&#xea;" k="41" />
<hkern u1="x" u2="&#xe9;" k="41" />
<hkern u1="x" u2="&#xe8;" k="41" />
<hkern u1="x" u2="&#xe7;" k="41" />
<hkern u1="x" u2="&#xe0;" k="41" />
<hkern u1="x" u2="q" k="41" />
<hkern u1="x" u2="o" k="41" />
<hkern u1="x" u2="e" k="41" />
<hkern u1="x" u2="d" k="41" />
<hkern u1="x" u2="c" k="41" />
<hkern u1="y" u2="&#x201e;" k="82" />
<hkern u1="y" u2="&#x201d;" k="-82" />
<hkern u1="y" u2="&#x201a;" k="82" />
<hkern u1="y" u2="&#x2019;" k="-82" />
<hkern u1="y" u2="&#x3f;" k="-41" />
<hkern u1="y" u2="&#x2e;" k="82" />
<hkern u1="y" u2="&#x2c;" k="82" />
<hkern u1="y" u2="&#x27;" k="-82" />
<hkern u1="y" u2="&#x22;" k="-82" />
<hkern u1="&#x7b;" u2="J" k="-184" />
<hkern u1="&#xc0;" u2="&#x201d;" k="143" />
<hkern u1="&#xc0;" u2="&#x2019;" k="143" />
<hkern u1="&#xc0;" u2="&#x178;" k="123" />
<hkern u1="&#xc0;" u2="&#x152;" k="41" />
<hkern u1="&#xc0;" u2="&#xdd;" k="123" />
<hkern u1="&#xc0;" u2="&#xd8;" k="41" />
<hkern u1="&#xc0;" u2="&#xd6;" k="41" />
<hkern u1="&#xc0;" u2="&#xd5;" k="41" />
<hkern u1="&#xc0;" u2="&#xd4;" k="41" />
<hkern u1="&#xc0;" u2="&#xd3;" k="41" />
<hkern u1="&#xc0;" u2="&#xd2;" k="41" />
<hkern u1="&#xc0;" u2="&#xc7;" k="41" />
<hkern u1="&#xc0;" u2="Y" k="123" />
<hkern u1="&#xc0;" u2="W" k="82" />
<hkern u1="&#xc0;" u2="V" k="82" />
<hkern u1="&#xc0;" u2="T" k="143" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="O" k="41" />
<hkern u1="&#xc0;" u2="J" k="-266" />
<hkern u1="&#xc0;" u2="G" k="41" />
<hkern u1="&#xc0;" u2="C" k="41" />
<hkern u1="&#xc0;" u2="&#x27;" k="143" />
<hkern u1="&#xc0;" u2="&#x22;" k="143" />
<hkern u1="&#xc1;" u2="&#x201d;" k="143" />
<hkern u1="&#xc1;" u2="&#x2019;" k="143" />
<hkern u1="&#xc1;" u2="&#x178;" k="123" />
<hkern u1="&#xc1;" u2="&#x152;" k="41" />
<hkern u1="&#xc1;" u2="&#xdd;" k="123" />
<hkern u1="&#xc1;" u2="&#xd8;" k="41" />
<hkern u1="&#xc1;" u2="&#xd6;" k="41" />
<hkern u1="&#xc1;" u2="&#xd5;" k="41" />
<hkern u1="&#xc1;" u2="&#xd4;" k="41" />
<hkern u1="&#xc1;" u2="&#xd3;" k="41" />
<hkern u1="&#xc1;" u2="&#xd2;" k="41" />
<hkern u1="&#xc1;" u2="&#xc7;" k="41" />
<hkern u1="&#xc1;" u2="Y" k="123" />
<hkern u1="&#xc1;" u2="W" k="82" />
<hkern u1="&#xc1;" u2="V" k="82" />
<hkern u1="&#xc1;" u2="T" k="143" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="O" k="41" />
<hkern u1="&#xc1;" u2="J" k="-266" />
<hkern u1="&#xc1;" u2="G" k="41" />
<hkern u1="&#xc1;" u2="C" k="41" />
<hkern u1="&#xc1;" u2="&#x27;" k="143" />
<hkern u1="&#xc1;" u2="&#x22;" k="143" />
<hkern u1="&#xc2;" u2="&#x201d;" k="143" />
<hkern u1="&#xc2;" u2="&#x2019;" k="143" />
<hkern u1="&#xc2;" u2="&#x178;" k="123" />
<hkern u1="&#xc2;" u2="&#x152;" k="41" />
<hkern u1="&#xc2;" u2="&#xdd;" k="123" />
<hkern u1="&#xc2;" u2="&#xd8;" k="41" />
<hkern u1="&#xc2;" u2="&#xd6;" k="41" />
<hkern u1="&#xc2;" u2="&#xd5;" k="41" />
<hkern u1="&#xc2;" u2="&#xd4;" k="41" />
<hkern u1="&#xc2;" u2="&#xd3;" k="41" />
<hkern u1="&#xc2;" u2="&#xd2;" k="41" />
<hkern u1="&#xc2;" u2="&#xc7;" k="41" />
<hkern u1="&#xc2;" u2="Y" k="123" />
<hkern u1="&#xc2;" u2="W" k="82" />
<hkern u1="&#xc2;" u2="V" k="82" />
<hkern u1="&#xc2;" u2="T" k="143" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="O" k="41" />
<hkern u1="&#xc2;" u2="J" k="-266" />
<hkern u1="&#xc2;" u2="G" k="41" />
<hkern u1="&#xc2;" u2="C" k="41" />
<hkern u1="&#xc2;" u2="&#x27;" k="143" />
<hkern u1="&#xc2;" u2="&#x22;" k="143" />
<hkern u1="&#xc3;" u2="&#x201d;" k="143" />
<hkern u1="&#xc3;" u2="&#x2019;" k="143" />
<hkern u1="&#xc3;" u2="&#x178;" k="123" />
<hkern u1="&#xc3;" u2="&#x152;" k="41" />
<hkern u1="&#xc3;" u2="&#xdd;" k="123" />
<hkern u1="&#xc3;" u2="&#xd8;" k="41" />
<hkern u1="&#xc3;" u2="&#xd6;" k="41" />
<hkern u1="&#xc3;" u2="&#xd5;" k="41" />
<hkern u1="&#xc3;" u2="&#xd4;" k="41" />
<hkern u1="&#xc3;" u2="&#xd3;" k="41" />
<hkern u1="&#xc3;" u2="&#xd2;" k="41" />
<hkern u1="&#xc3;" u2="&#xc7;" k="41" />
<hkern u1="&#xc3;" u2="Y" k="123" />
<hkern u1="&#xc3;" u2="W" k="82" />
<hkern u1="&#xc3;" u2="V" k="82" />
<hkern u1="&#xc3;" u2="T" k="143" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="O" k="41" />
<hkern u1="&#xc3;" u2="J" k="-266" />
<hkern u1="&#xc3;" u2="G" k="41" />
<hkern u1="&#xc3;" u2="C" k="41" />
<hkern u1="&#xc3;" u2="&#x27;" k="143" />
<hkern u1="&#xc3;" u2="&#x22;" k="143" />
<hkern u1="&#xc4;" u2="&#x201d;" k="143" />
<hkern u1="&#xc4;" u2="&#x2019;" k="143" />
<hkern u1="&#xc4;" u2="&#x178;" k="123" />
<hkern u1="&#xc4;" u2="&#x152;" k="41" />
<hkern u1="&#xc4;" u2="&#xdd;" k="123" />
<hkern u1="&#xc4;" u2="&#xd8;" k="41" />
<hkern u1="&#xc4;" u2="&#xd6;" k="41" />
<hkern u1="&#xc4;" u2="&#xd5;" k="41" />
<hkern u1="&#xc4;" u2="&#xd4;" k="41" />
<hkern u1="&#xc4;" u2="&#xd3;" k="41" />
<hkern u1="&#xc4;" u2="&#xd2;" k="41" />
<hkern u1="&#xc4;" u2="&#xc7;" k="41" />
<hkern u1="&#xc4;" u2="Y" k="123" />
<hkern u1="&#xc4;" u2="W" k="82" />
<hkern u1="&#xc4;" u2="V" k="82" />
<hkern u1="&#xc4;" u2="T" k="143" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="O" k="41" />
<hkern u1="&#xc4;" u2="J" k="-266" />
<hkern u1="&#xc4;" u2="G" k="41" />
<hkern u1="&#xc4;" u2="C" k="41" />
<hkern u1="&#xc4;" u2="&#x27;" k="143" />
<hkern u1="&#xc4;" u2="&#x22;" k="143" />
<hkern u1="&#xc5;" u2="&#x201d;" k="143" />
<hkern u1="&#xc5;" u2="&#x2019;" k="143" />
<hkern u1="&#xc5;" u2="&#x178;" k="123" />
<hkern u1="&#xc5;" u2="&#x152;" k="41" />
<hkern u1="&#xc5;" u2="&#xdd;" k="123" />
<hkern u1="&#xc5;" u2="&#xd8;" k="41" />
<hkern u1="&#xc5;" u2="&#xd6;" k="41" />
<hkern u1="&#xc5;" u2="&#xd5;" k="41" />
<hkern u1="&#xc5;" u2="&#xd4;" k="41" />
<hkern u1="&#xc5;" u2="&#xd3;" k="41" />
<hkern u1="&#xc5;" u2="&#xd2;" k="41" />
<hkern u1="&#xc5;" u2="&#xc7;" k="41" />
<hkern u1="&#xc5;" u2="Y" k="123" />
<hkern u1="&#xc5;" u2="W" k="82" />
<hkern u1="&#xc5;" u2="V" k="82" />
<hkern u1="&#xc5;" u2="T" k="143" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="O" k="41" />
<hkern u1="&#xc5;" u2="J" k="-266" />
<hkern u1="&#xc5;" u2="G" k="41" />
<hkern u1="&#xc5;" u2="C" k="41" />
<hkern u1="&#xc5;" u2="&#x27;" k="143" />
<hkern u1="&#xc5;" u2="&#x22;" k="143" />
<hkern u1="&#xc6;" u2="J" k="-123" />
<hkern u1="&#xc7;" u2="&#x152;" k="41" />
<hkern u1="&#xc7;" u2="&#xd8;" k="41" />
<hkern u1="&#xc7;" u2="&#xd6;" k="41" />
<hkern u1="&#xc7;" u2="&#xd5;" k="41" />
<hkern u1="&#xc7;" u2="&#xd4;" k="41" />
<hkern u1="&#xc7;" u2="&#xd3;" k="41" />
<hkern u1="&#xc7;" u2="&#xd2;" k="41" />
<hkern u1="&#xc7;" u2="&#xc7;" k="41" />
<hkern u1="&#xc7;" u2="Q" k="41" />
<hkern u1="&#xc7;" u2="O" k="41" />
<hkern u1="&#xc7;" u2="G" k="41" />
<hkern u1="&#xc7;" u2="C" k="41" />
<hkern u1="&#xc8;" u2="J" k="-123" />
<hkern u1="&#xc9;" u2="J" k="-123" />
<hkern u1="&#xca;" u2="J" k="-123" />
<hkern u1="&#xcb;" u2="J" k="-123" />
<hkern u1="&#xd0;" u2="&#x201e;" k="82" />
<hkern u1="&#xd0;" u2="&#x201a;" k="82" />
<hkern u1="&#xd0;" u2="&#x178;" k="20" />
<hkern u1="&#xd0;" u2="&#xdd;" k="20" />
<hkern u1="&#xd0;" u2="&#xc5;" k="41" />
<hkern u1="&#xd0;" u2="&#xc4;" k="41" />
<hkern u1="&#xd0;" u2="&#xc3;" k="41" />
<hkern u1="&#xd0;" u2="&#xc2;" k="41" />
<hkern u1="&#xd0;" u2="&#xc1;" k="41" />
<hkern u1="&#xd0;" u2="&#xc0;" k="41" />
<hkern u1="&#xd0;" u2="Z" k="20" />
<hkern u1="&#xd0;" u2="Y" k="20" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd0;" u2="W" k="20" />
<hkern u1="&#xd0;" u2="V" k="20" />
<hkern u1="&#xd0;" u2="T" k="61" />
<hkern u1="&#xd0;" u2="A" k="41" />
<hkern u1="&#xd0;" u2="&#x2e;" k="82" />
<hkern u1="&#xd0;" u2="&#x2c;" k="82" />
<hkern u1="&#xd2;" u2="&#x201e;" k="82" />
<hkern u1="&#xd2;" u2="&#x201a;" k="82" />
<hkern u1="&#xd2;" u2="&#x178;" k="20" />
<hkern u1="&#xd2;" u2="&#xdd;" k="20" />
<hkern u1="&#xd2;" u2="&#xc5;" k="41" />
<hkern u1="&#xd2;" u2="&#xc4;" k="41" />
<hkern u1="&#xd2;" u2="&#xc3;" k="41" />
<hkern u1="&#xd2;" u2="&#xc2;" k="41" />
<hkern u1="&#xd2;" u2="&#xc1;" k="41" />
<hkern u1="&#xd2;" u2="&#xc0;" k="41" />
<hkern u1="&#xd2;" u2="Z" k="20" />
<hkern u1="&#xd2;" u2="Y" k="20" />
<hkern u1="&#xd2;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="W" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="61" />
<hkern u1="&#xd2;" u2="A" k="41" />
<hkern u1="&#xd2;" u2="&#x2e;" k="82" />
<hkern u1="&#xd2;" u2="&#x2c;" k="82" />
<hkern u1="&#xd3;" u2="&#x201e;" k="82" />
<hkern u1="&#xd3;" u2="&#x201a;" k="82" />
<hkern u1="&#xd3;" u2="&#x178;" k="20" />
<hkern u1="&#xd3;" u2="&#xdd;" k="20" />
<hkern u1="&#xd3;" u2="&#xc5;" k="41" />
<hkern u1="&#xd3;" u2="&#xc4;" k="41" />
<hkern u1="&#xd3;" u2="&#xc3;" k="41" />
<hkern u1="&#xd3;" u2="&#xc2;" k="41" />
<hkern u1="&#xd3;" u2="&#xc1;" k="41" />
<hkern u1="&#xd3;" u2="&#xc0;" k="41" />
<hkern u1="&#xd3;" u2="Z" k="20" />
<hkern u1="&#xd3;" u2="Y" k="20" />
<hkern u1="&#xd3;" u2="X" k="41" />
<hkern u1="&#xd3;" u2="W" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="61" />
<hkern u1="&#xd3;" u2="A" k="41" />
<hkern u1="&#xd3;" u2="&#x2e;" k="82" />
<hkern u1="&#xd3;" u2="&#x2c;" k="82" />
<hkern u1="&#xd4;" u2="&#x201e;" k="82" />
<hkern u1="&#xd4;" u2="&#x201a;" k="82" />
<hkern u1="&#xd4;" u2="&#x178;" k="20" />
<hkern u1="&#xd4;" u2="&#xdd;" k="20" />
<hkern u1="&#xd4;" u2="&#xc5;" k="41" />
<hkern u1="&#xd4;" u2="&#xc4;" k="41" />
<hkern u1="&#xd4;" u2="&#xc3;" k="41" />
<hkern u1="&#xd4;" u2="&#xc2;" k="41" />
<hkern u1="&#xd4;" u2="&#xc1;" k="41" />
<hkern u1="&#xd4;" u2="&#xc0;" k="41" />
<hkern u1="&#xd4;" u2="Z" k="20" />
<hkern u1="&#xd4;" u2="Y" k="20" />
<hkern u1="&#xd4;" u2="X" k="41" />
<hkern u1="&#xd4;" u2="W" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="61" />
<hkern u1="&#xd4;" u2="A" k="41" />
<hkern u1="&#xd4;" u2="&#x2e;" k="82" />
<hkern u1="&#xd4;" u2="&#x2c;" k="82" />
<hkern u1="&#xd5;" u2="&#x201e;" k="82" />
<hkern u1="&#xd5;" u2="&#x201a;" k="82" />
<hkern u1="&#xd5;" u2="&#x178;" k="20" />
<hkern u1="&#xd5;" u2="&#xdd;" k="20" />
<hkern u1="&#xd5;" u2="&#xc5;" k="41" />
<hkern u1="&#xd5;" u2="&#xc4;" k="41" />
<hkern u1="&#xd5;" u2="&#xc3;" k="41" />
<hkern u1="&#xd5;" u2="&#xc2;" k="41" />
<hkern u1="&#xd5;" u2="&#xc1;" k="41" />
<hkern u1="&#xd5;" u2="&#xc0;" k="41" />
<hkern u1="&#xd5;" u2="Z" k="20" />
<hkern u1="&#xd5;" u2="Y" k="20" />
<hkern u1="&#xd5;" u2="X" k="41" />
<hkern u1="&#xd5;" u2="W" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="61" />
<hkern u1="&#xd5;" u2="A" k="41" />
<hkern u1="&#xd5;" u2="&#x2e;" k="82" />
<hkern u1="&#xd5;" u2="&#x2c;" k="82" />
<hkern u1="&#xd6;" u2="&#x201e;" k="82" />
<hkern u1="&#xd6;" u2="&#x201a;" k="82" />
<hkern u1="&#xd6;" u2="&#x178;" k="20" />
<hkern u1="&#xd6;" u2="&#xdd;" k="20" />
<hkern u1="&#xd6;" u2="&#xc5;" k="41" />
<hkern u1="&#xd6;" u2="&#xc4;" k="41" />
<hkern u1="&#xd6;" u2="&#xc3;" k="41" />
<hkern u1="&#xd6;" u2="&#xc2;" k="41" />
<hkern u1="&#xd6;" u2="&#xc1;" k="41" />
<hkern u1="&#xd6;" u2="&#xc0;" k="41" />
<hkern u1="&#xd6;" u2="Z" k="20" />
<hkern u1="&#xd6;" u2="Y" k="20" />
<hkern u1="&#xd6;" u2="X" k="41" />
<hkern u1="&#xd6;" u2="W" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="61" />
<hkern u1="&#xd6;" u2="A" k="41" />
<hkern u1="&#xd6;" u2="&#x2e;" k="82" />
<hkern u1="&#xd6;" u2="&#x2c;" k="82" />
<hkern u1="&#xd8;" u2="&#x201e;" k="82" />
<hkern u1="&#xd8;" u2="&#x201a;" k="82" />
<hkern u1="&#xd8;" u2="&#x178;" k="20" />
<hkern u1="&#xd8;" u2="&#xdd;" k="20" />
<hkern u1="&#xd8;" u2="&#xc5;" k="41" />
<hkern u1="&#xd8;" u2="&#xc4;" k="41" />
<hkern u1="&#xd8;" u2="&#xc3;" k="41" />
<hkern u1="&#xd8;" u2="&#xc2;" k="41" />
<hkern u1="&#xd8;" u2="&#xc1;" k="41" />
<hkern u1="&#xd8;" u2="&#xc0;" k="41" />
<hkern u1="&#xd8;" u2="Z" k="20" />
<hkern u1="&#xd8;" u2="Y" k="20" />
<hkern u1="&#xd8;" u2="X" k="41" />
<hkern u1="&#xd8;" u2="W" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="61" />
<hkern u1="&#xd8;" u2="A" k="41" />
<hkern u1="&#xd8;" u2="&#x2e;" k="82" />
<hkern u1="&#xd8;" u2="&#x2c;" k="82" />
<hkern u1="&#xd9;" u2="&#x201e;" k="41" />
<hkern u1="&#xd9;" u2="&#x201a;" k="41" />
<hkern u1="&#xd9;" u2="&#xc5;" k="20" />
<hkern u1="&#xd9;" u2="&#xc4;" k="20" />
<hkern u1="&#xd9;" u2="&#xc3;" k="20" />
<hkern u1="&#xd9;" u2="&#xc2;" k="20" />
<hkern u1="&#xd9;" u2="&#xc1;" k="20" />
<hkern u1="&#xd9;" u2="&#xc0;" k="20" />
<hkern u1="&#xd9;" u2="A" k="20" />
<hkern u1="&#xd9;" u2="&#x2e;" k="41" />
<hkern u1="&#xd9;" u2="&#x2c;" k="41" />
<hkern u1="&#xda;" u2="&#x201e;" k="41" />
<hkern u1="&#xda;" u2="&#x201a;" k="41" />
<hkern u1="&#xda;" u2="&#xc5;" k="20" />
<hkern u1="&#xda;" u2="&#xc4;" k="20" />
<hkern u1="&#xda;" u2="&#xc3;" k="20" />
<hkern u1="&#xda;" u2="&#xc2;" k="20" />
<hkern u1="&#xda;" u2="&#xc1;" k="20" />
<hkern u1="&#xda;" u2="&#xc0;" k="20" />
<hkern u1="&#xda;" u2="A" k="20" />
<hkern u1="&#xda;" u2="&#x2e;" k="41" />
<hkern u1="&#xda;" u2="&#x2c;" k="41" />
<hkern u1="&#xdb;" u2="&#x201e;" k="41" />
<hkern u1="&#xdb;" u2="&#x201a;" k="41" />
<hkern u1="&#xdb;" u2="&#xc5;" k="20" />
<hkern u1="&#xdb;" u2="&#xc4;" k="20" />
<hkern u1="&#xdb;" u2="&#xc3;" k="20" />
<hkern u1="&#xdb;" u2="&#xc2;" k="20" />
<hkern u1="&#xdb;" u2="&#xc1;" k="20" />
<hkern u1="&#xdb;" u2="&#xc0;" k="20" />
<hkern u1="&#xdb;" u2="A" k="20" />
<hkern u1="&#xdb;" u2="&#x2e;" k="41" />
<hkern u1="&#xdb;" u2="&#x2c;" k="41" />
<hkern u1="&#xdc;" u2="&#x201e;" k="41" />
<hkern u1="&#xdc;" u2="&#x201a;" k="41" />
<hkern u1="&#xdc;" u2="&#xc5;" k="20" />
<hkern u1="&#xdc;" u2="&#xc4;" k="20" />
<hkern u1="&#xdc;" u2="&#xc3;" k="20" />
<hkern u1="&#xdc;" u2="&#xc2;" k="20" />
<hkern u1="&#xdc;" u2="&#xc1;" k="20" />
<hkern u1="&#xdc;" u2="&#xc0;" k="20" />
<hkern u1="&#xdc;" u2="A" k="20" />
<hkern u1="&#xdc;" u2="&#x2e;" k="41" />
<hkern u1="&#xdc;" u2="&#x2c;" k="41" />
<hkern u1="&#xdd;" u2="&#x201e;" k="123" />
<hkern u1="&#xdd;" u2="&#x201a;" k="123" />
<hkern u1="&#xdd;" u2="&#x153;" k="102" />
<hkern u1="&#xdd;" u2="&#x152;" k="41" />
<hkern u1="&#xdd;" u2="&#xfc;" k="61" />
<hkern u1="&#xdd;" u2="&#xfb;" k="61" />
<hkern u1="&#xdd;" u2="&#xfa;" k="61" />
<hkern u1="&#xdd;" u2="&#xf9;" k="61" />
<hkern u1="&#xdd;" u2="&#xf8;" k="102" />
<hkern u1="&#xdd;" u2="&#xf6;" k="102" />
<hkern u1="&#xdd;" u2="&#xf5;" k="102" />
<hkern u1="&#xdd;" u2="&#xf4;" k="102" />
<hkern u1="&#xdd;" u2="&#xf3;" k="102" />
<hkern u1="&#xdd;" u2="&#xf2;" k="102" />
<hkern u1="&#xdd;" u2="&#xeb;" k="102" />
<hkern u1="&#xdd;" u2="&#xea;" k="102" />
<hkern u1="&#xdd;" u2="&#xe9;" k="102" />
<hkern u1="&#xdd;" u2="&#xe8;" k="102" />
<hkern u1="&#xdd;" u2="&#xe7;" k="102" />
<hkern u1="&#xdd;" u2="&#xe6;" k="102" />
<hkern u1="&#xdd;" u2="&#xe5;" k="102" />
<hkern u1="&#xdd;" u2="&#xe4;" k="102" />
<hkern u1="&#xdd;" u2="&#xe3;" k="102" />
<hkern u1="&#xdd;" u2="&#xe2;" k="102" />
<hkern u1="&#xdd;" u2="&#xe1;" k="102" />
<hkern u1="&#xdd;" u2="&#xe0;" k="102" />
<hkern u1="&#xdd;" u2="&#xd8;" k="41" />
<hkern u1="&#xdd;" u2="&#xd6;" k="41" />
<hkern u1="&#xdd;" u2="&#xd5;" k="41" />
<hkern u1="&#xdd;" u2="&#xd4;" k="41" />
<hkern u1="&#xdd;" u2="&#xd3;" k="41" />
<hkern u1="&#xdd;" u2="&#xd2;" k="41" />
<hkern u1="&#xdd;" u2="&#xc7;" k="41" />
<hkern u1="&#xdd;" u2="&#xc5;" k="123" />
<hkern u1="&#xdd;" u2="&#xc4;" k="123" />
<hkern u1="&#xdd;" u2="&#xc3;" k="123" />
<hkern u1="&#xdd;" u2="&#xc2;" k="123" />
<hkern u1="&#xdd;" u2="&#xc1;" k="123" />
<hkern u1="&#xdd;" u2="&#xc0;" k="123" />
<hkern u1="&#xdd;" u2="z" k="41" />
<hkern u1="&#xdd;" u2="u" k="61" />
<hkern u1="&#xdd;" u2="s" k="82" />
<hkern u1="&#xdd;" u2="r" k="61" />
<hkern u1="&#xdd;" u2="q" k="102" />
<hkern u1="&#xdd;" u2="p" k="61" />
<hkern u1="&#xdd;" u2="o" k="102" />
<hkern u1="&#xdd;" u2="n" k="61" />
<hkern u1="&#xdd;" u2="m" k="61" />
<hkern u1="&#xdd;" u2="g" k="41" />
<hkern u1="&#xdd;" u2="e" k="102" />
<hkern u1="&#xdd;" u2="d" k="102" />
<hkern u1="&#xdd;" u2="c" k="102" />
<hkern u1="&#xdd;" u2="a" k="102" />
<hkern u1="&#xdd;" u2="Q" k="41" />
<hkern u1="&#xdd;" u2="O" k="41" />
<hkern u1="&#xdd;" u2="G" k="41" />
<hkern u1="&#xdd;" u2="C" k="41" />
<hkern u1="&#xdd;" u2="A" k="123" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xdd;" u2="&#x2e;" k="123" />
<hkern u1="&#xdd;" u2="&#x2c;" k="123" />
<hkern u1="&#xde;" u2="&#x201e;" k="266" />
<hkern u1="&#xde;" u2="&#x201a;" k="266" />
<hkern u1="&#xde;" u2="&#xc5;" k="102" />
<hkern u1="&#xde;" u2="&#xc4;" k="102" />
<hkern u1="&#xde;" u2="&#xc3;" k="102" />
<hkern u1="&#xde;" u2="&#xc2;" k="102" />
<hkern u1="&#xde;" u2="&#xc1;" k="102" />
<hkern u1="&#xde;" u2="&#xc0;" k="102" />
<hkern u1="&#xde;" u2="Z" k="20" />
<hkern u1="&#xde;" u2="X" k="41" />
<hkern u1="&#xde;" u2="A" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="266" />
<hkern u1="&#xde;" u2="&#x2c;" k="266" />
<hkern u1="&#xe0;" u2="&#x201d;" k="20" />
<hkern u1="&#xe0;" u2="&#x2019;" k="20" />
<hkern u1="&#xe0;" u2="&#x27;" k="20" />
<hkern u1="&#xe0;" u2="&#x22;" k="20" />
<hkern u1="&#xe1;" u2="&#x201d;" k="20" />
<hkern u1="&#xe1;" u2="&#x2019;" k="20" />
<hkern u1="&#xe1;" u2="&#x27;" k="20" />
<hkern u1="&#xe1;" u2="&#x22;" k="20" />
<hkern u1="&#xe2;" u2="&#x201d;" k="20" />
<hkern u1="&#xe2;" u2="&#x2019;" k="20" />
<hkern u1="&#xe2;" u2="&#x27;" k="20" />
<hkern u1="&#xe2;" u2="&#x22;" k="20" />
<hkern u1="&#xe3;" u2="&#x201d;" k="20" />
<hkern u1="&#xe3;" u2="&#x2019;" k="20" />
<hkern u1="&#xe3;" u2="&#x27;" k="20" />
<hkern u1="&#xe3;" u2="&#x22;" k="20" />
<hkern u1="&#xe4;" u2="&#x201d;" k="20" />
<hkern u1="&#xe4;" u2="&#x2019;" k="20" />
<hkern u1="&#xe4;" u2="&#x27;" k="20" />
<hkern u1="&#xe4;" u2="&#x22;" k="20" />
<hkern u1="&#xe5;" u2="&#x201d;" k="20" />
<hkern u1="&#xe5;" u2="&#x2019;" k="20" />
<hkern u1="&#xe5;" u2="&#x27;" k="20" />
<hkern u1="&#xe5;" u2="&#x22;" k="20" />
<hkern u1="&#xe8;" u2="&#x201d;" k="20" />
<hkern u1="&#xe8;" u2="&#x2019;" k="20" />
<hkern u1="&#xe8;" u2="&#xfd;" k="41" />
<hkern u1="&#xe8;" u2="z" k="20" />
<hkern u1="&#xe8;" u2="y" k="41" />
<hkern u1="&#xe8;" u2="x" k="41" />
<hkern u1="&#xe8;" u2="w" k="41" />
<hkern u1="&#xe8;" u2="v" k="41" />
<hkern u1="&#xe8;" u2="&#x27;" k="20" />
<hkern u1="&#xe8;" u2="&#x22;" k="20" />
<hkern u1="&#xe9;" u2="&#x201d;" k="20" />
<hkern u1="&#xe9;" u2="&#x2019;" k="20" />
<hkern u1="&#xe9;" u2="&#xfd;" k="41" />
<hkern u1="&#xe9;" u2="z" k="20" />
<hkern u1="&#xe9;" u2="y" k="41" />
<hkern u1="&#xe9;" u2="x" k="41" />
<hkern u1="&#xe9;" u2="w" k="41" />
<hkern u1="&#xe9;" u2="v" k="41" />
<hkern u1="&#xe9;" u2="&#x27;" k="20" />
<hkern u1="&#xe9;" u2="&#x22;" k="20" />
<hkern u1="&#xea;" u2="&#x201d;" k="20" />
<hkern u1="&#xea;" u2="&#x2019;" k="20" />
<hkern u1="&#xea;" u2="&#xfd;" k="41" />
<hkern u1="&#xea;" u2="z" k="20" />
<hkern u1="&#xea;" u2="y" k="41" />
<hkern u1="&#xea;" u2="x" k="41" />
<hkern u1="&#xea;" u2="w" k="41" />
<hkern u1="&#xea;" u2="v" k="41" />
<hkern u1="&#xea;" u2="&#x27;" k="20" />
<hkern u1="&#xea;" u2="&#x22;" k="20" />
<hkern u1="&#xeb;" u2="&#x201d;" k="20" />
<hkern u1="&#xeb;" u2="&#x2019;" k="20" />
<hkern u1="&#xeb;" u2="&#xfd;" k="41" />
<hkern u1="&#xeb;" u2="z" k="20" />
<hkern u1="&#xeb;" u2="y" k="41" />
<hkern u1="&#xeb;" u2="x" k="41" />
<hkern u1="&#xeb;" u2="w" k="41" />
<hkern u1="&#xeb;" u2="v" k="41" />
<hkern u1="&#xeb;" u2="&#x27;" k="20" />
<hkern u1="&#xeb;" u2="&#x22;" k="20" />
<hkern u1="&#xf0;" u2="&#x201d;" k="20" />
<hkern u1="&#xf0;" u2="&#x2019;" k="20" />
<hkern u1="&#xf0;" u2="&#xfd;" k="41" />
<hkern u1="&#xf0;" u2="z" k="20" />
<hkern u1="&#xf0;" u2="y" k="41" />
<hkern u1="&#xf0;" u2="x" k="41" />
<hkern u1="&#xf0;" u2="w" k="41" />
<hkern u1="&#xf0;" u2="v" k="41" />
<hkern u1="&#xf0;" u2="&#x27;" k="20" />
<hkern u1="&#xf0;" u2="&#x22;" k="20" />
<hkern u1="&#xf2;" u2="&#x201d;" k="20" />
<hkern u1="&#xf2;" u2="&#x2019;" k="20" />
<hkern u1="&#xf2;" u2="&#xfd;" k="41" />
<hkern u1="&#xf2;" u2="z" k="20" />
<hkern u1="&#xf2;" u2="y" k="41" />
<hkern u1="&#xf2;" u2="x" k="41" />
<hkern u1="&#xf2;" u2="w" k="41" />
<hkern u1="&#xf2;" u2="v" k="41" />
<hkern u1="&#xf2;" u2="&#x27;" k="20" />
<hkern u1="&#xf2;" u2="&#x22;" k="20" />
<hkern u1="&#xf3;" u2="&#x201d;" k="20" />
<hkern u1="&#xf3;" u2="&#x2019;" k="20" />
<hkern u1="&#xf3;" u2="&#xfd;" k="41" />
<hkern u1="&#xf3;" u2="z" k="20" />
<hkern u1="&#xf3;" u2="y" k="41" />
<hkern u1="&#xf3;" u2="x" k="41" />
<hkern u1="&#xf3;" u2="w" k="41" />
<hkern u1="&#xf3;" u2="v" k="41" />
<hkern u1="&#xf3;" u2="&#x27;" k="20" />
<hkern u1="&#xf3;" u2="&#x22;" k="20" />
<hkern u1="&#xf4;" u2="&#x201d;" k="20" />
<hkern u1="&#xf4;" u2="&#x2019;" k="20" />
<hkern u1="&#xf4;" u2="&#xfd;" k="41" />
<hkern u1="&#xf4;" u2="z" k="20" />
<hkern u1="&#xf4;" u2="y" k="41" />
<hkern u1="&#xf4;" u2="x" k="41" />
<hkern u1="&#xf4;" u2="w" k="41" />
<hkern u1="&#xf4;" u2="v" k="41" />
<hkern u1="&#xf4;" u2="&#x27;" k="20" />
<hkern u1="&#xf4;" u2="&#x22;" k="20" />
<hkern u1="&#xf6;" u2="&#x201d;" k="41" />
<hkern u1="&#xf6;" u2="&#x2019;" k="41" />
<hkern u1="&#xf6;" u2="&#x27;" k="41" />
<hkern u1="&#xf6;" u2="&#x22;" k="41" />
<hkern u1="&#xf8;" u2="&#x201d;" k="20" />
<hkern u1="&#xf8;" u2="&#x2019;" k="20" />
<hkern u1="&#xf8;" u2="&#xfd;" k="41" />
<hkern u1="&#xf8;" u2="z" k="20" />
<hkern u1="&#xf8;" u2="y" k="41" />
<hkern u1="&#xf8;" u2="x" k="41" />
<hkern u1="&#xf8;" u2="w" k="41" />
<hkern u1="&#xf8;" u2="v" k="41" />
<hkern u1="&#xf8;" u2="&#x27;" k="20" />
<hkern u1="&#xf8;" u2="&#x22;" k="20" />
<hkern u1="&#xfd;" u2="&#x201e;" k="82" />
<hkern u1="&#xfd;" u2="&#x201d;" k="-82" />
<hkern u1="&#xfd;" u2="&#x201a;" k="82" />
<hkern u1="&#xfd;" u2="&#x2019;" k="-82" />
<hkern u1="&#xfd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xfd;" u2="&#x2e;" k="82" />
<hkern u1="&#xfd;" u2="&#x2c;" k="82" />
<hkern u1="&#xfd;" u2="&#x27;" k="-82" />
<hkern u1="&#xfd;" u2="&#x22;" k="-82" />
<hkern u1="&#xfe;" u2="&#x201d;" k="20" />
<hkern u1="&#xfe;" u2="&#x2019;" k="20" />
<hkern u1="&#xfe;" u2="&#xfd;" k="41" />
<hkern u1="&#xfe;" u2="z" k="20" />
<hkern u1="&#xfe;" u2="y" k="41" />
<hkern u1="&#xfe;" u2="x" k="41" />
<hkern u1="&#xfe;" u2="w" k="41" />
<hkern u1="&#xfe;" u2="v" k="41" />
<hkern u1="&#xfe;" u2="&#x27;" k="20" />
<hkern u1="&#xfe;" u2="&#x22;" k="20" />
<hkern u1="&#xff;" u2="&#x201e;" k="82" />
<hkern u1="&#xff;" u2="&#x201d;" k="-82" />
<hkern u1="&#xff;" u2="&#x201a;" k="82" />
<hkern u1="&#xff;" u2="&#x2019;" k="-82" />
<hkern u1="&#xff;" u2="&#x3f;" k="-41" />
<hkern u1="&#xff;" u2="&#x2e;" k="82" />
<hkern u1="&#xff;" u2="&#x2c;" k="82" />
<hkern u1="&#xff;" u2="&#x27;" k="-82" />
<hkern u1="&#xff;" u2="&#x22;" k="-82" />
<hkern u1="&#x152;" u2="J" k="-123" />
<hkern u1="&#x178;" u2="&#x201e;" k="123" />
<hkern u1="&#x178;" u2="&#x201a;" k="123" />
<hkern u1="&#x178;" u2="&#x153;" k="102" />
<hkern u1="&#x178;" u2="&#x152;" k="41" />
<hkern u1="&#x178;" u2="&#xfc;" k="61" />
<hkern u1="&#x178;" u2="&#xfb;" k="61" />
<hkern u1="&#x178;" u2="&#xfa;" k="61" />
<hkern u1="&#x178;" u2="&#xf9;" k="61" />
<hkern u1="&#x178;" u2="&#xf8;" k="102" />
<hkern u1="&#x178;" u2="&#xf6;" k="102" />
<hkern u1="&#x178;" u2="&#xf5;" k="102" />
<hkern u1="&#x178;" u2="&#xf4;" k="102" />
<hkern u1="&#x178;" u2="&#xf3;" k="102" />
<hkern u1="&#x178;" u2="&#xf2;" k="102" />
<hkern u1="&#x178;" u2="&#xeb;" k="102" />
<hkern u1="&#x178;" u2="&#xea;" k="102" />
<hkern u1="&#x178;" u2="&#xe9;" k="102" />
<hkern u1="&#x178;" u2="&#xe8;" k="102" />
<hkern u1="&#x178;" u2="&#xe7;" k="102" />
<hkern u1="&#x178;" u2="&#xe6;" k="102" />
<hkern u1="&#x178;" u2="&#xe5;" k="102" />
<hkern u1="&#x178;" u2="&#xe4;" k="102" />
<hkern u1="&#x178;" u2="&#xe3;" k="102" />
<hkern u1="&#x178;" u2="&#xe2;" k="102" />
<hkern u1="&#x178;" u2="&#xe1;" k="102" />
<hkern u1="&#x178;" u2="&#xe0;" k="102" />
<hkern u1="&#x178;" u2="&#xd8;" k="41" />
<hkern u1="&#x178;" u2="&#xd6;" k="41" />
<hkern u1="&#x178;" u2="&#xd5;" k="41" />
<hkern u1="&#x178;" u2="&#xd4;" k="41" />
<hkern u1="&#x178;" u2="&#xd3;" k="41" />
<hkern u1="&#x178;" u2="&#xd2;" k="41" />
<hkern u1="&#x178;" u2="&#xc7;" k="41" />
<hkern u1="&#x178;" u2="&#xc5;" k="123" />
<hkern u1="&#x178;" u2="&#xc4;" k="123" />
<hkern u1="&#x178;" u2="&#xc3;" k="123" />
<hkern u1="&#x178;" u2="&#xc2;" k="123" />
<hkern u1="&#x178;" u2="&#xc1;" k="123" />
<hkern u1="&#x178;" u2="&#xc0;" k="123" />
<hkern u1="&#x178;" u2="z" k="41" />
<hkern u1="&#x178;" u2="u" k="61" />
<hkern u1="&#x178;" u2="s" k="82" />
<hkern u1="&#x178;" u2="r" k="61" />
<hkern u1="&#x178;" u2="q" k="102" />
<hkern u1="&#x178;" u2="p" k="61" />
<hkern u1="&#x178;" u2="o" k="102" />
<hkern u1="&#x178;" u2="n" k="61" />
<hkern u1="&#x178;" u2="m" k="61" />
<hkern u1="&#x178;" u2="g" k="41" />
<hkern u1="&#x178;" u2="e" k="102" />
<hkern u1="&#x178;" u2="d" k="102" />
<hkern u1="&#x178;" u2="c" k="102" />
<hkern u1="&#x178;" u2="a" k="102" />
<hkern u1="&#x178;" u2="Q" k="41" />
<hkern u1="&#x178;" u2="O" k="41" />
<hkern u1="&#x178;" u2="G" k="41" />
<hkern u1="&#x178;" u2="C" k="41" />
<hkern u1="&#x178;" u2="A" k="123" />
<hkern u1="&#x178;" u2="&#x3f;" k="-41" />
<hkern u1="&#x178;" u2="&#x2e;" k="123" />
<hkern u1="&#x178;" u2="&#x2c;" k="123" />
<hkern u1="&#x2013;" u2="T" k="82" />
<hkern u1="&#x2014;" u2="T" k="82" />
<hkern u1="&#x2018;" u2="&#x178;" k="-20" />
<hkern u1="&#x2018;" u2="&#x153;" k="123" />
<hkern u1="&#x2018;" u2="&#xfc;" k="61" />
<hkern u1="&#x2018;" u2="&#xfb;" k="61" />
<hkern u1="&#x2018;" u2="&#xfa;" k="61" />
<hkern u1="&#x2018;" u2="&#xf9;" k="61" />
<hkern u1="&#x2018;" u2="&#xf8;" k="123" />
<hkern u1="&#x2018;" u2="&#xf6;" k="123" />
<hkern u1="&#x2018;" u2="&#xf5;" k="123" />
<hkern u1="&#x2018;" u2="&#xf4;" k="123" />
<hkern u1="&#x2018;" u2="&#xf3;" k="123" />
<hkern u1="&#x2018;" u2="&#xf2;" k="123" />
<hkern u1="&#x2018;" u2="&#xeb;" k="123" />
<hkern u1="&#x2018;" u2="&#xea;" k="123" />
<hkern u1="&#x2018;" u2="&#xe9;" k="123" />
<hkern u1="&#x2018;" u2="&#xe8;" k="123" />
<hkern u1="&#x2018;" u2="&#xe7;" k="123" />
<hkern u1="&#x2018;" u2="&#xe6;" k="82" />
<hkern u1="&#x2018;" u2="&#xe5;" k="82" />
<hkern u1="&#x2018;" u2="&#xe4;" k="82" />
<hkern u1="&#x2018;" u2="&#xe3;" k="82" />
<hkern u1="&#x2018;" u2="&#xe2;" k="82" />
<hkern u1="&#x2018;" u2="&#xe1;" k="82" />
<hkern u1="&#x2018;" u2="&#xe0;" k="123" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2018;" u2="&#xc5;" k="143" />
<hkern u1="&#x2018;" u2="&#xc4;" k="143" />
<hkern u1="&#x2018;" u2="&#xc3;" k="143" />
<hkern u1="&#x2018;" u2="&#xc2;" k="143" />
<hkern u1="&#x2018;" u2="&#xc1;" k="143" />
<hkern u1="&#x2018;" u2="&#xc0;" k="143" />
<hkern u1="&#x2018;" u2="u" k="61" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="61" />
<hkern u1="&#x2018;" u2="q" k="123" />
<hkern u1="&#x2018;" u2="p" k="61" />
<hkern u1="&#x2018;" u2="o" k="123" />
<hkern u1="&#x2018;" u2="n" k="61" />
<hkern u1="&#x2018;" u2="m" k="61" />
<hkern u1="&#x2018;" u2="g" k="61" />
<hkern u1="&#x2018;" u2="e" k="123" />
<hkern u1="&#x2018;" u2="d" k="123" />
<hkern u1="&#x2018;" u2="c" k="123" />
<hkern u1="&#x2018;" u2="a" k="82" />
<hkern u1="&#x2018;" u2="Y" k="-20" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-41" />
<hkern u1="&#x2018;" u2="A" k="143" />
<hkern u1="&#x2019;" u2="&#x178;" k="-20" />
<hkern u1="&#x2019;" u2="&#x153;" k="123" />
<hkern u1="&#x2019;" u2="&#xfc;" k="61" />
<hkern u1="&#x2019;" u2="&#xfb;" k="61" />
<hkern u1="&#x2019;" u2="&#xfa;" k="61" />
<hkern u1="&#x2019;" u2="&#xf9;" k="61" />
<hkern u1="&#x2019;" u2="&#xf8;" k="123" />
<hkern u1="&#x2019;" u2="&#xf6;" k="123" />
<hkern u1="&#x2019;" u2="&#xf5;" k="123" />
<hkern u1="&#x2019;" u2="&#xf4;" k="123" />
<hkern u1="&#x2019;" u2="&#xf3;" k="123" />
<hkern u1="&#x2019;" u2="&#xf2;" k="123" />
<hkern u1="&#x2019;" u2="&#xeb;" k="123" />
<hkern u1="&#x2019;" u2="&#xea;" k="123" />
<hkern u1="&#x2019;" u2="&#xe9;" k="123" />
<hkern u1="&#x2019;" u2="&#xe8;" k="123" />
<hkern u1="&#x2019;" u2="&#xe7;" k="123" />
<hkern u1="&#x2019;" u2="&#xe6;" k="82" />
<hkern u1="&#x2019;" u2="&#xe5;" k="82" />
<hkern u1="&#x2019;" u2="&#xe4;" k="82" />
<hkern u1="&#x2019;" u2="&#xe3;" k="82" />
<hkern u1="&#x2019;" u2="&#xe2;" k="82" />
<hkern u1="&#x2019;" u2="&#xe1;" k="82" />
<hkern u1="&#x2019;" u2="&#xe0;" k="123" />
<hkern u1="&#x2019;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2019;" u2="&#xc5;" k="143" />
<hkern u1="&#x2019;" u2="&#xc4;" k="143" />
<hkern u1="&#x2019;" u2="&#xc3;" k="143" />
<hkern u1="&#x2019;" u2="&#xc2;" k="143" />
<hkern u1="&#x2019;" u2="&#xc1;" k="143" />
<hkern u1="&#x2019;" u2="&#xc0;" k="143" />
<hkern u1="&#x2019;" u2="u" k="61" />
<hkern u1="&#x2019;" u2="s" k="61" />
<hkern u1="&#x2019;" u2="r" k="61" />
<hkern u1="&#x2019;" u2="q" k="123" />
<hkern u1="&#x2019;" u2="p" k="61" />
<hkern u1="&#x2019;" u2="o" k="123" />
<hkern u1="&#x2019;" u2="n" k="61" />
<hkern u1="&#x2019;" u2="m" k="61" />
<hkern u1="&#x2019;" u2="g" k="61" />
<hkern u1="&#x2019;" u2="e" k="123" />
<hkern u1="&#x2019;" u2="d" k="123" />
<hkern u1="&#x2019;" u2="c" k="123" />
<hkern u1="&#x2019;" u2="a" k="82" />
<hkern u1="&#x2019;" u2="Y" k="-20" />
<hkern u1="&#x2019;" u2="W" k="-41" />
<hkern u1="&#x2019;" u2="V" k="-41" />
<hkern u1="&#x2019;" u2="T" k="-41" />
<hkern u1="&#x2019;" u2="A" k="143" />
<hkern u1="&#x201a;" u2="&#x178;" k="123" />
<hkern u1="&#x201a;" u2="&#x152;" k="102" />
<hkern u1="&#x201a;" u2="&#xdd;" k="123" />
<hkern u1="&#x201a;" u2="&#xdc;" k="41" />
<hkern u1="&#x201a;" u2="&#xdb;" k="41" />
<hkern u1="&#x201a;" u2="&#xda;" k="41" />
<hkern u1="&#x201a;" u2="&#xd9;" k="41" />
<hkern u1="&#x201a;" u2="&#xd8;" k="102" />
<hkern u1="&#x201a;" u2="&#xd6;" k="102" />
<hkern u1="&#x201a;" u2="&#xd5;" k="102" />
<hkern u1="&#x201a;" u2="&#xd4;" k="102" />
<hkern u1="&#x201a;" u2="&#xd3;" k="102" />
<hkern u1="&#x201a;" u2="&#xd2;" k="102" />
<hkern u1="&#x201a;" u2="&#xc7;" k="102" />
<hkern u1="&#x201a;" u2="Y" k="123" />
<hkern u1="&#x201a;" u2="W" k="123" />
<hkern u1="&#x201a;" u2="V" k="123" />
<hkern u1="&#x201a;" u2="U" k="41" />
<hkern u1="&#x201a;" u2="T" k="143" />
<hkern u1="&#x201a;" u2="Q" k="102" />
<hkern u1="&#x201a;" u2="O" k="102" />
<hkern u1="&#x201a;" u2="G" k="102" />
<hkern u1="&#x201a;" u2="C" k="102" />
<hkern u1="&#x201c;" u2="&#x178;" k="-20" />
<hkern u1="&#x201c;" u2="&#x153;" k="123" />
<hkern u1="&#x201c;" u2="&#xfc;" k="61" />
<hkern u1="&#x201c;" u2="&#xfb;" k="61" />
<hkern u1="&#x201c;" u2="&#xfa;" k="61" />
<hkern u1="&#x201c;" u2="&#xf9;" k="61" />
<hkern u1="&#x201c;" u2="&#xf8;" k="123" />
<hkern u1="&#x201c;" u2="&#xf6;" k="123" />
<hkern u1="&#x201c;" u2="&#xf5;" k="123" />
<hkern u1="&#x201c;" u2="&#xf4;" k="123" />
<hkern u1="&#x201c;" u2="&#xf3;" k="123" />
<hkern u1="&#x201c;" u2="&#xf2;" k="123" />
<hkern u1="&#x201c;" u2="&#xeb;" k="123" />
<hkern u1="&#x201c;" u2="&#xea;" k="123" />
<hkern u1="&#x201c;" u2="&#xe9;" k="123" />
<hkern u1="&#x201c;" u2="&#xe8;" k="123" />
<hkern u1="&#x201c;" u2="&#xe7;" k="123" />
<hkern u1="&#x201c;" u2="&#xe6;" k="82" />
<hkern u1="&#x201c;" u2="&#xe5;" k="82" />
<hkern u1="&#x201c;" u2="&#xe4;" k="82" />
<hkern u1="&#x201c;" u2="&#xe3;" k="82" />
<hkern u1="&#x201c;" u2="&#xe2;" k="82" />
<hkern u1="&#x201c;" u2="&#xe1;" k="82" />
<hkern u1="&#x201c;" u2="&#xe0;" k="123" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-20" />
<hkern u1="&#x201c;" u2="&#xc5;" k="143" />
<hkern u1="&#x201c;" u2="&#xc4;" k="143" />
<hkern u1="&#x201c;" u2="&#xc3;" k="143" />
<hkern u1="&#x201c;" u2="&#xc2;" k="143" />
<hkern u1="&#x201c;" u2="&#xc1;" k="143" />
<hkern u1="&#x201c;" u2="&#xc0;" k="143" />
<hkern u1="&#x201c;" u2="u" k="61" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="61" />
<hkern u1="&#x201c;" u2="q" k="123" />
<hkern u1="&#x201c;" u2="p" k="61" />
<hkern u1="&#x201c;" u2="o" k="123" />
<hkern u1="&#x201c;" u2="n" k="61" />
<hkern u1="&#x201c;" u2="m" k="61" />
<hkern u1="&#x201c;" u2="g" k="61" />
<hkern u1="&#x201c;" u2="e" k="123" />
<hkern u1="&#x201c;" u2="d" k="123" />
<hkern u1="&#x201c;" u2="c" k="123" />
<hkern u1="&#x201c;" u2="a" k="82" />
<hkern u1="&#x201c;" u2="Y" k="-20" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-41" />
<hkern u1="&#x201c;" u2="A" k="143" />
<hkern u1="&#x201e;" u2="&#x178;" k="123" />
<hkern u1="&#x201e;" u2="&#x152;" k="102" />
<hkern u1="&#x201e;" u2="&#xdd;" k="123" />
<hkern u1="&#x201e;" u2="&#xdc;" k="41" />
<hkern u1="&#x201e;" u2="&#xdb;" k="41" />
<hkern u1="&#x201e;" u2="&#xda;" k="41" />
<hkern u1="&#x201e;" u2="&#xd9;" k="41" />
<hkern u1="&#x201e;" u2="&#xd8;" k="102" />
<hkern u1="&#x201e;" u2="&#xd6;" k="102" />
<hkern u1="&#x201e;" u2="&#xd5;" k="102" />
<hkern u1="&#x201e;" u2="&#xd4;" k="102" />
<hkern u1="&#x201e;" u2="&#xd3;" k="102" />
<hkern u1="&#x201e;" u2="&#xd2;" k="102" />
<hkern u1="&#x201e;" u2="&#xc7;" k="102" />
<hkern u1="&#x201e;" u2="Y" k="123" />
<hkern u1="&#x201e;" u2="W" k="123" />
<hkern u1="&#x201e;" u2="V" k="123" />
<hkern u1="&#x201e;" u2="U" k="41" />
<hkern u1="&#x201e;" u2="T" k="143" />
<hkern u1="&#x201e;" u2="Q" k="102" />
<hkern u1="&#x201e;" u2="O" k="102" />
<hkern u1="&#x201e;" u2="G" k="102" />
<hkern u1="&#x201e;" u2="C" k="102" />
</font>
</defs></svg> 