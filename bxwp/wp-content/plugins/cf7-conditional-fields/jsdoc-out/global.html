<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Global</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Global</h1>

    




<section>

<header>
    
        <h2></h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id="repeaterAddSub"><span class="type-signature"></span>repeaterAddSub<span class="signature">($form, repeaterDataId)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Append a new sub-entry to the repeater with the name <code>repeaterDataId</code> inside the form <code>$form</code></p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater. Normally this is simply the name of the repeater. However, in case of a nested repeater you need to append the name with the correct suffix. For example <code>my-nested-repeater__1__3</code>. Hint (check the <code>data-id</code> attribute in the HTML code to find the correct suffix)</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts_es6.js.html">scripts_es6.js</a>, <a href="scripts_es6.js.html#line1355">line 1355</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Namespaces</h3><ul><li><a href="wpcf7cf.html">wpcf7cf</a></li></ul><h3>Global</h3><ul><li><a href="global.html#repeaterAddSub">repeaterAddSub</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 3.6.5</a> on Mon Sep 07 2020 11:18:31 GMT+0200 (Central European Summer Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>