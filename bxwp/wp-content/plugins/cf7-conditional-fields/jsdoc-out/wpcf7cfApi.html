<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Class: wpcf7cfApi</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Class: wpcf7cfApi</h1>

    




<section>

<header>
    
        <h2><span class="attribs"><span class="type-signature"></span></span>wpcf7cfApi<span class="signature">()</span><span class="type-signature"></span></h2>
        
            <div class="class-description">public API</div>
        
    
</header>

<article>
    <div class="container-overview">
    
        

    
    <h2>Constructor</h2>
    

    
    <h4 class="name" id="wpcf7cfApi"><span class="type-signature"></span>new wpcf7cfApi<span class="signature">()</span><span class="type-signature"></span></h4>
    

    















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts_es6.js.html">scripts_es6.js</a>, <a href="scripts_es6.js.html#line1351">line 1351</a>
    </li></ul></dd>
    

    

    

    
</dl>




















    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".repeaterAddSub"><span class="type-signature">(static) </span>repeaterAddSub<span class="signature">($form, repeaterDataId)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    Append a new sub-entry to the repeater with the name `repeaterDataId` inside the form `$form`
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last">JQuery object or css-selector representing the form</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last">*data-id* attribute of the repeater. Normally this is simply the name of the repeater. However, in case of a nested repeater you need to append the name with the correct suffix. For example `my-nested-repeater__1__3`. Hint (check the `data-id` attribute in the HTML code to find the correct suffix)</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts_es6.js.html">scripts_es6.js</a>, <a href="scripts_es6.js.html#line1358">line 1358</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="wpcf7cfApi.html">wpcf7cfApi</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 3.6.5</a> on Mon Sep 07 2020 10:41:43 GMT+0200 (Central European Summer Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>