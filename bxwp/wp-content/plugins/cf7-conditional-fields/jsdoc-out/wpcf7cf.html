<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Namespace: wpcf7cf</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Namespace: wpcf7cf</h1>

    




<section>

<header>
    
        <h2>wpcf7cf</h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1007">line 1007</a>
    </li></ul></dd>
    

    

    

    
</dl>


        
    
    </div>

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id=".multistepMoveToStep"><span class="type-signature">(static) </span>multistepMoveToStep<span class="signature">($form, step)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Move to step number <code>step</code>, ignoring any validation.</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>step</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>


            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1464">line 1464</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".multistepMoveToStepWithValidation"><span class="type-signature">(static) </span>multistepMoveToStepWithValidation<span class="signature">($form, step)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Validate the current step, and move to step number <code>step</code> if validation passes.</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>step</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1475">line 1475</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".repeaterAddSub"><span class="type-signature">(static) </span>repeaterAddSub<span class="signature">($form, repeaterDataId)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Append a new sub-entry to the repeater with the name <code>repeaterDataId</code> inside the form <code>$form</code></p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater. Normally this is simply the name of the repeater. However, in case of a nested repeater you need to append the name with the correct suffix. For example <code>my-nested-repeater__1__3</code>. Hint (check the <code>data-id</code> attribute in the HTML code to find the correct suffix)</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1396">line 1396</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".repeaterAddSubAtIndex"><span class="type-signature">(static) </span>repeaterAddSubAtIndex<span class="signature">($form, repeaterDataId, index)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Insert a new sub-entry at the given <code>index</code> of the repeater with the name <code>repeaterDataId</code> inside the form <code>$form</code></p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>position where to insert the new sub-entry within the repeater</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1416">line 1416</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".repeaterRemoveSub"><span class="type-signature">(static) </span>repeaterRemoveSub<span class="signature">($form, repeaterDataId, index)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Remove the last sub-entry from the repeater with the <em>data-id</em> attribute of <code>repeaterDataId</code> inside the form <code>$form</code></p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>position where to insert the new sub-entry within the repeater</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1440">line 1440</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".repeaterRemoveSubAtIndex"><span class="type-signature">(static) </span>repeaterRemoveSubAtIndex<span class="signature">($form, repeaterDataId, index)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Remove the sub-entry at the given <code>index</code> of the repeater with the <em>data-id</em> attribute of <code>repeaterDataId</code> inside the form <code>$form</code></p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>position where to insert the new sub-entry within the repeater</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1428">line 1428</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
            

    

    
    <h4 class="name" id=".repeaterSetNumberOfSubs"><span class="type-signature">(static) </span>repeaterSetNumberOfSubs<span class="signature">($form, repeaterDataId, numberOfSubs)</span><span class="type-signature"></span></h4>
    

    



<div class="description">
    <p>Set the number of subs for the repeater with the <em>data-id</em> attribute of <code>repeaterDataId</code> inside the form <code>$form</code>.
Subs are either appended to or removed from the end of the repeater.</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>$form</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">JQuery</span>


            
            </td>

            

            

            <td class="description last"><p>JQuery object or css-selector representing the form</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>repeaterDataId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>


            
            </td>

            

            

            <td class="description last"><p><em>data-id</em> attribute of the repeater.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>numberOfSubs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>


            
            </td>

            

            

            <td class="description last"><p>position where to insert the new sub-entry within the repeater</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="scripts.js.html">scripts.js</a>, <a href="scripts.js.html#line1453">line 1453</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Namespaces</h3><ul><li><a href="wpcf7cf.html">wpcf7cf</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 4.0.2</a> on Thu Dec 05 2024 19:24:14 GMT+0100 (Central European Standard Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>