#wpcf7cf-new-entry {
  display: inline-block;
  display: none;
}

#wpcf7cf-delete-button {
  display: none;
}

#wpcf7cf-settings-text {
  width: 100%;
  height: 280px;
  font-family: "Courier New", Courier, monospace;
}

#wpcf7cf-conditional-panel {
  overflow-x: auto;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .label, #wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .field, #wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .description {
  padding: 10px;
  display: inline-block;
  vertical-align: middle;
  width: 10%;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .field {
  width: 20%;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .description {
  width: 40%;
  vertical-align: top;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line {
  border-bottom: 1px solid #dddddd;
  background-color: #f9f9f9;
  padding: 0 10px;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line:nth-child(2n) {
  background-color: #e9e9e9;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line input, #wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line select {
  width: 100%;
}
#wpcf7cf-conditional-panel .ui-autocomplete-term {
  font-weight: bold;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line > .label.editable {
  position: relative;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line > .label.editable input {
  background-color: transparent;
  border-width: 0;
  outline: none;
  box-shadow: none;
  padding: 0;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line > .label.editable input:after {
  content: "edit";
  display: block;
  width: 10px;
  right: 0;
  position: absolute;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line > .label.editable input:focus {
  background: #fff;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
  border: 1px solid #ddd;
  padding: 2px;
}
#wpcf7cf-conditional-panel .wpcf7cf-admin-wrap .option-line > .label.editable input:focus:after {
  content: "";
}
#wpcf7cf-conditional-panel .wpcf7cf-and {
  display: inline-block;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rules, #wpcf7cf-conditional-panel .wpcf7cf-if {
  display: inline-block;
  vertical-align: top;
}
#wpcf7cf-conditional-panel #wpcf7cf-entries input, #wpcf7cf-conditional-panel #wpcf7cf-entries select, #wpcf7cf-conditional-panel .and-button, #wpcf7cf-conditional-panel .delete-button, #wpcf7cf-conditional-panel #wpcf7cf-entries .if-txt, #wpcf7cf-conditional-panel #wpcf7cf-entries .label, #wpcf7cf-conditional-panel #wpcf7cf-add-button {
  position: relative;
  display: inline-block;
  vertical-align: bottom;
  margin: 2px;
  padding: 3px;
  background: #fefefe;
  border: 1px solid #bababa;
  box-shadow: none;
  height: 22px;
  line-height: 22px;
  min-height: 22px;
  box-sizing: content-box;
}
#wpcf7cf-conditional-panel #wpcf7cf-entries .label {
  background-color: transparent;
  border: none;
}
#wpcf7cf-conditional-panel .and-button, #wpcf7cf-conditional-panel .delete-button, #wpcf7cf-conditional-panel #wpcf7cf-add-button {
  border: 1px solid #4ed521;
  color: #007b04;
  cursor: pointer;
  font-size: 11px;
  font-weight: bold;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently */
  background-color: rgba(75, 169, 61, 0.11);
}
#wpcf7cf-conditional-panel #wpcf7cf-add-button {
  margin-top: 10px;
  text-align: center;
  padding-left: 20px;
  padding-right: 20px;
}
#wpcf7cf-conditional-panel .and-button:hover {
  background-color: rgba(75, 169, 61, 0.2);
}
#wpcf7cf-conditional-panel .delete-button {
  border: 1px solid #bababa;
  color: #858585;
  background-color: transparent;
  margin-left: 42px;
}
#wpcf7cf-conditional-panel .delete-button:hover {
  background-color: rgba(133, 133, 133, 0.11);
}
#wpcf7cf-conditional-panel .and-button {
  display: none;
  width: 30px;
  text-align: center;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule:first-child .and-button {
  display: inline-block;
  vertical-align: top;
  height: 22px;
  position: absolute;
  line-height: 22px;
  right: 53px;
  top: 0;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule {
  margin-bottom: 4px;
  height: 30px;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule .if-txt, #wpcf7cf-conditional-panel .wpcf7cf-if > .label {
  cursor: n-resize;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule .if-txt:before {
  content: "and";
  position: absolute;
  width: 30px;
  text-align: right;
  left: -34px;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule:first-child .if-txt:before {
  display: none;
}
#wpcf7cf-conditional-panel .wpcf7cf-inner-container {
  min-width: 800px;
}
#wpcf7cf-conditional-panel .entry {
  box-sizing: border-box;
  display: flex;
}
#wpcf7cf-conditional-panel .entry .wpcf7cf-if {
  width: 189px;
}
#wpcf7cf-conditional-panel .then-field-select {
  width: 130px;
}
#wpcf7cf-conditional-panel .entry .wpcf7cf-and-rules {
  flex: 1;
  position: relative;
}
#wpcf7cf-conditional-panel .wpcf7cf-and-rule {
  display: flex;
}
#wpcf7cf-conditional-panel .if-txt {
  width: 14px;
  text-align: center;
}
#wpcf7cf-conditional-panel .operator {
  /*width:140px;*/
}
#wpcf7cf-conditional-panel .if-value {
  flex: 1;
  margin-right: 3px !important;
}

.wpcf7cf-list li {
  list-style: disc;
  margin-left: 20px;
}

/* The switch - the box around the slider */
.wpcf7cf-switch {
  position: absolute;
  display: block;
  top: 20px;
  right: 20px;
}
.wpcf7cf-switch .label {
  margin-right: 4px;
  display: inline-block;
  vertical-align: top;
}
.wpcf7cf-switch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 17px;
  /* Hide default HTML checkbox */
  /* The slider */
  /* Rounded sliders */
}
.wpcf7cf-switch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.wpcf7cf-switch .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.wpcf7cf-switch .switch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.3s;
}
.wpcf7cf-switch .switch input:checked + .slider {
  background-color: #1e8cbe;
}
.wpcf7cf-switch .switch input:focus + .slider {
  box-shadow: 0 0 1px #1e8cbe;
}
.wpcf7cf-switch .switch input:checked + .slider:before {
  -webkit-transform: translateX(13px);
  -ms-transform: translateX(13px);
  transform: translateX(13px);
}
.wpcf7cf-switch .switch .slider.round {
  border-radius: 17px;
}
.wpcf7cf-switch .switch .slider.round:before {
  border-radius: 50%;
}

#wpcf7cf-conditional-panel {
  position: relative;
}

.wpcf7cf-notice {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-left-width: 4px;
  border-left-color: #ffb900;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  margin: 5px 0 2px;
  padding: 1px 12px;
}

/*# sourceMappingURL=admin-style.css.map */
