/* initially hide all groups (even before J<PERSON> is loaded), so the page will never render them while loading */
[data-class="wpcf7cf_group"], .wpcf7cf_remove, .wpcf7cf_add {
    display:none;
}

.wpcf7cf_repeater_sub {
    margin-bottom: 20px;
}

.wpcf7cf_repeater_controls, .wpcf7cf_step_controls {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20px;
}

.wpcf7cf_multistep .wpcf7cf_step {
    /* display:none; */
    width: 100%;
}

.wpcf7cf_multistep .wpcf7cf_step .step-title {
    display: none;
}

.wpcf7cf_multistep .wpcf7cf_steps-dots {
    display: flex;
    width: 100%;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.wpcf7cf_multistep .wpcf7cf_steps-dots .dot .step-index {
    display: inline-block;
    border-radius: 50%;
    background: #dfdfdf;
    color: #000000;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
}

.wpcf7cf_multistep .wpcf7cf_steps-dots .dot {
    border-bottom: 5px solid #dfdfdf;
    text-align: center;
    flex: 1;
    padding: 15px;
    min-width: 120px;
}
.wpcf7cf_multistep .wpcf7cf_steps-dots .dot.completed {
    border-bottom: 5px solid #333;
}
.wpcf7cf_multistep .wpcf7cf_steps-dots .dot.active {
    border-bottom: 5px solid #333;
    font-weight: bold;
}

.wpcf7cf_multistep .wpcf7cf_steps-dots .dot.completed .step-index {
    background-color: #333;
    color: #ffffff;
}

.wpcf7cf_multistep .wpcf7cf_steps-dots .dot.active .step-index {
    background-color: #333;
    color: #ffffff;
}

.wpcf7cf_step_controls .disabled {
    pointer-events: none;
    cursor: default;
    opacity: .5;
}