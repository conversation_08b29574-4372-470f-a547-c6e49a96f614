<?php

namespace CanaisDigitais;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static function fill_with_complementary_items(array $available_items, array $complementary_items, int $total = 3)
   {
      $available_item_count = count($available_items);

      if ($available_item_count === $total) {
         return $available_items;
      }

      if ($available_item_count < $total) {
         $filled_items = array_merge(
            $available_items,
            array_slice($complementary_items, 0, $total - $available_item_count)
         );

         return $filled_items;
      }
   }

   // TODO: moving to essentials
   public static function hex2rgb(string $hex, float $opacity = 1, int $brightness = 100)
   {
      $hex = str_replace('#', '', $hex);

      if (6 === strlen($hex)) {
         $colors = str_split($hex, 2);
      } elseif (3 === strlen($hex)) {
         $colors = array_map(fn($color) => "$color$color", str_split($hex, 1));
      } else {
         return '';
      }

      $colors = array_map(function ($color) use ($brightness) {
         $color = hexdec($color);

         if ($brightness !== 100) {
            $color = $color + round(($brightness - 50) * 2.55);
            $color = max(0, min(255, $color));
         }

         return $color;
      }, $colors);

      $rgb = implode(', ', $colors);

      if (1 === $opacity) {
         return "rgb($rgb)";
      }

      if ($opacity < 0 || 1 < $opacity) {
         return '';
      }

      return "rgba($rgb, $opacity)";
   }

   // TODO: moving to essentials
   public static function current_page(bool $is_first = false, bool $raw = false)
   {
      $paged = (int) get_query_var('paged', 0);

      if (!$raw && 0 === $paged) {
         $paged = 1;
      }

      if ($is_first) {
         return in_array($paged, [0, 1]);
      }

      return $paged;
   }
   static public function get_closest_double(int $size, array $sizes)
   {
      $double = $size * 2;
      $closest_double = null;

      foreach ($sizes as $current_size) {

         if ($closest_double === null || abs($double - $current_size) < abs($double - $closest_double)) {
            $closest_double = $current_size;
         }
      }

      return $closest_double;
   }

   static public function get_array_key_by_value($array, $key, $value)
   {
      foreach ($array as $currentKey => $item) {
         if (isset($item[$key]) && $item[$key] === $value) {
            return $currentKey;
         }
      }
      return null;
   }
}
