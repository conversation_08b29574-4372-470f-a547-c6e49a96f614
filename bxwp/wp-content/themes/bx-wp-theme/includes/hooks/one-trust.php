<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('bx_head_scripts', 'bx_canais_digitais_one_trust');
function bx_canais_digitais_one_trust()
{

   $data_domain = get_field('bx_onetrust_data_domain', 'tags-and-pixels');

   if (empty($data_domain)) {
      return;
   }

?>
   <script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" type="text/javascript" charset="UTF-8" data-domain-script="<?php echo esc_html($data_domain); ?>" defer>
   </script>

   <script type="text/javascript">
      function OptanonWrapper() {

      }
   </script>

<?php
}
