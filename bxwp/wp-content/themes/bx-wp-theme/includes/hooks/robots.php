<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('wp_loaded', 'bx_cd_prevent_any_change_robots_txt');
function bx_cd_prevent_any_change_robots_txt()
{
   global $wp_filter;

   if (isset($wp_filter['robots_txt']->callbacks) && is_array($wp_filter['robots_txt']->callbacks)) {

      foreach ($wp_filter['robots_txt']->callbacks as $callback_priority => $callback) {
         foreach ($callback as $function_key => $function) {

            if ('filter_robots' === $function['function'][1]) {
               unset($wp_filter['robots_txt']->callbacks[$callback_priority][$function_key]);
            }
         }
      }
   }
}

add_filter('robots_txt', 'bx_cd_custom_robots_txt', 10, 2);
function bx_cd_custom_robots_txt($output)
{
   $sitemap_url = home_url('sitemap.xml');

   $output = <<<ROBOTS
User-agent: *
Disallow: */feed/
Disallow: /trackback/
Disallow: /wp-admin/
Disallow: /wp-content/
Disallow: /wp-includes/
Disallow: /xmlrpc.php

Allow: /wp-content/uploads/
Allow: /wp-content/plugins/
Allow: /wp-includes/js/
Allow: /wp-content/cache/
Allow: /*.css
Allow: /*.js
Allow: /wp-includes/css/
Allow: /wp-content/themes/

Sitemap: {$sitemap_url}
ROBOTS;

   return $output;
}
