<?php

use <PERSON>ais<PERSON><PERSON>tais\Utils;

if (!defined('ABSPATH')) {
   exit;
}

add_action('init', 'bx_cd_add_image_sizes');
function bx_cd_add_image_sizes()
{
   add_image_size('web-stories', 320, 569, true);
}

function bx_cd_get_all_image_sizes()
{
   $registered_sizes = wp_get_registered_image_subsizes();
   $image_sizes      = [];

   foreach ($registered_sizes as $size => $args) {

      $width  = isset($args['width']) ? (int) $args['width'] : 0;
      $height = isset($args['height']) ? (int) $args['height'] : 0;
      $crop   = isset($args['crop']) ? (bool) $args['crop'] : false;

      $image_sizes[$size] = ['width' => $width, 'height' => $height, 'crop' => $crop];
   }

   return $image_sizes;
}

add_filter('wp_get_attachment_image', 'bx_cd_convert_image_sizes', 10, 5);
function bx_cd_convert_image_sizes($html, $attachment_ID, $target_size, $icon, $attr)
{
   if (is_admin()) {
      return $html;
   }

   unset($icon);

   $picture_widths = [
      300,
      768,
      1024,
   ];

   $all_images      = bx_cd_get_all_image_sizes();
   $has_retina      = isset($attr['has_retina']) && $attr['has_retina'] != 0;
   $target_size_num = (int) $all_images[$target_size]['width'];

   if (!isset($all_images[$target_size]) || empty($attr['src']) || empty($attr['sizes']) || empty($attr['has_retina'])) {
      return $html;
   }

   $mapped_sizes = [];
   $src_sizes    = [];

   if (isset($attr['srcset'])) {

      $src_sizes = explode(',', $attr['srcset']);

      foreach ($src_sizes as $src_size) {
         $src_size = trim($src_size);

         if (preg_match('/(https?:\/\/[^\s]+)\s(\d+)w/', $src_size, $matches)) {
            $url   = $matches[1];
            $width = (int) $matches[2];

            $size = 'unknown';
            foreach ($all_images as $key => $details) {
               if ((int) $details['width'] === $width) {
                  $size = $key;
                  break;
               }
            }

            $mapped_sizes[] = [
               'url'   => $url,
               'width' => $width,
               'size'  => $size
            ];
         }
      }

      usort($mapped_sizes, function ($a, $b) {
         return $b['width'] <=> $a['width'];
      });
   } else {

      $mapped_sizes[] = [
         'url'   => $attr['src'],
         'width' => $target_size_num,
         'size'  => $target_size
      ];
   }

   $sources = $mapped_sizes;

   $first_size            = Utils::get_array_key_by_value($sources, 'width', 150);
   $second_size           = Utils::get_array_key_by_value($sources, 'width', 300);
   $selected_size_for_src = isset($sources[$second_size]) ? $second_size : $first_size;

   $attr['src'] = isset($sources[$selected_size_for_src]['url']) ?
      $sources[$selected_size_for_src]['url'] :
      $attr['src'];

   unset($sources[$first_size], $sources[$second_size]);
   unset($attr['srcset'], $attr['sizes'], $attr['has_retina']);

   preg_match('/(width="\d*" height="\d*")/', $html, $hwstring);

   $html = rtrim("<img {$hwstring[1]}");

   $container_class = '';

   if (!empty($attr['class'])) {
      $container_class = "class=\"{$attr['class']}\" ";
   }

   foreach ($attr as $name => $value) {
      $html .= " $name=" . '"' . $value . '"';
   }

   $html .= '>';

   $output_sources = [];

   foreach ($sources as $size) {

      $width = (int) $size['width'];
      $url   = $size['url'];

      if ($width > $target_size_num || !in_array($width, $picture_widths)) {
         continue;
      }

      if ($has_retina && $width >= 768) {

         $double_size_width = Utils::get_closest_double($width, array_column($sources, 'width'));
         $retina_size       = Utils::get_array_key_by_value($sources, 'width', $double_size_width) ?? '';

         if (!empty($retina_size) && isset($sources[$retina_size]['url']) && !empty($sources[$retina_size]['url'])) {

            $retina_url = $sources[$retina_size]['url'];
         } else {

            $double_size_width = Utils::get_closest_double($width, array_column($all_images, 'width'));
            $double_size       = Utils::get_array_key_by_value($all_images, 'width', $double_size_width) ?? '2048x2048';
            $retina_url        = wp_get_attachment_image_src($attachment_ID, $double_size)[0];
         }

         $output_sources[] = "<source media=\"(min-width: {$width}px)\" srcset=\"{$retina_url} 2x, {$url} 1x\">";
      } else {

         $output_sources[] = "<source media=\"(min-width: {$width}px)\" srcset=\"{$url}\">";
      }
   }

   $sources = implode('', $output_sources);

   return <<<IMAGE
	<picture $container_class data-cy="image-$target_size">
		$sources
		$html
	</picture>
IMAGE;
}
