<?php

use CanaisDigitais\Utils;

if (!defined('ABSPATH')) {
   exit;
}

add_filter('wp_get_custom_css', 'bx_cd_custom_global_colors');
function bx_cd_custom_global_colors($css)
{
   $primary_color   = get_theme_mod('bx_cd_primary_color', '#1CBF69');
   $secondary_color = get_theme_mod('bx_cd_secondary_color', '#003d5b');

   $primary_rgb_10  = Utils::hex2rgb($primary_color, 0.1);
   $primary_rgb_100 = Utils::hex2rgb($primary_color, 1, 90);
   $primary_rgb_200 = Utils::hex2rgb($primary_color, 1, 80);
   $primary_rgb_300 = Utils::hex2rgb($primary_color, 1, 60);
   $primary_rgb_500 = Utils::hex2rgb($primary_color);
   $primary_rgb_800 = Utils::hex2rgb($primary_color, 1, 40);

   $secondary_rgb_100 = Utils::hex2rgb($secondary_color, 1, 90);
   $secondary_rgb_200 = Utils::hex2rgb($secondary_color, 1, 80);
   $secondary_rgb_300 = Utils::hex2rgb($secondary_color, 1, 60);
   $secondary_rgb_500 = Utils::hex2rgb($secondary_color);
   $secondary_rgb_800 = Utils::hex2rgb($secondary_color, 1, 40);

   $css .= <<<STYLE
:root {
   --primary-color-10: $primary_rgb_10;
   --primary-color-100: $primary_rgb_100;
   --primary-color-200: $primary_rgb_200;
   --primary-color-300: $primary_rgb_300;
   --primary-color-500: $primary_rgb_500;
   --primary-color-800: $primary_rgb_800;

   --secondary-color-100: $secondary_rgb_100;
   --secondary-color-200: $secondary_rgb_200;
   --secondary-color-300: $secondary_rgb_300;
   --secondary-color-500: $secondary_rgb_500;
   --secondary-color-800: $secondary_rgb_800;
}
STYLE;

   return $css;
}

add_action('admin_head', 'wp_custom_css_cb', 101);
