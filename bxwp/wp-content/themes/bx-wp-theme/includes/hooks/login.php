<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('after_setup_theme', 'create_profile_page');
function create_profile_page()
{
   $profile_page = get_page_by_path('perfil');

   if (!$profile_page) {

      $page_args = array(
         'post_title'    => 'Perfil',
         'post_name'     => 'perfil',
         'post_content'  => '',
         'post_status'   => 'publish',
         'post_type'     => 'page'
      );

      $new_page_id = wp_insert_post($page_args);

      if (is_wp_error($new_page_id)) {

         echo "Error creating the 'Perfil' page: " . $new_page_id->get_error_message();
      }
   }
}

add_action('wp', 'redirect_profile_page');
function redirect_profile_page()
{
   if (!isset($_COOKIE['IRIS_AUTH']) && is_page('perfil')) {
      wp_safe_redirect(home_url());
      exit;
   }
}

// add_action('wp_body_open', 'bx_cd_iris_script', 30);
function bx_cd_iris_script()
{

   $scripts = get_field('bx_iris_script', 'tags-and-pixels');

   if (!empty($scripts)) {

      echo $scripts;
   }
}

// add_action('wp_footer', 'bx_cd_iris_cdn', 50);
function bx_cd_iris_cdn()
{

   $scripts = [
      'https://static.iris.informa.com/widgets/v3.1/iris-identity.js',
      'https://static.iris.informa.com/widgets/v3/iris-profilemgr.js'
   ];

   if (!empty($scripts)) {
      foreach ($scripts as $script) {
         echo "<script src='$script'></script>";
      }
   }
}
