<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('init', 'bx_cd_wp_head_change_yoast_action');
function bx_cd_wp_head_change_yoast_action()
{
   if (!class_exists('Yoast\WP\SEO\Integrations\Front_End_Integration')) {
      return;
   }

   $front_end = YoastSEO()->classes->get(Yoast\WP\SEO\Integrations\Front_End_Integration::class);

   remove_action('wpseo_head', [$front_end, 'present_head'], -9999);
   add_action('bx_head_metas', [$front_end, 'present_head']);
}
