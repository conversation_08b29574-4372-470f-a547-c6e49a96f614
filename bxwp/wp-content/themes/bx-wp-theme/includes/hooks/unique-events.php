<?php

if (!defined('ABSPATH')) {
   exit;
}

function bx_cd_get_ran_scheduled_unique_events(string $event_name)
{
   $ran_scheduled_unique_events = get_option('bx_ran_scheduled_unique_events', []);

   return in_array($event_name, $ran_scheduled_unique_events);
}

function bx_cd_add_ran_scheduled_unique_events(string $event_name)
{
   $ran_scheduled_unique_events = get_option('bx_ran_scheduled_unique_events', []);
   $ran_scheduled_unique_events[] = $event_name;

   update_option('bx_ran_scheduled_unique_events', $ran_scheduled_unique_events, 'no');
}
