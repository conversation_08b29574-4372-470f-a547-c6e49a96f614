<?php

namespace CanaisDigitais\Page;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   static private function get_page_by_template(string $template)
   {
      $pages = get_pages([
         'meta_key' => '_wp_page_template',
         'meta_value' => $template
      ]);

      return !empty($pages) ? reset($pages) : null;
   }

   static public function get_specialist_archive_page()
   {
      return self::get_page_by_template('pages/template-authors.php');
   }

   static public function get_form_newsletter_page()
   {
      return self::get_page_by_template('pages/template-form-newsletter.php');
   }
}
