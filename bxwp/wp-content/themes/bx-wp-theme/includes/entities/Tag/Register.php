<?php

namespace CanaisDigitais\Tag;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      add_filter('init', [$this, 'update_tag_base_option']);
      add_action('acf/include_fields', [$this, 'register_tag_fields']);
      add_action('pre_get_posts', [$this, 'modify_tag_page_query']);
   }

   public function update_tag_base_option()
   {
      $new_tag_base     = 'tags';
      $current_tag_base = get_option('tag_base');

      if ($current_tag_base === $new_tag_base) {
         return;
      }
      update_option('tag_base', $new_tag_base);
   }

   public function modify_tag_page_query($query)
   {
      if (!is_admin() && $query->is_tag() && $query->is_main_query()) {
         $query->set('posts_per_page', 11);
      }
   }

   public function register_tag_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key' => 'group_680bf88e4cf55',
         'title' => 'Tags',
         'fields' => array(
            array(
               'key' => 'field_680bf88e6aa4a',
               'label' => 'Cor destacada',
               'name' => 'post_tag_color',
               'aria-label' => '',
               'type' => 'color_picker',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'default_value' => '#1CBF69',
               'enable_opacity' => 0,
               'return_format' => 'string',
               'allow_in_bindings' => 1,
            ),
            array(
               'key' => 'field_680bf88e6aa4e',
               'label' => 'Imagem de Fundo',
               'name' => 'post_tag_background_image',
               'aria-label' => '',
               'type' => 'image',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'return_format' => 'url',
               'library' => 'all',
               'min_width' => '',
               'min_height' => '',
               'min_size' => '',
               'max_width' => '',
               'max_height' => '',
               'max_size' => '',
               'mime_types' => '',
               'allow_in_bindings' => 1,
               'preview_size' => 'thumbnail',
            ),
            array(
               'key' => 'field_680bf88e6aa51',
               'label' => 'Fundo com opacidade',
               'name' => 'post_tag_background_opacity',
               'aria-label' => '',
               'type' => 'color_picker',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'default_value' => '',
               'enable_opacity' => 1,
               'return_format' => 'string',
               'allow_in_bindings' => 1,
            ),
            array(
               'key' => 'field_680bf88e6aa59',
               'label' => 'Cor do Texto',
               'name' => 'post_tag_text_color',
               'aria-label' => '',
               'type' => 'color_picker',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'default_value' => '#FFFFFF',
               'enable_opacity' => 0,
               'return_format' => 'string',
               'allow_in_bindings' => 1,
            ),
            array(
               'key' => 'field_680bf88e6aa5e',
               'label' => 'Mostrar cor no bloco interno',
               'name' => 'post_tag_show_color',
               'aria-label' => '',
               'type' => 'true_false',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'message' => '',
               'default_value' => 1,
               'allow_in_bindings' => 1,
               'ui_on_text' => '',
               'ui_off_text' => '',
               'ui' => 1,
            ),
         ),
         'location' => array(
            array(
               array(
                  'param' => 'taxonomy',
                  'operator' => '==',
                  'value' => 'post_tag',
               ),
            ),
         ),
         'menu_order' => 0,
         'position' => 'normal',
         'style' => 'default',
         'label_placement' => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen' => '',
         'active' => true,
         'description' => '',
         'show_in_rest' => 0,
      ));
   }
}
