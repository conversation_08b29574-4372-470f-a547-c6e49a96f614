<?php

namespace CanaisDigitais\Taxonomy;

class Taxonomy
{
   protected \WP_Term $term;
   private $default_color = '#7a7a7a';

   public function __construct($term)
   {
      if (empty($term)) {
         return null;
      }

      $term = get_term($term);

      if (is_wp_error($term)) {
         return $term;
      }

      $this->term = $term;
   }

   public function get_the_ID()
   {
      return $this->term->term_id;
   }

   public function get_slug()
   {
      return $this->term->slug;
   }

   public function get_name()
   {
      return $this->term->name;
   }

   public function get_link()
   {
      return get_term_link($this->get_the_ID());
   }

   public function get_description()
   {
      return $this->term->description;
   }

   public function get_taxonomy()
   {
      return $this->term->taxonomy;
   }

   public function get_color()
   {
      $color = get_field("{$this->get_taxonomy()}_color", $this->term->taxonomy . '_' . $this->get_the_ID());

      if (empty($color)) {
         $color = get_field("{$this->get_taxonomy()}_background_opacity", $this->term->taxonomy . '_' . $this->get_the_ID());
      }

      if (empty($color)) {
         $color = $this->default_color;
      }

      return $color;
   }

   public function get_background_image()
   {
      return get_field("{$this->get_taxonomy()}_background_image", $this->term->taxonomy . '_' . $this->get_the_ID());
   }

   public function get_background_opacity()
   {
      return get_field("{$this->get_taxonomy()}_background_opacity", $this->term->taxonomy . '_' . $this->get_the_ID());
   }

   public function get_text_color()
   {
      return get_field("{$this->get_taxonomy()}_text_color", $this->term->taxonomy . '_' . $this->get_the_ID())
         ?: '#ffffff';
   }

   public function get_show_color_status()
   {
      return get_field("{$this->get_taxonomy()}_show_color", $this->term->taxonomy . '_' . $this->get_the_ID());
   }
}
