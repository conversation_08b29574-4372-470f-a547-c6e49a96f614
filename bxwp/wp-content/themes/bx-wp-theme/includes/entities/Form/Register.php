<?php

namespace CanaisDigitais\Form;

use CanaisDigitais\Form\CD_Flamingo_CSV;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   private $registered_scripts = [
      'contact-form-7',
      'contact-form-7-html5-fallback',
      'swv',
      'google-recaptcha',
      'wpcf7-recaptcha',
      'stripe',
      'wpcf7-stripe'
   ];

   private $registered_styles = [
      'contact-form-7',
      'contact-form-7-rtl',
      'jquery-ui-smoothness',
      'wpcf7-stripe'
   ];

   public function __construct()
   {
      add_filter('wpcf7_skip_mail', '__return_true');
      add_action('wpcf7_form_tag', [$this, 'force_value_on_selectable_inputs']);
      add_action('init', [$this, 'recaptcha_config']);
      add_action('template_redirect', [$this, 'redirect_conditions']);
      add_action('wp_enqueue_scripts', [$this, 'deregister_scripts_and_styles_outside_form_page'], 30);
      add_action('wpcf7_posted_data', [$this, 'parse_form_data_to_flamingo']);
      add_filter('wpcf7_recaptcha_threshold', [$this, 'set_recaptcha_score'], 10, 1);
      add_filter('flamingo_inbound_csv_class', [$this, 'get_custom_flamingo_csv_class']);
      add_filter('flamingo_csv_field_prefix', '__return_empty_string');
   }

   public function parse_form_data_to_flamingo($posted_data)
   {
      if (!is_array($posted_data)) {
         return $posted_data;
      }

      foreach ($posted_data as $key => $value) {
         if (is_array($value)) {
            $posted_data[$key] = implode(',', array_filter($value));
         }

         if (strpos($key, 'bx_cd_other_') === 0) {
            $new_key = str_replace('bx_cd_other_', '', $key);

            if (!empty($value)) {
               if (!empty($posted_data[$new_key])) {
                  $posted_data[$new_key] = rtrim($posted_data[$new_key], ',') . ',' . $value;
               } else {
                  $posted_data[$new_key] = $value;
               }
            }

            unset($posted_data[$key]);
         }

         if (strpos($key, 'bx_cd_checkbox_required_group_') === 0) {
            $new_key = str_replace('bx_cd_checkbox_required_group_', '', $key);

            $posted_data[$new_key] = $value;

            unset($posted_data[$key]);
         }
      }

      return $posted_data;
   }

   public function recaptcha_config()
   {
      if (
         !class_exists('WPCF7') ||
         !defined('RECAPTCHA_SITE_KEY') || !defined('RECAPTCHA_SECRET_KEY') ||
         !is_environment('production')
      ) {
         return;
      }

      $current_recaptcha = \WPCF7::get_option('recaptcha');

      if (!empty($current_recaptcha)) {
         return;
      }

      \WPCF7::update_option('recaptcha', [
         RECAPTCHA_SITE_KEY => RECAPTCHA_SECRET_KEY
      ]);
   }

   public function redirect_conditions()
   {
      if (
         is_page_template('pages/template-form-newsletter.php') && (!Utils::is_lead_form_enabled() || isset($_COOKIE[Utils::$has_newsletter_signup_cookie_name]))
      ) {
         wp_safe_redirect(home_url());
         exit;
      }
   }

   public function deregister_scripts_and_styles_outside_form_page()
   {
      if (is_page_template('pages/template-form-newsletter.php')) {
         return;
      }

      foreach ($this->registered_scripts as $script) {
         wp_dequeue_script($script);
         wp_deregister_script($script);
      }

      foreach ($this->registered_styles as $style) {
         wp_dequeue_style($style);
         wp_deregister_style($style);
      }
   }

   public function force_value_on_selectable_inputs($input)
   {

      if (!in_array($input['basetype'], ['select', 'checkbox', 'radio'])) {
         return $input;
      }

      $values = [];
      $labels = [];

      foreach ($input['raw_values'] as $raw_value) {
         $raw_value_parts = explode('|', $raw_value);

         if (count($raw_value_parts) >= 2) {
            $values[] = $raw_value_parts[1];
            $labels[] = $raw_value_parts[0];
         } else {
            $values[] = $raw_value;
            $labels[] = $raw_value;
         }
      }

      $input['values'] = $values;
      $input['labels'] = $labels;

      $reversed_raw_values = array_map(function ($raw_value) {
         $raw_value_parts = explode('|', $raw_value);
         return implode('|', array_reverse($raw_value_parts));
      }, $input['raw_values']);

      $input['pipes'] = new \WPCF7_Pipes($reversed_raw_values);

      return $input;
   }

   public function set_recaptcha_score()
   {
      return (float) 0.4;
   }

   public function get_custom_flamingo_csv_class()
   {
      return CD_Flamingo_CSV::class;
   }
}