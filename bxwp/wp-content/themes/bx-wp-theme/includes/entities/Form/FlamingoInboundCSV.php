<?php

namespace CanaisDigitais\Form;

if (!defined('ABSPATH')) {
   exit;
}

if (!class_exists('\\Flamingo_CSV')) {
   return;
}

class FlamingoInboundCSV extends \Flamingo_CSV
{
   public function get_file_name()
   {
      return sprintf(
         '%s-flamingo-inbound-%s.csv',
         sanitize_key(get_bloginfo('name')),
         wp_date('Y-m-d')
      );
   }

   public function print_data()
   {
      $args = [
         'posts_per_page' => -1,
         'orderby'        => 'date',
         'order'          => 'DESC',
         's'              => $_REQUEST['s'] ?? '',
      ];

      $items = \Flamingo_Inbound_Message::find($args);
      if (empty($items)) {
         return;
      }

      $fields  = array_keys($items[0]->fields);
      $header = array_merge($fields, ['url', 'site_title', 'file', __('Date', 'flamingo')]);
      echo \flamingo_csv_row($header);

      foreach ($items as $item) {
         $row = [];
         foreach ($fields as $field) {
            $val = $item->fields[$field] ?? '';
            if (is_array($val)) {
               $val = implode(', ', array_filter($val));
            }

            if ($field === 'mobilePhone') {
               $val = preg_replace('/^\(Alerta de segurança:.*?\)\s*/', '', $val);
            }
            $row[] = $val;
         }

         $url   = $item->meta['url'] ?? '';
         $row[] = $url;
         $row[] = $item->meta['site_title'] ?? '';

         $file = '';
         if (!empty($url) && ($qs = parse_url($url, PHP_URL_QUERY))) {
            parse_str($qs, $params);
            if (!empty($params['ContentName'])) {
               $file = urldecode($params['ContentName']);
            }
         }
         $row[] = $file;

         $row[] = get_post_time('c', false, $item->id());

         echo "\r\n" . \flamingo_csv_row($row);
      }
   }
}
