<?php

namespace CanaisDigitais\Form;

if (!defined('ABSPATH')) {
   exit;
}

if (!class_exists('\\Flamingo_CSV')) {
   return;
}

class CD_Flamingo_CSV extends \Flamingo_CSV
{
   public function get_file_name()
   {
      return sprintf(
         '%s-leads-%s.csv',
         sanitize_key(get_bloginfo('name')),
         wp_date('Y-m-d')
      );
   }

   public function print_data()
   {
      $args = [
         'posts_per_page' => -1,
         'orderby'        => 'date',
         'order'          => 'DESC',
         's'              => $_REQUEST['s'] ?? '',
      ];

      if (!empty($_REQUEST['m']) && preg_match('/^\d{6}$/', $_REQUEST['m'])) {
         $dt = \DateTime::createFromFormat('Ym', $_REQUEST['m']);
         if ($dt) {
            $args['date_query'] = [[
               'year'  => (int) $dt->format('Y'),
               'month' => (int) $dt->format('n'),
            ]];
         }
      }

      $items = \Flamingo_Inbound_Message::find($args);
      if (empty($items)) {
         return;
      }

      $fields = array_keys($items[0]->fields);

      $additional_columns = ['Brand', 'Session', 'Content', 'Data'];
      $columns_to_add = array_diff($additional_columns, $fields);

      $header = array_merge($fields, $columns_to_add);
      echo \flamingo_csv_row($header);

      foreach ($items as $item) {
         $row = [];

         foreach ($fields as $field) {
            $val = $item->fields[$field] ?? '';

            if (is_array($val)) {
               $val = implode(', ', array_filter($val));
            }

            $row[] = $val;
         }

         $brand   = $item->meta['url'] ?? '';
         $session = 'SET_NWSLETR';
         $content = '';

         if (!empty($brand) && ($qs = parse_url($brand, PHP_URL_QUERY))) {
            parse_str($qs, $params);
            if (!empty($params['ContentName'])) {
               $content = urldecode($params['ContentName']);
               $session = 'SET_DGTLCNT';
            }
         }

         $additional_data = [
            'Brand'   => parse_url($brand, PHP_URL_HOST),
            'Session' => $session,
            'Content' => $content,
            'Data'    => get_post_time('c', false, $item->id())
         ];

         foreach ($additional_columns as $column) {
            if (in_array($column, $fields)) {
               $column_index = array_search($column, $fields);

               if (empty($row[$column_index])) {
                  $row[$column_index] = $additional_data[$column];
               }
            }
         }

         foreach ($columns_to_add as $column) {
            $row[] = $additional_data[$column];
         }

         echo "\r\n" . \flamingo_csv_row($row);
      }
   }
}