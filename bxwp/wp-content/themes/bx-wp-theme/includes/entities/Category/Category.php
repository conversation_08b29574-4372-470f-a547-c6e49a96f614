<?php

namespace CanaisDigitais\Category;

use CanaisDigitais\Taxonomy\Taxonomy;

if (!defined('ABSPATH')) {
   exit;
}

class Category extends Taxonomy
{
   public function __construct($category = null)
   {
      parent::__construct($category);
   }

   public function get_field($field_name)
   {
      return get_field($field_name, $this->get_taxonomy() . '_' . $this->get_the_ID());
   }

   function get_type()
   {
      $type = $this->get_field('category_type');

      if (empty($type)) {
         $type = 'normal';
      }

      return $type;
   }

   function is_default_category()
   {
      $default_category_ids = Utils::get_default_categories_ids();

      if (in_array($this->get_the_ID(), $default_category_ids)) {
         return true;
      }

      return false;
   }

   function is_parent()
   {
      $subcategories = get_term_children($this->get_the_ID(), $this->get_taxonomy());

      if (!empty($subcategories) || get_term_field('parent', $this->get_the_ID(), $this->get_taxonomy()) === 0) {
         return true;
      }

      return false;
   }

   function get_parent()
   {
      return $this->term->parent;
   }

   function get_title_format()
   {
      return $this->get_field('category_title_format');
   }

   public function get_event_location()
   {
      return $this->get_field('event_location');
   }

   public function get_event_start_date($date_format = 'd \d\e F, Y', $unformatted = false)
   {
      $event_start_date = $this->get_field('event_start_date');

      if ($unformatted === true) {
         return $event_start_date;
      }

      if (empty($event_start_date) || !strtotime($event_start_date)) {
         return;
      }

      $date = strtotime($event_start_date);

      return esc_html(date_i18n($date_format, $date));
   }

   public function get_event_end_date($date_format = 'd \d\e F, Y')
   {
      $event_end_date = $this->get_field('event_end_date');

      if (empty($event_end_date) || !strtotime($event_end_date)) {
         return;
      }

      $date = strtotime($event_end_date);

      return esc_html(date_i18n($date_format, $date));
   }

   public function get_event_url_title()
   {
      return $this->get_field('event_url_title');
   }

   public function get_event_url()
   {
      return $this->get_field('event_url');
   }

   public function is_special_page()
   {
      return $this->get_field('category_enable_special_page');
   }

   public function get_special_page_high_impact_post_id()
   {
      return $this->get_field('category_high_impact_post');
   }

   public function get_special_page_ordering_sections()
   {
      return $this->get_field('ordering_sections');
   }

   public function get_special_page_specialists_title()
   {
      return $this->get_field('category_specialists_title');
   }

   public function get_special_page_specialists()
   {
      return $this->get_field('category_specialists');
   }

   public function get_special_page_most_read()
   {
      return $this->get_field('category_most_read_articles');
   }

   public function get_special_page_most_read_period()
   {
      return $this->get_field('period_category_most_read_articles');
   }

   public function get_special_page_slider_events_posts()
   {
      return $this->get_field('category_slider_events_posts');
   }

   public function get_special_page_other_events_posts()
   {
      return $this->get_field('category_events_other_posts');
   }

   public function get_special_page_videos()
   {
      return $this->get_field('category_videos');
   }

   public function get_special_page_newsletter()
   {
      return [
         'title'            => $this->get_field('category_newsletter_title'),
         'text'             => $this->get_field('category_newsletter_text'),
         'background_type'  => $this->get_field('category_newsletter_background_type'),
         'image'            => $this->get_field('category_newsletter_image'),
         'background_color' => $this->get_field('category_newsletter_background_color'),
         'inner_background' => $this->get_field('category_newsletter_inner_background'),
      ];
   }

   public function get_special_page_featured_materials()
   {
      return $this->get_field('category_featured_materials');
   }

   public function get_special_page_featured_materials_download()
   {
      return $this->get_field('category_featured_materials_download');
   }

   public function get_special_page_featured_materials_title()
   {
      return $this->get_field('category_featured_materials_title');
   }

   public function get_sponsors()
   {
      return $this->get_field('category_sponsor_list');
   }

   public function get_special_page_categories()
   {
      return $this->get_field('category_categories');
   }

   public function get_special_page_category_videos_title()
   {
      return $this->get_field('category_videos_title');
   }

   public function get_special_page_category_videos_text()
   {
      return $this->get_field('category_videos_text');
   }

   public function get_special_page_category_events_title()
   {
      return $this->get_field('category_events_title');
   }

   public function get_special_page_category_events_description()
   {
      return $this->get_field('category_events_description');
   }
}
