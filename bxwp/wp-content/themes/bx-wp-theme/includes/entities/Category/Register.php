<?php

namespace CanaisDigitais\Category;

use Canais<PERSON><PERSON>tais\Category\Utils;
use Canais<PERSON><PERSON>tais\Author\Utils as AuthorUtils;
use Canais<PERSON><PERSON>tais\CD_Post\Utils as PostUtils;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   private $post_object_fields = ['category_high_impact_post', 'single_article', 'category_featured_materials_download'];

   private $relationship_fields = ['category_videos', 'category_featured_materials', 'category_most_read_articles', 'category_slider_events_posts', 'category_events_other_posts'];

   public function __construct()
   {
      add_action('init', [$this, 'create_default_categories']);
      add_action('acf/include_fields', [$this, 'register_category_fields']);
      add_action('pre_get_posts', [$this, 'modify_category_query']);

      add_filter('bx_cd_options_category_tabs', [$this, 'tab_show_special_page']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_high_impact_post']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_specialists']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_categories']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_events']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_videos']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_newsletter']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_featured_materials']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_sponsors']);
      add_filter('bx_cd_options_category_tabs', [$this, 'tab_ordering']);

      add_filter('acf/fields/taxonomy/query/name=category_id', [$this, 'filter_child_categories'], 10, 3);
      add_filter('acf/fields/post_object/query/name=category_single_article', [$this, 'filter_child_categories'], 10, 3);
      add_filter('acf/fields/taxonomy/query/name=author_id', [$this, 'filter_specialists_by_category'], 10, 3);

      foreach ($this->post_object_fields as $field) {
         add_filter(
            "acf/fields/post_object/query/name={$field}",
            [$this, 'filter_posts_by_category'],
            10,
            3
         );
      }

      foreach ($this->relationship_fields as $field) {
         add_filter(
            "acf/fields/relationship/query/name={$field}",
            [$this, 'filter_posts_by_category'],
            10,
            3
         );
      }
   }

   public function create_default_categories()
   {
      $default_categories = Utils::get_default_categories();

      foreach ($default_categories as $cat) {
         $category = get_term_by('slug', $cat['slug'], 'category');

         if (false === $category) {
            $new_category = wp_insert_term($cat['name'], 'category', [
               'slug' => $cat['slug']
            ]);

            if (!is_wp_error($new_category) && $cat['slug'] === $default_categories['video']['slug']) {
               update_term_meta($new_category['term_id'], 'category_type', esc_html('video'));
            }
         } else {
            if ($cat['slug'] === $default_categories['video']['slug']) {
               $category_type = get_term_meta($category->term_id, 'category_type', true);

               if ($category_type !== 'video') {
                  update_term_meta($category->term_id, 'category_type', esc_html('video'));
               }
            }
         }
      }
   }

   public function register_category_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_65428e3f2d68a',
         'fields' => array(
            array(
               'key'               => 'field_65aeb855baada',
               'label'             => 'Tipo de Categoria',
               'name'              => 'category_type',
               'aria-label'        => '',
               'type'              => 'select',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'choices' => array(
                  'normal' => 'Normal',
                  'video'  => 'Vídeo',
                  'event'  => 'Evento',
               ),
               'default_value' => 'normal',
               'return_format' => 'value',
               'multiple'      => 0,
               'allow_null'    => 0,
               'ui'            => 0,
               'ajax'          => 0,
               'placeholder'   => '',
            ),
            array(
               'key'               => 'field_65428e3fb0dd4',
               'label'             => 'Cor destacada',
               'name'              => 'category_color',
               'aria-label'        => '',
               'type'              => 'color_picker',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value'  => '#7a7a7a',
               'enable_opacity' => 0,
               'return_format'  => 'string',
            ),
            array(
               'key'               => 'field_654a44bfd5ae6ac',
               'label'             => 'Imagem de Fundo',
               'name'              => 'category_background_image',
               'aria-label'        => '',
               'type'              => 'image',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'return_format' => 'url',
               'library'       => 'all',
               'min_width'     => '',
               'min_height'    => '',
               'min_size'      => '',
               'max_width'     => '',
               'max_height'    => '',
               'max_size'      => '',
               'mime_types'    => '',
               'preview_size'  => 'thumbnail',
            ),
            array(
               'key'               => 'field_654afd44feae6ad',
               'label'             => 'Fundo com opacidade',
               'name'              => 'category_background_opacity',
               'aria-label'        => '',
               'type'              => 'color_picker',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value'  => '',
               'enable_opacity' => 1,
               'return_format'  => 'string',
            ),
            array(
               'key'               => 'field_654fda4587ae6af',
               'label'             => 'Cor do Texto',
               'name'              => 'category_text_color',
               'aria-label'        => '',
               'type'              => 'color_picker',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value'  => '#FFFFFF',
               'enable_opacity' => 0,
               'return_format'  => 'string',
            ),
            array(
               'key'               => 'field_654fda4540ae6ae',
               'label'             => 'Mostrar cor no bloco interno',
               'name'              => 'category_show_color',
               'aria-label'        => '',
               'type'              => 'true_false',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'message'       => '',
               'default_value' => 1,
               'ui_on_text'    => '',
               'ui_off_text'   => '',
               'ui'            => 1,
            ),
            array(
               'key'               => 'field_656f162fd3334070f18',
               'label'             => 'Tipo de Titulo',
               'name'              => 'category_title_format',
               'aria-label'        => '',
               'type'              => 'radio',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'choices' => array(
                  'top_border'  => 'Borda no topo',
                  'duo_tone'    => 'Tom duplo',
                  'left_border' => 'Borda na esquerda',
                  'text_only'   => 'Somente texto',
               ),
               'default_value'     => 'top_border',
               'return_format'     => 'value',
               'allow_null'        => 0,
               'other_choice'      => 0,
               'layout'            => 'vertical',
               'save_other_choice' => 0,
            ),
            array(
               'key'               => 'field_location',
               'label'             => 'Localização do Evento',
               'name'              => 'event_location',
               'type'              => 'text',
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_65aeb855baada',
                        'operator' => '==',
                        'value'    => 'event',
                     ),
                  ),
               ),
            ),
            array(
               'key'               => 'field_6503840174b06',
               'label'             => 'Data de Início do Evento',
               'name'              => 'event_start_date',
               'type'              => 'date_picker',
               'display_format'    => 'd-m-y',
               'return_format'     => 'Y-m-d',
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_65aeb855baada',
                        'operator' => '==',
                        'value'    => 'event',
                     ),
                  ),
               ),
               'first_day' => 1,
               'required'  => 0,
               'wrapper'   => array(
                  'width' => '50%',
               ),
            ),

            array(
               'key'               => 'field_6503840174b08',
               'label'             => 'Data de Término do Evento',
               'name'              => 'event_end_date',
               'type'              => 'date_picker',
               'display_format'    => 'd-m-y',
               'return_format'     => 'Y-m-d',
               'first_day'         => 1,
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_65aeb855baada',
                        'operator' => '==',
                        'value'    => 'event',
                     ),
                  ),
               ),
               'required' => 0,
               'wrapper'  => array(
                  'width' => '50%',
               ),
            ),
            array(
               'key'               => 'field_6503840174b10',
               'label'             => 'Título para Link de Evento',
               'name'              => 'event_url_title',
               'type'              => 'text',
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_65aeb855baada',
                        'operator' => '==',
                        'value'    => 'event',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '50%',
               ),
            ),
            array(
               'key'               => 'field_6503840174b11',
               'label'             => 'Link para o Evento',
               'name'              => 'event_url',
               'type'              => 'url',
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_65aeb855baada',
                        'operator' => '==',
                        'value'    => 'event',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '50%',
               ),
            ),
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'taxonomy',
                  'operator' => '==',
                  'value'    => 'category',
               ),
            ),
         ),
         'menu_order'            => 1,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));

      acf_add_local_field_group([
         'key'      => 'group_7fG9hJ2kL5mN',
         'title'    => __('Campos da página especial', 'canais_digitais'),
         'fields'   => array_merge(...apply_filters('bx_cd_options_category_tabs', [])),
         'location' => [
            [
               [
                  'param'    => 'taxonomy',
                  'operator' => '==',
                  'value'    => 'category',
               ],
            ],
         ],
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ]);
   }

   public function modify_category_query($query)
   {
      if (is_admin() || !$query->is_category() || !$query->is_main_query()) {
         return;
      }

      $current_category = $query->get_queried_object();

      if (is_null($current_category)) {
         return;
      }

      $Category = new Category($current_category);

      $query->set('post_type', ['post']);

      if ($Category->get_type() === 'video') {
         $query->set('posts_per_page', 18);
         return;
      } else {
         $query->set('posts_per_page', 20);
      }

      if ($Category->get_parent() === 0) {
         $subcategories = Utils::get_child_categories($Category->get_the_ID(), true);

         if (!empty($subcategories)) {
            $query->set('category__not_in', $subcategories);
         }
      }
   }

   public function tab_show_special_page($fields)
   {
      $fields[] = [
         [
            'key'               => 'field_Kp9vR3mX7qL2',
            'label'             => __('Habilitar página especial', 'canais_digitais'),
            'name'              => 'category_enable_special_page',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'message'           => '',
            'default_value'     => 0,
            'allow_in_bindings' => 0,
            'ui'                => 0,
            'ui_on_text'        => '',
            'ui_off_text'       => '',
         ]
      ];

      return $fields;
   }

   public function tab_high_impact_post($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_p3Qr8sT1vWxY',
            'label'             => 'Alto Impacto',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_K4nB6vC9mX2z',
            'label'             => 'Alto Impacto',
            'name'              => 'category_high_impact_post',
            'aria-label'        => '',
            'type'              => 'post_object',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'post_type'   => 'post',
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy'             => '',
            'return_format'        => 'id',
            'multiple'             => 0,
            'allow_null'           => 1,
            'bidirectional'        => 0,
            'ui'                   => 1,
            'bidirectional_target' => array(),
         ),
      ];

      return $fields;
   }

   public function tab_specialists($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_2L8qW4eR6tY9',
            'label'             => 'Especialistas e Mais Lidas',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_j9Tk3Fp5N7sD',
            'label'             => 'Título da Seção',
            'name'              => 'category_specialists_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_6gH2uJ4wK8lZ',
            'label'             => 'Especialistas',
            'name'              => 'category_specialists',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 0,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_S3d5Vf7Gh1Jk',
                  'label'             => 'Especialista',
                  'name'              => 'author_id',
                  'aria-label'        => '',
                  'type'              => 'taxonomy',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => [
                     [
                        [
                           'field'    => 'field_Kp9vR3mX7qL2',
                           'operator' => '==',
                           'value'    => '1',
                        ],
                     ],
                  ],
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'taxonomy'             => 'author_tax',
                  'add_term'             => 0,
                  'save_terms'           => 0,
                  'load_terms'           => 0,
                  'return_format'        => 'id',
                  'field_type'           => 'select',
                  'allow_null'           => 0,
                  'bidirectional'        => 1,
                  'bidirectional_target' => '',
                  'multiple'             => 0,
                  'parent_repeater'      => 'field_6gH2uJ4wK8lZ',
               ),
               array(
                  'key'               => 'field_m4Px8Qz2R9bL',
                  'label'             => 'Artigos',
                  'name'              => 'specialist_articles',
                  'aria-label'        => '',
                  'type'              => 'repeater',
                  'instructions'      => '',
                  'required'          => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'layout'        => 'table',
                  'min'           => 0,
                  'max'           => 4,
                  'collapsed'     => '',
                  'button_label'  => 'Adicionar novo',
                  'rows_per_page' => 20,
                  'sub_fields'    => array(
                     array(
                        'key'               => 'field_5tB8nY1vE6rA',
                        'label'             => 'Artigo',
                        'name'              => 'single_article',
                        'aria-label'        => '',
                        'type'              => 'post_object',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => [
                           [
                              [
                                 'field'    => 'field_Kp9vR3mX7qL2',
                                 'operator' => '==',
                                 'value'    => '1',
                              ],
                           ],
                        ],
                        'wrapper'           => array(
                           'width' => '',
                           'class' => '',
                           'id'    => '',
                        ),
                        'post_type' => array(
                           0 => 'post',
                        ),
                        'post_status' => array(
                           0 => 'publish',
                        ),
                        'taxonomy' => array(
                           0 => 'category:' . Utils::get_default_category_slug('article'),
                        ),
                        'return_format'        => 'id',
                        'multiple'             => 0,
                        'allow_null'           => 0,
                        'bidirectional'        => 0,
                        'ui'                   => 1,
                        'bidirectional_target' => array(),
                        'parent_repeater'      => 'field_m4Px8Qz2R9bL',
                     ),
                  ),
                  'parent_repeater' => 'field_6gH2uJ4wK8lZ',
               ),
            ),
         ),
         array(
            'key'               => 'field_wE5rT8yU3iO9',
            'label'             => 'Artigos Mais Lidos',
            'name'              => 'category_most_read_articles',
            'aria-label'        => '',
            'type'              => 'relationship',
            'instructions'      => 'Selecione até 5 artigos.',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . Utils::get_default_category_slug('article')
            ),
            'return_format' => 'id',
            'multiple'      => 1,
            'allow_null'    => 1,
            'ui'            => 1,
            'max'           => 4,
            'filters'       => [],
         ),
         array(
            'key'          => 'field_X9bN3cV6mH1q',
            'label'        => 'Período dos Artigos Mais Lidos',
            'name'         => 'period_category_most_read_articles',
            'aria-label'   => '',
            'type'         => 'select',
            'instructions' => __('Selecione o período para os artigos mais lidos. Essa opção é ignorada para artigos selecionados manualmente.', 'canais_digitais'),
            'required'     => 0,
            'wrapper'      => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'choices' => array(
               7  => '7 dias',
               15 => '15 dias',
            ),
            'default_value'     => 7,
            'return_format'     => 'value',
            'multiple'          => 0,
            'allow_null'        => 0,
            'allow_in_bindings' => 0,
            'ui'                => 0,
            'ajax'              => 0,
            'placeholder'       => '',
         ),
      ];

      return $fields;
   }

   public function tab_categories($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_8sK4dF7gJ2lR',
            'label'             => 'Categorias',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_T3pZ6hY9uW2x',
            'label'             => 'Categorias',
            'name'              => 'category_categories',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 20,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_q7L4mS8nB1vG',
                  'label'             => 'Categoria',
                  'name'              => 'category_id',
                  'aria-label'        => '',
                  'type'              => 'taxonomy',
                  'instructions'      => '',
                  'required'          => 1,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'taxonomy'             => 'category',
                  'add_term'             => 1,
                  'save_terms'           => 0,
                  'load_terms'           => 0,
                  'return_format'        => 'id',
                  'field_type'           => 'select',
                  'allow_null'           => 0,
                  'bidirectional'        => 0,
                  'multiple'             => 0,
                  'bidirectional_target' => array(),
                  'parent_repeater'      => 'field_T3pZ6hY9uW2x',
               ),
               array(
                  'key'          => 'field_B8vN3mC6xK7z',
                  'label'        => 'Posts',
                  'name'         => 'category_articles',
                  'aria-label'   => '',
                  'type'         => 'repeater',
                  'instructions' => '',
                  'required'     => 0,
                  'wrapper'      => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'layout'        => 'table',
                  'min'           => 0,
                  'max'           => 6,
                  'collapsed'     => '',
                  'button_label'  => 'Adicionar novo',
                  'rows_per_page' => 20,
                  'sub_fields'    => array(
                     array(
                        'key'               => 'field_6yH9tR2wQ5pS',
                        'label'             => 'Post',
                        'name'              => 'category_single_article',
                        'aria-label'        => '',
                        'type'              => 'post_object',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => array(
                           'width' => '',
                           'class' => '',
                           'id'    => '',
                        ),
                        'post_type' => array(
                           0 => 'post',
                        ),
                        'post_status' => array(
                           0 => 'publish',
                        ),
                        'taxonomy' => array(
                           0 => 'category:' . Utils::get_default_category_slug('article'),
                        ),
                        'return_format'        => 'id',
                        'multiple'             => 0,
                        'allow_null'           => 0,
                        'bidirectional'        => 0,
                        'ui'                   => 1,
                        'bidirectional_target' => array(),
                        'parent_repeater'      => 'field_B8vN3mC6xK7z',
                     ),
                  ),
                  'parent_repeater' => 'field_T3pZ6hY9uW2x',
               ),
            ),
         ),
      ];

      return $fields;
   }

   public function tab_events($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_8Lk3mFg7qN9R',
            'label'             => 'Eventos',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_p5Xb2T8yV4sD',
            'label'             => 'Nome da Seção',
            'name'              => 'category_events_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_9Jh4nW3kM7vP',
            'label'             => 'Descrição da Seção',
            'name'              => 'category_events_description',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_r6T2zQ8dY5mL',
            'label'             => 'Eventos do Slider',
            'name'              => 'category_slider_events_posts',
            'aria-label'        => '',
            'type'              => 'relationship',
            'instructions'      => '',
            'required'          => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'max'               => 5,
            'filters'           => [],
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . Utils::get_default_category_slug('event'),
            ),
            'return_format' => 'id',
            'multiple'      => 1,
            'allow_null'    => 0,
            'ui'            => 1,
         ),
         array(
            'key'               => 'field_W4sN8pR1qK3t',
            'label'             => 'Outros Eventos',
            'name'              => 'category_events_other_posts',
            'aria-label'        => '',
            'type'              => 'relationship',
            'instructions'      => '',
            'required'          => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'max'               => 4,
            'filters'           => [],
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . Utils::get_default_category_slug('event'),
            ),
            'return_format' => 'id',
            'multiple'      => 1,
            'allow_null'    => 0,
            'ui'            => 1,
         ),
      ];

      return $fields;
   }

   public function tab_videos($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_v7Kp3Rq9Nx2L',
            'label'             => 'Vídeos',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_5Tj8YmW4sB6h',
            'label'             => 'Vídeos',
            'name'              => 'category_videos',
            'aria-label'        => '',
            'type'              => 'relationship',
            'instructions'      => 'Selecione até 5 vídeos.',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . Utils::get_default_category_slug('video'),
            ),
            'return_format' => 'id',
            'multiple'      => 1,
            'allow_null'    => 1,
            'ui'            => 1,
            'max'           => 5,
            'filters'       => [],
         ),
         array(
            'key'               => 'field_Q3n9Vk7dF2rG',
            'label'             => 'Título',
            'name'              => 'category_videos_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_8Lm4Hj6Pw9Xz',
            'label'             => 'Texto',
            'name'              => 'category_videos_text',
            'aria-label'        => '',
            'type'              => 'textarea',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'rows'          => '',
            'placeholder'   => '',
            'new_lines'     => '',
         ),
      ];

      return $fields;
   }

   public function tab_newsletter($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_k9mLp4Rq2Xv8',
            'label'             => 'Newsletter',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_T5nY8hW3sJ7',
            'label'             => 'Titulo',
            'name'              => 'category_newsletter_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '50',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_7FjN2dK9rV4',
            'label'             => 'Texto',
            'name'              => 'category_newsletter_text',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '50',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_Q3vL6tP9mB1',
            'label'             => 'Tipo de Fundo',
            'name'              => 'category_newsletter_background_type',
            'aria-label'        => '',
            'type'              => 'radio',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'choices' => array(
               'color' => 'Cor',
               'image' => 'Imagem',
            ),
            'default_value'     => '',
            'return_format'     => 'value',
            'allow_null'        => 0,
            'other_choice'      => 0,
            'layout'            => 'vertical',
            'save_other_choice' => 0,
         ),
         array(
            'key'               => 'field_8Hx4Gk7nZ2',
            'label'             => 'Cor de Fundo',
            'name'              => 'category_newsletter_background_color',
            'aria-label'        => '',
            'type'              => 'color_picker',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Q3vL6tP9mB1',
                     'operator' => '==',
                     'value'    => 'color',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'  => '#1CBF69',
            'enable_opacity' => 0,
            'return_format'  => 'string',
         ),
         array(
            'key'               => 'field_b5Rf9Jd3Mp',
            'label'             => 'Imagem de Fundo',
            'name'              => 'category_newsletter_image',
            'aria-label'        => '',
            'type'              => 'image',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Q3vL6tP9mB1',
                     'operator' => '==',
                     'value'    => 'image',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'return_format' => 'url',
            'library'       => 'all',
            'min_width'     => '',
            'min_height'    => '',
            'min_size'      => '',
            'max_width'     => '',
            'max_height'    => '',
            'max_size'      => '',
            'mime_types'    => '',
            'preview_size'  => 'medium',
         ),
         array(
            'key'               => 'field_W2sK8yL4qV',
            'label'             => 'Fundo Interno',
            'name'              => 'category_newsletter_inner_background',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'message'       => '',
            'default_value' => 0,
            'ui'            => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
         ),
      ];

      return $fields;
   }

   public function tab_featured_materials($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_7xJk4pQ9vR2m',
            'label' => 'Destaque',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_L5nT8yW3hB6',
            'label' => 'Título',
            'name' => 'category_featured_materials_title',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => __('Mais destaques', 'canais-digitais'),
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
         array(
            'key'               => 'field_9dFg2rK7mN4',
            'label'             => 'Destaque',
            'name'              => 'category_featured_materials',
            'type'              => 'relationship',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'post_type'     => array('post'),
            'post_status'   => array('publish'),
            'filters'       => array(),
            'elements'      => '',
            'return_format' => 'id',
            'max'           => 7,
         ),
         array(
            'key' => 'field_q3Pj8sV5tX1',
            'label' => 'Download',
            'name' => 'category_featured_materials_download',
            'aria-label' => '',
            'type' => 'post_object',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . Utils::get_default_category_slug('download-material'),
            ),
            'return_format' => 'id',
            'multiple' => 0,
            'allow_null' => 1,
            'ui' => 1,
         ),
      ];

      return $fields;
   }

   public function tab_sponsors($fields)
   {
      $fields[] = [
         [
            'key'               => 'field_Q7tL3jY8wN1',
            'label'             => __('Patrocinadores', 'canal-energia'),
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'placement' => 'top',
            'endpoint'  => 0,
            'selected'  => 0,
         ],
         [
            'key'               => 'field_2vK6nM9pR4',
            'label'             => __('Lista de patrocinadores', 'canal-energia'),
            'name'              => 'category_sponsor_list',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 5,
            'collapsed'     => '',
            'button_label'  => 'Adicionar linha',
            'rows_per_page' => 20,
            'sub_fields'    => [
               [
                  'key'               => 'field_W5sJ8dF3hQ7',
                  'label'             => __('Imagem', 'canal-energia'),
                  'name'              => 'image',
                  'aria-label'        => '',
                  'type'              => 'image',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => 0,
                  'wrapper'           => [
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ],
                  'return_format'     => 'url',
                  'library'           => 'all',
                  'min_width'         => '',
                  'min_height'        => '',
                  'min_size'          => '',
                  'max_width'         => '',
                  'max_height'        => '',
                  'max_size'          => '',
                  'mime_types'        => '',
                  'allow_in_bindings' => 0,
                  'preview_size'      => 'medium',
                  'parent_repeater'   => 'field_2vK6nM9pR4',
               ],
            ],
         ],
      ];

      return $fields;
   }

   public function tab_ordering($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_w8Yk3qP7tN2m',
            'label'             => 'Ordenação',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key' => 'field_5rT9vL4sJ6xH',
            'label' => 'Atenção!',
            'name' => 'ordering_message',
            'aria-label' => '',
            'type' => 'message',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'message' => 'As categorias selecionadas seguirão a ordem em que foram escolhidas na aba \'Categorias\'.',
            'new_lines' => 'wpautop',
            'esc_html' => 0,
         ),
         array(
            'key'               => 'field_J4mK8bV3nZ7p',
            'label'             => 'Seções',
            'name'              => 'ordering_sections',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_Kp9vR3mX7qL2',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 25,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_9fX5hR2dQ8y',
                  'label'             => 'Seção',
                  'name'              => 'single_section',
                  'aria-label'        => '',
                  'type'              => 'select',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => [
                     [
                        [
                           'field'    => 'field_Kp9vR3mX7qL2',
                           'operator' => '==',
                           'value'    => '1',
                        ],
                     ],
                  ],
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'choices' => array(
                     'category'           => 'Categoria',
                     'grid-specialists'   => 'Especialistas',
                     'grid-events'        => 'Eventos',
                     'newsletter'         => 'Newsletter',
                     'videos'             => 'Vídeos',
                     'featured-materials' => 'Destaques',
                     'sponsors'           => 'Patrocinadores',
                  ),
                  'default_value'   => false,
                  'return_format'   => 'value',
                  'multiple'        => 0,
                  'allow_null'      => 0,
                  'ui'              => 0,
                  'ajax'            => 0,
                  'placeholder'     => '',
                  'parent_repeater' => 'field_w8Yk3qP7tN2m',
               ),
            ),
         )
      ];

      return $fields;
   }

   private function get_term_id($post_id)
   {
      if (strpos($post_id, 'term_') !== 0 && strpos($post_id, 'category_') !== 0) {
         return;
      }

      $term_ID = (int) str_replace(['term_', 'category_'], '', $post_id);

      if (empty($term_ID)) {
         return;
      }

      return $term_ID;
   }

   public function filter_child_categories($args, $field, $post_id)
   {
      $term_ID = $this->get_term_id($post_id);

      if (empty($term_ID)) {
         return $args;
      }

      $child_categories = get_terms([
         'taxonomy'   => 'category',
         'child_of'   => $term_ID,
         'hide_empty' => true,
         'fields'     => 'ids'
      ]);

      $has_children = !empty($child_categories) && !is_wp_error($child_categories);

      if ($field['name'] === 'category_single_article') {
         $args['tax_query'] = [
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => $has_children ? $child_categories : [$term_ID],
            ]
         ];

         return $args;
      }

      $args['include'] = $has_children ? $child_categories : [$term_ID];

      return $args;
   }

   public function filter_posts_by_category($args, $field, $post_id)
   {
      $term_ID = $this->get_term_id($post_id);

      if (empty($term_ID)) {
         return $args;
      }

      $args['orderby'] = 'date';
      $args['order'] = 'DESC';

      if ($field['name'] === 'single_article') {
         $args['tax_query'] = [
            'relation' => 'AND',
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$term_ID],
            ],
            [
               'taxonomy' => 'author_tax',
               'operator' => 'EXISTS'
            ]
         ];

         return $args;
      }

      if ($field['name'] === 'single_event_post') {
         $event_term_id = get_term_by('slug', Utils::get_default_category_slug('event'), 'category')->term_id;

         $args['tax_query'] = [
            'relation' => 'AND',
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$event_term_id],
            ],
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$term_ID],
            ]
         ];

         return $args;
      }

      if ($field['name'] === 'category_videos') {
         $video_term_id = get_term_by('slug', Utils::get_default_category_slug('video'), 'category')->term_id;

         $args['tax_query'] = [
            'relation' => 'AND',
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$video_term_id],
            ],
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$term_ID],
            ]
         ];

         return $args;
      }

      if ($field['name'] === 'category_slider_events_posts' || $field['name'] === 'category_events_other_posts') {
         $args['tax_query'] = [
            'relation' => 'AND',
            [
               'taxonomy' => 'category',
               'field'    => 'slug',
               'terms'    => [Utils::get_default_category_slug('event')],
            ],
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$term_ID],
            ]
         ];

         return $args;
      }

      if ($field['name'] === 'category_featured_materials_download') {
         $args['tax_query'] = [
            'relation' => 'AND',
            [
               'taxonomy' => 'category',
               'field'    => 'slug',
               'terms'    => [Utils::get_default_category_slug('download-material')],
            ],
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$term_ID],
            ]
         ];

         return $args;
      }

      $args['tax_query'] = [
         [
            'taxonomy' => 'category',
            'field'    => 'term_id',
            'terms'    => $term_ID,
         ]
      ];

      return $args;
   }

   public function filter_specialists_by_category($args, $field, $post_id)
   {
      $term_ID = $this->get_term_id($post_id);

      if (empty($term_ID)) {
         return $args;
      }

      $specialists = AuthorUtils::get_all_authors('ids', 'specialist');

      $args['exclude'] = [];

      foreach ($specialists as $specialist) {
         $specialist_has_posts = PostUtils::get_latest_articles_by_author_term_id($specialist, 1, [], 0, 1, $term_ID);

         if (!empty($specialist_has_posts)) {
            continue;
         }

         $args['exclude'][] = $specialist;
      }

      return $args;
   }
}
