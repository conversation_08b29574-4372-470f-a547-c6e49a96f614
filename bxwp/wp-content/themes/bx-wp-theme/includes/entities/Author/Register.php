<?php

namespace CanaisDigitais\Author;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      add_action('init', [$this, 'reset_author_archive_url'], 5);
      add_action('init', [$this, 'register_taxonomy'], 10);
      add_action('acf/include_fields', [$this, 'register_fields']);
      add_action('acf/include_fields', [$this, 'register_template_authors_fields']);
      add_action('pre_get_posts', [$this, 'modify_journalist_query']);
      add_action('pre_get_posts', [$this, 'modify_wp_author_page_query']);
   }

   function reset_author_archive_url()
   {
      global $wp_rewrite;
      $author_slug             = 'user';
      $wp_rewrite->author_base = $author_slug;
   }

   public function register_taxonomy()
   {
      register_taxonomy('author_tax', [
         'post',
      ], [
         'labels' =>
         [
            'name'          => esc_attr__('Autores', 'canais_digitais'),
            'singular_name' => esc_attr__('Autor', 'canais_digitais'),
            'menu_name'     => esc_attr__('Autores', 'canais_digitais'),
            'search_items'  => esc_attr__('Procurar autor', 'canais_digitais'),
            'all_items'     => esc_attr__('Todos autores', 'canais_digitais'),
            'edit_item'     => esc_attr__('Editar autor', 'canais_digitais'),
            'update_item'   => esc_attr__('Atualizar autor', 'canais_digitais'),
            'add_new_item'  => esc_attr__('Adicionar novo autor', 'canais_digitais'),
            'new_item_name' => esc_attr__('Novo autor', 'canais_digitais'),
         ],
         'description'        => esc_attr__('Autores que contribuem com matérias.', 'canais_digitais'),
         'hierarchical'       => false,
         'public'             => true,
         'show_ui'            => true,
         'show_in_nav_menus'  => true,
         'show_tagcloud'      => true,
         'show_in_quick_edit' => true,
         'show_admin_column'  => true,
         'show_in_rest'       => true,
         'rewrite'            => [
            'slug' => 'author',
         ],
      ]);
   }

   public function register_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_6500d1c12ebe0',
         'title'  => 'Autor',
         'fields' => array(
            array(
               'key'               => 'field_6542585042264',
               'label'             => 'Tipo',
               'name'              => 'author_type',
               'aria-label'        => '',
               'type'              => 'select',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'choices' => array(
                  'journalist' => 'Jornalista',
                  'specialist' => 'Especialista',
                  'company'    => 'Empresa',
               ),
               'default_value' => 'journalist',
               'return_format' => 'value',
               'multiple'      => 0,
               'allow_null'    => 0,
               'ui'            => 0,
               'ajax'          => 0,
               'placeholder'   => '',
            ),
            array(
               'key'               => 'field_6500d1c128885',
               'label'             => 'Foto',
               'name'              => 'author_avatar',
               'aria-label'        => '',
               'type'              => 'image',
               'instructions'      => '',
               'required'          => 1,
               'conditional_logic' => array(
                  array(
                     array(
                        'field' => 'field_6542585042264',
                        'operator' => '==',
                        'value' => 'specialist',
                     ),
                  ),
               ),
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'return_format' => 'id',
               'library'       => 'all',
               'min_width'     => '',
               'min_height'    => '',
               'min_size'      => '',
               'max_width'     => '',
               'max_height'    => '',
               'max_size'      => '',
               'mime_types'    => '',
               'preview_size'  => 'medium',
            ),
            array(
               'key' => 'field_6579dbefdc158',
               'label' => 'Mostrar Artigos Mais Recentes',
               'name' => 'tax_author_show_most_recent_articles',
               'aria-label' => '',
               'type' => 'true_false',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'message' => '',
               'default_value' => 0,
               'ui' => 0,
               'ui_on_text' => '',
               'ui_off_text' => '',
            ),
            array(
               'key' => 'field_6579db7fdc156',
               'label' => 'Artigos',
               'name' => 'tax_author_featured_articles',
               'aria-label' => '',
               'type' => 'repeater',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => array(
                  array(
                     array(
                        'field' => 'field_6579dbefdc158',
                        'operator' => '!=',
                        'value' => '1',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'layout' => 'table',
               'pagination' => 0,
               'min' => 0,
               'max' => 3,
               'collapsed' => '',
               'button_label' => 'Adicionar artigo',
               'rows_per_page' => 20,
               'sub_fields' => array(
                  array(
                     'key' => 'field_6579dbb7dc157',
                     'label' => 'Artigo',
                     'name' => 'tax_author_featured_articles_single_article',
                     'aria-label' => '',
                     'type' => 'post_object',
                     'instructions' => '',
                     'required' => 1,
                     'conditional_logic' => 0,
                     'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                     ),
                     'post_type' => array(
                        0 => 'post',
                     ),
                     'post_status' => array(
                        0 => 'publish',
                     ),
                     'taxonomy' => '',
                     'return_format' => 'id',
                     'multiple' => 0,
                     'allow_null' => 0,
                     'bidirectional' => 0,
                     'ui' => 1,
                     'bidirectional_target' => array(),
                     'parent_repeater' => 'field_6579db7fdc156',
                  ),
               ),
            ),
            array(
               'key' => 'field_65b7b50fee495',
               'label' => 'Usuário WordPress',
               'name' => 'attached_wp_user_id',
               'aria-label' => '',
               'type' => 'user',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'role' => '',
               'return_format' => 'id',
               'multiple' => 0,
               'allow_null' => 0,
               'bidirectional' => 0,
               'bidirectional_target' => array(),
            ),
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'taxonomy',
                  'operator' => '==',
                  'value'    => 'author_tax',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function register_template_authors_fields()
   {

      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_657740696801a',
         'title'  => 'Campos de Especialistas',
         'fields' => array(
            array(
               'key'               => 'field_6577406920094',
               'label'             => 'Descrição',
               'name'              => 'template_authors_description',
               'aria-label'        => '',
               'type'              => 'text',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value' => '',
               'maxlength'     => '',
               'placeholder'   => '',
               'prepend'       => '',
               'append'        => '',
            ),
            array(
               'key'               => 'field_6577409b20095',
               'label'             => 'Autores',
               'name'              => 'template_authors_authors',
               'aria-label'        => '',
               'type'              => 'repeater',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'layout'        => 'table',
               'pagination'    => 0,
               'min'           => 0,
               'max'           => 0,
               'collapsed'     => '',
               'button_label'  => 'Adicionar autor',
               'rows_per_page' => 20,
               'sub_fields'    => array(
                  array(
                     'key'               => 'field_657740b720096',
                     'label'             => 'Autor',
                     'name'              => 'template_authors_single_author',
                     'aria-label'        => '',
                     'type'              => 'taxonomy',
                     'instructions'      => '',
                     'required'          => 1,
                     'conditional_logic' => 0,
                     'wrapper'           => array(
                        'width' => '',
                        'class' => '',
                        'id'    => '',
                     ),
                     'taxonomy'             => 'author_tax',
                     'add_term'             => 0,
                     'save_terms'           => 0,
                     'load_terms'           => 0,
                     'return_format'        => 'id',
                     'field_type'           => 'select',
                     'allow_null'           => 0,
                     'bidirectional'        => 0,
                     'multiple'             => 0,
                     'bidirectional_target' => array(),
                     'parent_repeater'      => 'field_6577409b20095',
                  ),
               ),
            ),
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'page_template',
                  'operator' => '==',
                  'value'    => 'pages/template-authors.php',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function modify_journalist_query($query)
   {
      if (is_admin() || !$query->is_main_query()) {
         return;
      }

      if (!is_tax('author_tax')) {
         return;
      }

      $Author = new Author(get_queried_object()->term_id);

      if ($Author->get_author_type() !== 'journalist') {
         return;
      }

      $query->set('posts_per_page', 12);
   }

   public function modify_wp_author_page_query($query)
   {
      if (!is_admin() && $query->is_author() && $query->is_main_query()) {
         $query->set('posts_per_page', 12);
      }
   }
}
