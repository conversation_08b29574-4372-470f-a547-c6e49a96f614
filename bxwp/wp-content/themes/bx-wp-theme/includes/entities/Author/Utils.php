<?php

namespace CanaisDigitais\Author;

use Canais<PERSON><PERSON>tais\CD_Post\Utils as PostUtils;
use <PERSON>ais<PERSON><PERSON>tais\Utils as GenericUtils;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   static $homepage_option_page      = 'homepage-options';
   static $general_options_page      = 'general-options';

   static $default_specialist_naming = [
      'singular' => 'Especialista',
      'plural'   => 'Especialistas',
   ];

   static $specialist_category_slugs = ['especialistas', 'colunistas'];

   public static function get_all_authors($fields = 'ids', $types = [], $not_in = [], $is_count = false)
   {
      $args = [
         'taxonomy' => 'author_tax',
         'fields'   => $fields,
         'hide_empty' => false,
      ];

      if (!empty($types)) {
         $args['meta_key']   = 'author_type';
         $args['meta_query'] = [
            'key'     => 'author_type',
            'value'   => $types,
            'compare' => 'IN',
         ];
      }

      if (!empty($not_in)) {
         $args['exclude'] = $not_in;
      }

      if ($is_count === true) {
         $count_terms = (int) wp_count_terms($args);
         return $count_terms;
      }

      $terms = get_terms($args);

      return $terms;
   }

   public static function count_all_authors($fields = 'ids', $types = [], $not_in = [])
   {
      return self::get_all_authors($fields, $types, $not_in, true);
   }

   static function get_homepage_specialists_section_title()
   {
      $section_title = get_field('homepage_specialists_title', self::$homepage_option_page);
      return $section_title;
   }

   public static function get_homepage_specialists_with_articles()
   {
      global $excluded_posts_ID;

      $specialists = (array) get_field('homepage_specialists', self::$homepage_option_page);

      $is_show_all_specialists = (bool) get_field('homepage_show_all_specialists', self::$homepage_option_page);

      if ($is_show_all_specialists) {
         $specialists = self::get_all_authors('ids', 'specialist');
      }

      if (empty($specialists)) {
         return [];
      }

      $filtered_specialists = [];

      foreach ($specialists as $specialist) {
         $specialist_id    = $specialist['author_id'] ?? $specialist;
         $show_latest_ones = $specialist['show_latest_ones'] ?? true;
         $chosen_articles  = !$show_latest_ones && !empty($specialist['specialist_articles'])
            ? array_diff(array_column($specialist['specialist_articles'], 'single_article'), $excluded_posts_ID)
            : [];

         if (!$show_latest_ones) {
            $excluded_posts_ID = array_merge($excluded_posts_ID, $chosen_articles);
         }

         $latest_articles = array_column(
            PostUtils::get_latest_articles_by_author_term_id($specialist_id, 4, $excluded_posts_ID),
            'ID'
         );

         $featured_article = null;
         $articles = [];

         if (!empty($chosen_articles) && !$show_latest_ones) {
            $featured_article = reset($chosen_articles);
            $articles = array_merge(array_slice($chosen_articles, 1), array_slice($latest_articles, 0, 4 - count($chosen_articles)));
         } else {
            $featured_article = reset($latest_articles);
            $articles = array_slice($latest_articles, 1, 4);
         }

         if (empty($articles) && empty($featured_article)) {
            continue;
         }

         $excluded_posts_ID = array_merge($excluded_posts_ID, $articles, [$featured_article]);

         $filtered_specialists[] = [
            'author_id'        => $specialist_id,
            'articles'         => $articles,
            'featured_article' => $featured_article
         ];
      }

      return $filtered_specialists;
   }

   static public function get_custom_specialist_naming(string $type = 'singular')
   {

      $field_prefix = 'general_options_name_for_author_specialist';

      $chosen_custom_naming  = get_field($field_prefix, self::$general_options_page);

      if ($type === 'singular') {

         $default_naming = self::$default_specialist_naming['singular'];
         $field_suffix   = $type;
      } elseif ($type === 'plural') {

         $default_naming = self::$default_specialist_naming['plural'];
         $field_suffix   = $type;
      } else {

         return null;
      }

      $custom_naming = isset($chosen_custom_naming[$field_prefix . '_' . $field_suffix]) ? $chosen_custom_naming[$field_prefix . '_' . $field_suffix] : '';

      if (empty($custom_naming)) {

         return $default_naming;
      }

      return $custom_naming;
   }

   static public function get_template_authors_description()
   {
      $description = get_field('template_authors_description');
      return $description;
   }

   static public function get_template_authors_chosen_specialists()
   {
      $authors = [];
      $chosen_authors  = get_field('template_authors_authors');

      if (!empty($chosen_authors)) {
         $authors = array_column($chosen_authors, 'template_authors_single_author');
      }

      $get_remaining_authors = self::get_all_authors('ids', 'specialist', $authors);
      $count_all_authors     = self::count_all_authors('ids', 'specialist');

      $authors = GenericUtils::fill_with_complementary_items($authors, $get_remaining_authors, $count_all_authors);

      return $authors;
   }

   static public function get_specialists_archive_url()
   {
      if (empty(self::$specialist_category_slugs)) {
         return false;
      }

      foreach (self::$specialist_category_slugs as $slug) {
         $term = get_term_by('slug', $slug, 'category');
         if ($term) {
            return get_term_link($term);
         }
      }

      return false;
   }
}
