<?php

/**
 * Name: Author
 * Type: Taxonomy
 * Custom Fields: avatar de autor, artigos destacados do autor, tipo do autor, mostrar artigos mais recentes do autor
 * Description: Taxonomia que associa artigos a pessoas no front.
 */

namespace CanaisDigitais\Author;

use CanaisDigitais\Utils as GenericUtils;
use CanaisDigitais\CD_Post\Utils as PostUtils;

if (!defined('ABSPATH')) {
   exit;
}

class Author
{
   public $term = null;
   public $term_ID = 0;

   public function __construct($term = null)
   {
      if (is_null($term)) {
         $term = \get_queried_object();
      } else {
         $term = \get_term($term);
      }

      $this->term    = $term;

      $this->term_ID = $term->term_id;
   }

   public function get_name()
   {
      return $this->term->name;
   }

   public function get_link()
   {
      return \get_term_link($this->term);
   }

   public function get_description()
   {
      $description = get_term_field('description', $this->term->term_id);
      $description = strip_tags($description, '<a>');

      return $description;
   }

   public function get_avatar($size = 'thumbnail', array $attrs = [])
   {
      $thumbnail_ID = \get_term_meta($this->term_ID, 'author_avatar', true);

      if (empty($thumbnail_ID)) {
         return;
      }

      return \wp_get_attachment_image($thumbnail_ID, $size, false, $attrs);
   }

   public function display($strong = true)
   {
      $name = $this->get_name();
      $link = $this->get_link();

      if ($strong) {
         return <<<AUTHOR
            <a href="$link"><strong>$name</strong></a>
AUTHOR;
      }

      return $name;
   }

   public function get_author_posts($number = 6)
   {
      return \get_posts([
         'posts_per_page' => $number,
         'tax_query'      => [
            [
               'taxonomy' => 'author_tax',
               'field'    => 'term_id',
               'terms'    => $this->term_ID,
            ]
         ]
      ]);
   }

   public function get_author_type()
   {
      $author_type = \get_term_meta($this->term_ID, 'author_type', true);

      return $author_type;
   }

   public function get_featured_articles()
   {
      $taxonomy_key         = $this->term->taxonomy . '_' . $this->term_ID;
      $featured_articles    = get_field('tax_author_featured_articles', $taxonomy_key) ?: [];
      $show_latest_articles = (bool) get_field('tax_author_show_most_recent_articles', $taxonomy_key);

      if (empty($featured_articles) || $show_latest_articles === true) {
         $featured_articles = [];
      } else {
         $featured_articles = array_column($featured_articles, 'tax_author_featured_articles_single_article');
      }

      $latest_articles = PostUtils::get_latest_articles_by_author_term_id($this->term_ID, 3);
      $latest_articles = $latest_articles ? array_column($latest_articles, 'ID') : [];

      return GenericUtils::fill_with_complementary_items($featured_articles, $latest_articles, 3);
   }
}
