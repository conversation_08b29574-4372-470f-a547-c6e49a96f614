<?php

namespace CanaisDigitais\Story;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      add_action('pre_get_posts', [$this, 'modify_web_story_archive_query']);
   }

   public function modify_web_story_archive_query($query)
   {
      if (!is_admin() && $query->is_post_type_archive('web-story') && $query->is_main_query()) {
         $query->set('posts_per_page', 12);
      }
   }
}
