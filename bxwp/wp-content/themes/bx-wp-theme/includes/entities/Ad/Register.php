<?php

namespace CanaisDigitais\Ad;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      add_action('init', [$this, 'register_ad_option_page']);
      add_action('wp_head', [$this, 'define_slots']);
      add_action('bx_cd_admanager_define_slots', [$this, 'dynamic_define_slots']);
   }

   public function register_ad_option_page()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_options_page([
         'page_title'  => 'Anúncios',
         'menu_slug'   => 'ad-options',
         'redirect'    => false,
         'capability'  => 'bx_manage_theme_options',
         'position'    => 5,
         'parent_slug' => '',
         'icon_url'    => 'dashicons-admin-generic',
         'redirect'    => true,
         'post_id'     => 'ad-options',
         'autoload'    => false,
      ]);

      acf_add_local_field_group(array(
         'key'    => 'group_64d3e788b1ddd',
         'title'  => 'Anúncios',
         'fields' => array(
            array(
               'key'               => 'field_654bd0c93715f',
               'label'             => 'ID de conta no Ad Manager',
               'name'              => 'admanager_account_id',
               'aria-label'        => '',
               'type'              => 'text',
               'instructions'      => '',
               'required'          => 1,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value' => '',
               'maxlength'     => '',
               'placeholder'   => '',
               'prepend'       => '',
               'append'        => '',
            ),

            array(
               'key'               => 'field_454bd0793915d',
               'label'             => 'Ad Unit (site ID)',
               'name'              => 'admanager_adunit',
               'aria-label'        => '',
               'type'              => 'text',
               'instructions'      => 'Exemplo: avozdaindustria',
               'required'          => 1,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'default_value' => '',
               'maxlength'     => '',
               'placeholder'   => '',
               'prepend'       => '',
               'append'        => '',
            ),
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'ad-options',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'acf_after_title',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => array(
            0  => 'permalink',
            1  => 'the_content',
            2  => 'excerpt',
            3  => 'discussion',
            4  => 'comments',
            5  => 'revisions',
            6  => 'slug',
            7  => 'author',
            8  => 'format',
            9  => 'page_attributes',
            10 => 'featured_image',
            11 => 'categories',
            12 => 'tags',
            13 => 'send-trackbacks',
         ),
         'active'       => true,
         'description'  => '',
         'show_in_rest' => 1,
      ));
   }

   public function define_slots()
   {
      $account_id = Utils::get_admanager_account_id();
      $site_id    = Utils::get_admanager_site_id();

      if (empty($account_id) || empty($site_id)) {
         return;
      }

?>
      <script type='text/javascript'>
         window.googletag = window.googletag || {
            cmd: []
         };
         googletag.cmd.push(function() {
            <?php

            do_action('bx_cd_admanager_define_slots');

            ?>
            googletag.pubads().enableSingleRequest();
            googletag.enableServices();
         });
      </script>
<?php

   }

   public function dynamic_define_slots()
   {
      $all_ads = Utils::get_all_ads();

      foreach ($all_ads as $key => $ad) {

         new Define_Slot($key, $ad);
      }
   }
}
