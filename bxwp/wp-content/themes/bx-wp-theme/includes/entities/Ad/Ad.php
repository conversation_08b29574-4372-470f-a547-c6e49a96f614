<?php

/**
 * Name: Ad
 * Custom Fields: ID da conta do Google AdManager
 * Description: An<PERSON>cios
 */

namespace CanaisDigitais\Ad;

use CanaisDigitais\Ad\Utils;

if (!defined('ABSPATH')) {
   exit;
}

class Ad
{
   protected $ad_position;
   protected $ad_list;
   protected $wrapper_classes;

   public function __construct(string $ad_position, string $wrapper_classes = '')
   {
      $this->ad_position = $ad_position;
      $this->ad_list = Utils::get_all_ads();
      $this->wrapper_classes = $wrapper_classes;

      $this->get_ad_by_position();
   }

   private function print_ad(array $ad = [])
   {

      if (empty($ad)) {
         return;
      }

      $account_id  = Utils::get_admanager_account_id();
      $site_id     = Utils::get_admanager_site_id();
      $ad_position = $this->ad_position;
      $ad_name     = "{$account_id}_{$site_id}_{$ad_position}";
      $ad_id       = Utils::generate_ad_id($ad_name);

      if (is_environment(['staging', 'production'])) {

         get_component('ad-render', [
            'ad_info' => [
               'ID'   => $ad_id,
               'name' => $ad_name,
            ],
            'wrapper_classes' => $this->wrapper_classes
         ]);

      } else {

         get_component('ad-placeholder', [
            'ad_name'         => $this->ad_position,
            'ad_sizes'        => $this->get_ad_sizes(),
            'ad_info'         => $this->ad_list[$this->ad_position],
            'wrapper_classes' => $this->wrapper_classes
         ]);
      }
   }

   private function get_breakpoint_sizes($breakpoint)
   {
      if(!in_array($breakpoint, Utils::$ad_breakpoint_alias)) {
         return;
      }

      if (isset($this->ad_list[$this->ad_position]['breakpoints'])) {
         $breakpoints = $this->ad_list[$this->ad_position]['breakpoints'];
      } else {

         $breakpoints = null;
      }

      if (isset($this->ad_list[$this->ad_position]['size'])) {
         $canonical_size = $this->ad_list[$this->ad_position]['size'];
      }

      if (isset($breakpoints[$breakpoint])) {
         if (isset($breakpoints[$breakpoint]['ad_sizes'][0]) && is_array($breakpoints[$breakpoint]['ad_sizes'][0])) {
            return $breakpoints[$breakpoint]['ad_sizes'][0];
         } else {
            return $breakpoints[$breakpoint]['ad_sizes'];
         }
      } elseif (isset($canonical_size)) {

         if ($canonical_size === 'fluid') {
            return Utils::$fluid_desktop_placeholder_size;
         }
         
         if (count($canonical_size) === 2) {
            return $canonical_size;
         } else {
            return $canonical_size[0];
         }
      } else {

         return Utils::$fluid_desktop_placeholder_size;
      }
   }

   private function get_ad_sizes()
   {
      $sizes = [
         'desktop' => $this->get_breakpoint_sizes('desktop'),
      ];

      return $sizes;
   }

   private function get_ad_by_position()
   {

      $ad_list = $this->ad_list;

      if (empty($ad_list)) {
         return;
      }

      if (!empty($ad_list[$this->ad_position])) {
         $this->print_ad($ad_list[$this->ad_position]);
      }
   }
}
