<?php

namespace CanaisDigitais\Ad;

if (!defined('ABSPATH')) exit;

class Define_Slot
{
   public function __construct(string $ad_position, array $ad)
   {
      if (empty($ad)) {
         return;
      }

      $context = Utils::get_current_context();

      if ($context === null) {
         return;
      }

      if (!isset($context['target'], $ad['page']) && !in_array($context['target'], $ad['page'])) {
         return;
      }

      $context_for_path = '/' . $context['ad_path'];

      $devices = Utils::$ad_breakpoint_alias;

      $account_id = Utils::get_admanager_account_id();
      $site_id    = Utils::get_admanager_site_id();
      $sizes      = $ad['size'];
      $ad_path    = "/{$account_id}/{$site_id}" . $context_for_path;
      $ad_id      = Utils::generate_ad_id("{$account_id}_{$site_id}_{$ad_position}");

      $script = "  googletag.defineSlot('" . $ad_path . "', " . json_encode($sizes) . ", '" . $ad_id . "')";

      $script .= ".setTargeting('pos', ['" . $ad_position . "'])";

      $script .= ".setTargeting('reg', ['anonymous'])";

      if (isset($context['targetings'])) {

         foreach ($context['targetings'] as $key => $targeting) {

            $script .= ".setTargeting('" . $key . "', ['" . $targeting . "'])";
         }
      }

      if (isset($ad['breakpoints'])) {

         $script .= ".defineSizeMapping(googletag.sizeMapping()";
      }

      foreach ($devices as $device) {
         if (isset($ad['breakpoints'][$device])) {
            $script .= ".addSize(" . json_encode($ad['breakpoints'][$device]['breakpoint']) . ", " . json_encode($ad['breakpoints'][$device]['ad_sizes']) . ")";
         }
      }

      if (isset($ad['breakpoints'])) {

         $script .= ".build())";
      }

      $script .= ".addService(googletag.pubads());";

      echo $script . "\n\r";
   }
}
