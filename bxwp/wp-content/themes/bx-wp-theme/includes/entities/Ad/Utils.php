<?php

namespace CanaisDigitais\Ad;

use <PERSON>ais<PERSON><PERSON>tais\Author\Author;
use CanaisDigitais\Category\Category;
use <PERSON>ais<PERSON><PERSON>tais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}
class Utils
{
   public static $ad_options_page  = 'ad-options';

   public static $ad_breakpoint_alias = ['desktop', 'tablet', 'mobile'];

   public static $fluid_desktop_placeholder_size = [300, 250];

   public static $ad_positions = [

      '300_1_rht' => [
         'breakpoints' => [
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[300, 250]]],
            'desktop' => ['breakpoint' => [779, 0], 'ad_sizes' => [[300, 250], [300, 600]]]
         ],
         'page' => ['article', 'author', 'category', 'tag', 'page', 'homepage'],
         'size' => [[300, 250], [300, 600]]
      ],
      '300_2_rht' => [
         'breakpoints' => [
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[300, 250]]],
            'desktop' => ['breakpoint' => [779, 0], 'ad_sizes' => [[300, 250], [300, 600]]]
         ],
         'page' => ['article', 'homepage'],
         'size' => [[300, 250], [300, 600]]
      ],
      '300_1_lft' => [
         'breakpoints' => [
            'desktop' => ['breakpoint' => [779, 0], 'ad_sizes' => [300, 250]]
         ],
         'page' => ['article', 'author', 'category', 'tag', 'homepage', 'search'],
         'size' => [[300, 250]]
      ],
      '300_2_lft' => [
         'page' => ['homepage'],
         'size' => [[300, 250]]
      ],
      '728_1_a' => [
         'breakpoints' => [
            'desktop' => ['breakpoint' => [1119, 0], 'ad_sizes' => [[970, 90], [970, 250], [728, 90]]],
            'tablet'  => ['breakpoint' => [728, 0], 'ad_sizes' => [728, 90]],
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [320, 50]]
         ],
         'page' => ['article', 'author', 'category', 'tag', 'homepage', 'search'],
         'size' => [[728, 90], [970, 90], [320, 50], [970, 250]]
      ],
      '728_10_a' => [
         'breakpoints' => [
            'desktop' => ['breakpoint' => [728, 0], 'ad_sizes' => [[728, 90]]],
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[320, 50]]]
         ],
         'page' => ['article', 'author', 'category', 'tag', 'search'],
         'size' => [[728, 90], [320, 50]]
      ],
      '728_2_a' => [
         'breakpoints' => [
            'desktop' => ['breakpoint' => [728, 0], 'ad_sizes' => [[728, 90]]],
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[320, 50]]]
         ],
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[728, 90], [320, 50]]
      ],
      'nativekey_1_lft' => [
         'page' => ['article', 'category', 'tag', 'homepage'],
         'size' => 'fluid'
      ],
      'nativekey_2_lft' => [
         'page' => ['article', 'category', 'tag',  'homepage'],
         'size' => 'fluid'
      ],
      'nativekey_12_1' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'nativekey_14_1' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'nativekey_14_2' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'nativekey_14_3' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'nativekey_13_1' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'nativekey_3' => [
         'page' => ['article', 'category', 'tag', 'homepage'],
         'size' => 'fluid'
      ],
      '300_3_rht' => [
         'breakpoints' => [
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[300, 250]]],
            'desktop' => ['breakpoint' => [779, 0], 'ad_sizes' => [[300, 250], [300, 600]]]
         ],
         'page' => ['article'],
         'size' => [[300, 250], [300, 600]]
      ],
      '300_4_rht' => [
         'breakpoints' => [
            'mobile'  => ['breakpoint' => [0, 0], 'ad_sizes' => [[300, 250]]],
            'desktop' => ['breakpoint' => [779, 0], 'ad_sizes' => [[300, 250], [300, 600]]]
         ],
         'page' => ['article'],
         'size' => [[300, 250], [300, 600]]
      ],

      'adhesion' => [
         'page' => ['article', 'category', 'tag',  'homepage'],
         'size' => 'fluid'
      ],
      'customsponsoredlogo1' => [
         'page' => ['article', 'category', 'tag'],
         'size' => [[160, 65]]
      ],
      'customsponsoredlogo2' => [
         'page' => ['article', 'category', 'tag'],
         'size' => [[160, 65]]
      ],
      'inarticlevideo_1_a' => [
         'page' => ['article'],
         'size' => 'fluid'
      ],
      'sponsoredlogo' => [
         'page' => ['category', 'tag', 'homepage'],
         'size' => [[160, 65], [125, 125], [90, 90]]
      ],
      'sponsoredlogo_weather' => [
         'page' => ['article', 'homepage'],
         'size' => [[160, 65], [125, 125], [90, 90]]
      ],
      '100_1' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_2' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_3' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_4' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_5' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_6' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_7' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],
      '100_8' => [
         'page' => ['article', 'author', 'category', 'tag', 'homepage'],
         'size' => [[100, 40]]
      ],

      'footnote' => [
         'page' => ['author', 'article', 'search'],
         'size' => 'fluid'
      ],
      'nativekey_8' => [
         'page' => ['category', 'tag'],
         'size' => 'fluid'
      ],
      'wrap' => [
         'breakpoints' =>
         [
            'desktop' => ['breakpoint' => [1500, 0], 'ad_sizes' => [[1, 1]]]
         ],
         'page' => ['search'],
         'size' => [['fluid']]
      ],
      'commodwidgetlogo' => [
         'page' => ['homepage'],
         'size' => [[160, 65], [125, 125], [90, 90]]
      ],
      'nativekey_main_ge1' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge2' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge3' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge4' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge5' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge6' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_main_ge7' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge1' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge2' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge3' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge4' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge5' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge6' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge7' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_ge8' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'cashgrainwidgetlogo' => [
         'page' => ['homepage'],
         'size' => [[160, 65], [125, 125], [90, 90]]
      ],
      'nativekey_4' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_5' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_6' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_7' => [
         'page' => ['homepage'],
         'size' => 'fluid'
      ],
      'nativekey_10' => [
         'page' => ['category', 'tag'],
         'size' => 'fluid'
      ],
      'gallery_300_right' => [
         'page' => ['article'],
         'size' => [[300, 250]]
      ],
      'gallery_728_a' => [
         'breakpoints' => [
            'desktop' => ['breakpoint' => [1119, 0], 'ad_sizes' => [[970, 90], [970, 250], [728, 90]]],
            'tablet'  => ['breakpoint' =>  [728, 0], 'ad_sizes' => [[728, 90]]],
            'mobile'  => ['breakpoint' =>  [0, 0], 'ad_sizes' => [[320, 50]]],
         ],
         'page' => ['article'],
         'size' => [[728, 90], [970, 90], [320, 50], [970, 250]]
      ],
      'gallery_interstitial' => [
         'page' => ['article'],
         'size' => [[300, 250]]
      ]
   ];

   public static function get_all_ads()
   {
      return self::$ad_positions;
   }

   public static function get_admanager_account_id()
   {
      return (string) get_field('admanager_account_id', self::$ad_options_page);
   }

   public static function get_admanager_site_id()
   {
      return (string) get_field('admanager_adunit', self::$ad_options_page);
   }

   public static function generate_ad_id($ad_name)
   {
      return 'cd_ad_' . preg_replace("/[^a-zA-Z0-9_]/", '', $ad_name);
   }

   public static function get_current_context()
   {
      if (is_page_template('pages/template-form-newsletter.php')) {
         return null;
      }

      $ad_path            = 'homepage';
      $article_term_name  = '';
      $category_term_name = '';
      $author_slug        = '';

      if (is_single()) {

         $CD_Post  = new CD_Post();
         $Category = $CD_Post->get_category('', false);

         if ($Category) {

            $ad_path           = 'article/' . self::sanatize_string_for_ad_manager($Category->get_name());
            $article_term_name = self::sanatize_string_for_ad_manager($Category->get_name());

            if ($Category->get_parent()) {

               $parent_category = new Category(get_category($Category->get_parent()));

               if ($parent_category->is_default_category() === false) {

                  $ad_path           = 'article/' . self::sanatize_string_for_ad_manager($parent_category->get_name()) . '/' .
                     self::sanatize_string_for_ad_manager($Category->get_name());
                  $article_term_name = self::sanatize_string_for_ad_manager($parent_category->get_name());
               }
            }
         }
      }

      if (is_category()) {

         $Category = new Category(get_queried_object());

         $ad_path            = 'categories/' . self::sanatize_string_for_ad_manager($Category->get_name());
         $category_term_name = self::sanatize_string_for_ad_manager($Category->get_name());
      }

      if (is_tag()) {

         $ad_path = 'tags/' . self::sanatize_string_for_ad_manager(single_tag_title('', false));
      }

      if (is_tax('author_tax')) {

         $Author      = new Author();
         $ad_path     = 'author/' . self::sanatize_string_for_ad_manager($Author->get_name());
         $author_slug = self::sanatize_string_for_ad_manager($Author->get_name());
      }

      $ad_targets = [
         'homepage' => [
            'target'    => 'homepage',
            'condition' => is_home() || is_front_page(),
            'ad_path'   => $ad_path,
         ],
         'category' => [
            'target'     => 'category',
            'condition'  => is_category(),
            'ad_path'    => $ad_path,
            'targetings' => [
               'ptype' => 'categories',
               'pterm' => $category_term_name
            ]
         ],
         'tag' => [
            'target'     => 'tag',
            'condition'  => is_tag(),
            'ad_path'    => $ad_path,
            'targetings' => [
               'ptype' => 'tags'
            ],
         ],
         'article' => [
            'target'     => 'article',
            'condition'  => is_single(),
            'ad_path'    => $ad_path,
            'targetings' => [
               'ptype' => 'Article',
               'nid'   => get_the_ID(),
               'pterm' => $article_term_name
            ]
         ],
         'search' => [
            'target'     => 'search',
            'condition'  => is_search(),
            'ad_path'    => 'search',
            'targetings' => [
               'ptype' => 'search'
            ]
         ],
         'author' => [
            'target'     => 'author',
            'condition'  => is_tax('author_tax'),
            'ad_path'    => $ad_path,
            'targetings' => [
               'ptype'  => 'Author',
               'author' => $author_slug
            ],
         ],
      ];

      foreach ($ad_targets as $key => $value) {
         if ($value['condition'] === true) {
            return $ad_targets[$key];
         }
      }
   }

   public static function sanatize_string_for_ad_manager($string)
   {

      $string = strtolower(trim($string));
      $string = str_replace(' ', '_', $string);

      return $string;
   }
}
