<?php

namespace CanaisDigitais\Mail;

if (!defined('ABSPATH')) exit;

class Register
{
   public function __construct()
   {
      \add_action('phpmailer_init', [$this, 'phpmailer_init']);
      \add_action('wp_mail_failed', [$this, 'report_phpmailer_errors']);
   }

   public function phpmailer_init($phpmailer)
   {
      $admin_email = \get_option('admin_email');
      $site_name   = \get_bloginfo('name');

      $phpmailer->Mailer     = 'smtp';
      $phpmailer->Host       = WP_SMTP_HOST;
      $phpmailer->Username   = WP_SMTP_USER;
      $phpmailer->Password   = WP_SMTP_PASS;
      $phpmailer->SMTPAuth   = true;
      $phpmailer->Port       = 587;
      $phpmailer->SMTPSecure = 'tls';
   
      $phpmailer->SetFrom($admin_email, $site_name);
      $phpmailer->addReplyTo($admin_email, $site_name);

      // $phpmailer->SMTPDebug  = 3;
   }

   public function report_phpmailer_errors($error)
   {
      error_log($error->get_error_message());
   }
}
