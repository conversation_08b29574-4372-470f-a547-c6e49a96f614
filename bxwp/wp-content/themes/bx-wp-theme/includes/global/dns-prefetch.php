<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('wp_resource_hints', 'bx_cd_add_resource_hints', 5, 2);
function bx_cd_add_resource_hints($urls, $relation_type)
{
   global $pagenow;

   if ($pagenow === 'wp-login.php') {
      return $urls;
   }

   if (!in_array($relation_type, ['dns-prefetch', 'preconnect'])) {
      return $urls;
   }

   if (is_admin()) {
      return $urls;
   }

   $dns_domains = [
      ['href' => 'https://www.googletagmanager.com'],
      ['href' => 'https://www.gstatic.com'],
      ['href' => 'https://www.youtube.com'],
      ['href' => 'https://www.instagram.com/'],
      ['href' => 'https://static.iris.informa.com/'],
   ];

   return array_merge($urls, $dns_domains);
}
