<?php

if (!defined('ABSPATH')) exit;

add_action('after_setup_theme', 'bx_cd_rss_enable_and_tweaks');

function bx_cd_rss_enable_and_tweaks()
{
   add_theme_support('automatic-feed-links');
   add_filter('feed_links_show_comments_feed', '__return_false');
}

add_action('rss2_item', 'bx_cd_add_media_content_to_rss_feed');

function bx_cd_add_media_content_to_rss_feed()
{
   global $post;

   if (!has_post_thumbnail($post)) {
      return;
   }

   $thumbnail_id = get_post_thumbnail_id($post->ID);
   $thumbnail    = wp_get_attachment_image_src($thumbnail_id, 'full');

   $thumbnail_url    = $thumbnail[0];
   $thumbnail_width  = $thumbnail[1];
   $thumbnail_height = $thumbnail[2];
   $thumbnail_type   = get_post_mime_type($thumbnail_id);
   $thumbnail_length = filesize(get_attached_file($thumbnail_id));

   printf(
      '<enclosure url="%1$s" width="%2$s" height="%3$s" length="%4$s" medium="image" type="%5$s" />' . "\n",
      $thumbnail_url,
      $thumbnail_width,
      $thumbnail_height,
      $thumbnail_length,
      $thumbnail_type,
   );
}
