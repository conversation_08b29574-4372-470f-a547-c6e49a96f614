<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('admin_init', 'bx_cd_register_cron_custom_roles');
function bx_cd_register_cron_custom_roles()
{
   if (!bx_cd_get_ran_scheduled_unique_events('bx_cd_cron_register_custom_role_support')) {
      wp_schedule_single_event(time(), 'bx_cd_cron_register_custom_role_support');
   }

   if (!bx_cd_get_ran_scheduled_unique_events('bx_cd_cron_register_theme_options_capability_to_roles')) {
      wp_schedule_single_event(time(), 'bx_cd_cron_register_theme_options_capability_to_roles');
   }
}

add_action('bx_cd_cron_register_custom_role_support', 'bx_cd_register_custom_role_support');
function bx_cd_register_custom_role_support()
{
   bx_cd_add_ran_scheduled_unique_events('bx_cd_cron_register_custom_role_support');

   add_role('support', esc_html__('Suporte', 'canais-digitais'));

   $role = get_role('support');

   if (empty($role)) {
      return;
   }

   $role->add_cap('bx_manage_theme_options');
   $role->add_cap('read');

   if (!is_plugin_active('wordpress-seo/wp-seo.php')) {
      return;
   }

   $role->add_cap('wpseo_manage_options');
   $role->add_cap('wpseo_bulk_edit');
   $role->add_cap('wpseo_edit_advanced_metadata');
   $role->add_cap('wpseo_manage_redirects');
}

add_action('bx_cd_cron_register_theme_options_capability_to_roles', 'bx_cd_register_theme_options_capability_to_roles');
function bx_cd_register_theme_options_capability_to_roles()
{
   bx_cd_add_ran_scheduled_unique_events('bx_cd_cron_register_theme_options_capability_to_roles');

   global $wp_roles;

   if (empty($wp_roles)) {
      $wp_roles = new \WP_Roles();
   }

   foreach ($wp_roles->roles as $role_slug => $role_data) {
      $role = get_role($role_slug);

      if ($role && $role->has_cap('manage_options')) {
         $role->add_cap('bx_manage_theme_options');
      }
   }
}
