<?php

use CanaisDigitais\Form\Utils as FormUtils;

if (!defined('ABSPATH')) exit;

add_action('wp_enqueue_scripts', 'bx_cd_enqueue_main_assets');
function bx_cd_enqueue_main_assets()
{
   wp_enqueue_style('all', get_theme_file_uri('assets/css/all.min.css'));
   wp_enqueue_script('all', get_theme_file_uri('assets/js/all.min.js'), ['gearbox', 'alpine'], false, true);

   wp_localize_script('all', 'messages', [
      'download_document'           => esc_html__('Baixar documento', 'canais_digitais'),
      'download_error'              => esc_html__('Erro ao baixar documento. Tente novamente em instantes.', 'canais_digitais'),
      'newsletter_signup_failed'    => esc_html__('Falha no cadastro. Tente novamente em instantes.', 'canais_digitais'),
      'newsletter_signup_sucessful' => esc_html__('Cadastro realizado com sucesso.', 'canais_digitais'),
      'newsletter_signup_spam'      => esc_html__('Sua mensagem não foi enviada, pois foi considerada spam. Tente mais tarde.', 'canais_digitais'),
   ]);

   if (is_page_template('pages/template-form-newsletter.php') && FormUtils::is_lead_form_enabled()) {
      wp_localize_script('all', 'leadFormData', FormUtils::get_lead_form_config_data());
   }
}

add_action('admin_enqueue_scripts', 'bx_cd_enqueue_admin_assets');
function bx_cd_enqueue_admin_assets($current_page)
{

   if ($current_page == 'post-new.php' || $current_page == 'post.php' || $current_page == 'term.php') {
      wp_enqueue_style('admin', get_theme_file_uri('assets/css/admin.min.css'));
   }
}
