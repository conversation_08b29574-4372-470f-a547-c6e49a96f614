<?php

/**
 * Name: General_Options
 * Custom Fields: texto do rodapé, logo do rodapé, texto de copyright do rodapé, título de download do CTA, descrição do download de CTA, título de header de archive de vídeos, descrição de archive de vídeos, cor destacada de archive de vídeos, cor destacada com opacidade de archive de vídeos, cor do texto de archive de vídeos, imagem destacada de archive de vídeo, mostrar cor de archive de vídeos, título global para especialista no singular, título global para especialista no plural, título de single de download no paywall, descrição de título de download no paywall
 * Description: Opções de customizações globais.
 */

namespace CanaisDigitais\Theme_Settings;

use CanaisDigitais\Author\Utils as AuthorUtils;

if (!defined('ABSPATH')) exit;

class Register_General_Options
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);

      add_action('init', [$this, 'register_general_options_fields']);
      add_filter('bx_cd_general_options_tabs', [$this, 'general_options_tab_custom_naming'], 5);
      add_filter('bx_cd_general_options_tabs', [$this, 'general_options_tab_download_material_CTA'], 10);
      add_filter('bx_cd_general_options_tabs', [$this, 'general_options_tab_single_download'], 15);
      add_filter('bx_cd_general_options_tabs', [$this, 'general_options_tab_archive_video'], 20);
      add_filter('bx_cd_general_options_tabs', [$this, 'general_options_tab_footer'], 25);
      add_filter('bx_cd_general_options_tabs', [$this, '_general_options_tab_form'], 30);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_options_sub_page')) {
         return;
      }

      acf_add_options_sub_page([
         'page_title'  => 'Opções Gerais',
         'menu_title'  => 'Opções Gerais',
         'menu_slug'   => 'general-options',
         'capability'  => 'bx_manage_theme_options',
         'post_id'     => 'general-options',
         'parent_slug' => 'theme-settings',
      ]);
   }

   public function register_general_options_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_xx1boznj4v7b',
         'title'  => 'Campos de Opções Gerais',
         'fields' => array_merge(...apply_filters('bx_cd_general_options_tabs', [])),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'general-options',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function general_options_tab_footer($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_6536973235bfb',
            'label'             => 'Footer',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_65369787a6cac',
            'label'             => 'Logo',
            'name'              => 'footer_logo',
            'aria-label'        => '',
            'type'              => 'image',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'return_format' => 'url',
            'library'       => 'all',
            'min_width'     => '',
            'min_height'    => '',
            'min_size'      => '',
            'max_width'     => '',
            'max_height'    => '',
            'max_size'      => '',
            'mime_types'    => '',
            'preview_size'  => 'thumbnail',
         ),
         array(
            'key'               => 'field_653697a1a6cad',
            'label'             => 'Texto',
            'name'              => 'footer_text',
            'aria-label'        => '',
            'type'              => 'textarea',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'rows'          => '',
            'placeholder'   => '',
            'new_lines'     => '',
         ),
         array(
            'key'               => 'field_653697eea6caf',
            'label'             => 'Direitos Autorais - Texto',
            'name'              => 'footer_copyright_text',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
      ];

      return $fields;
   }

   public function general_options_tab_download_material_CTA($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_3pixdo87ou17',
            'label' => '[CTA] - Material para Download]',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_e95fk4g8orjf',
            'label' => 'Título',
            'name' => 'download_material_cta_title',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => 'Download',
            'new_lines' => '',
         ),
         array(
            'key' => 'field_pl7h9d5e0gsc',
            'label' => 'Descrição',
            'name' => 'download_material_cta_description',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => 'Acesse o acervo completo de Ebooks de todos os setores alimentícios',
            'new_lines' => '',
         ),
      ];

      return $fields;
   }

   public function general_options_tab_single_download($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_656d76b58b91c5',
            'label' => '[POST] - Bloco Download',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_65676dbf3b91c6',
            'label' => 'Titulo',
            'name' => 'single_download_title',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
         ),
         array(
            'key' => 'field_65676c0ebd91c7',
            'label' => 'Descrição',
            'name' => 'single_download_description',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
      ];

      return $fields;
   }

   public function general_options_tab_archive_video($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_6566274a43f86',
            'label' => 'Página de Vídeos',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_6566277d43f87',
            'label' => 'Header',
            'name' => 'videos_archive_header',
            'aria-label' => '',
            'type' => 'group',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'layout' => 'block',
            'sub_fields' => array(
               array(
                  'key' => 'field_65662ad9db763',
                  'label' => 'Nome',
                  'name' => 'title',
                  'aria-label' => '',
                  'type' => 'textarea',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => 'Vídeos',
                  'maxlength' => '',
                  'rows' => '',
                  'placeholder' => '',
                  'new_lines' => '',
               ),
               array(
                  'key' => 'field_65662af1db764',
                  'label' => 'Descrição',
                  'name' => 'description',
                  'aria-label' => '',
                  'type' => 'textarea',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => '',
                  'maxlength' => '',
                  'rows' => '',
                  'placeholder' => '',
                  'new_lines' => '',
               ),
               array(
                  'key' => 'field_6566282f43f88',
                  'label' => 'Cor Destacada',
                  'name' => 'featured_color',
                  'aria-label' => '',
                  'type' => 'color_picker',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => '#1CBF69',
                  'enable_opacity' => 0,
                  'return_format' => 'string',
               ),
               array(
                  'key' => 'field_6566287043f89',
                  'label' => 'Imagem de Fundo',
                  'name' => 'featured_image',
                  'aria-label' => '',
                  'type' => 'image',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'return_format' => 'url',
                  'library' => 'all',
                  'min_width' => '',
                  'min_height' => '',
                  'min_size' => '',
                  'max_width' => '',
                  'max_height' => '',
                  'max_size' => '',
                  'mime_types' => '',
                  'preview_size' => 'medium',
               ),
               array(
                  'key' => 'field_6566289443f8a',
                  'label' => 'Fundo com opacidade',
                  'name' => 'featured_color_with_opacity',
                  'aria-label' => '',
                  'type' => 'color_picker',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => '',
                  'enable_opacity' => 1,
                  'return_format' => 'string',
               ),
               array(
                  'key' => 'field_656628bd43f8b',
                  'label' => 'Cor do Texto',
                  'name' => 'text_color',
                  'aria-label' => '',
                  'type' => 'color_picker',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => '#FFFFFF',
                  'enable_opacity' => 0,
                  'return_format' => 'string',
               ),
               array(
                  'key' => 'field_656628e943f8c',
                  'label' => 'Mostrar cor no bloco interno',
                  'name' => 'show_color',
                  'aria-label' => '',
                  'type' => 'true_false',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'message' => '',
                  'default_value' => 1,
                  'ui' => 0,
                  'ui_on_text' => '',
                  'ui_off_text' => '',
               ),
            ),
         ),
      ];

      return $fields;
   }

   public function general_options_tab_custom_naming($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_75662b4a43f96',
            'label' => 'Nomenclaturas',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_65773dceec512',
            'label' => 'Nome para "Especialista"',
            'name' => 'general_options_name_for_author_specialist',
            'aria-label' => '',
            'type' => 'group',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'layout' => 'table',
            'sub_fields' => array(
               array(
                  'key' => 'field_65773851bbe29',
                  'label' => 'Singular',
                  'name' => 'general_options_name_for_author_specialist_singular',
                  'aria-label' => '',
                  'type' => 'text',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => AuthorUtils::get_custom_specialist_naming('singular'),
                  'maxlength' => '',
                  'placeholder' => '',
                  'prepend' => '',
                  'append' => '',
               ),
               array(
                  'key' => 'field_65773da69fb62',
                  'label' => 'Plural',
                  'name' => 'general_options_name_for_author_specialist_plural',
                  'aria-label' => '',
                  'type' => 'text',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => AuthorUtils::get_custom_specialist_naming('plural'),
                  'maxlength' => '',
                  'placeholder' => '',
                  'prepend' => '',
                  'append' => '',
               ),
            ),
         ),

      ];

      return $fields;
   }

   public function _general_options_tab_form($fields)
   {
      $fields[] = [
         [
            'key'               => 'field_675749642bbd6',
            'label'             => __('Formulário', 'canais-digitais'),
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'placement' => 'top',
            'endpoint'  => 0,
         ],
         [
            'key'               => 'field_675749aa2bbd7',
            'label'             => __('Ativar formulário de leads', 'canais-digitais'),
            'name'              => 'general_options_form_is_active',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'message'       => __('Marque para habilitar o formulário de leads na Home e nos detalhes das notícias de materiais para downloads', 'canais-digitais'),
            'default_value' => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
            'ui'            => 1,
         ],
         [
            'key'               => 'field_2787d9cf15301',
            'label'             => 'Endpoint do Form',
            'name'              => 'bx_cd_eloqua_endpoint',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_2787d9cd15809',
            'label'             => 'Endpoint de Customer GUID',
            'name'              => 'bx_cd_eloqua_endpoint_customer_guid',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_3787f3cf1d403',
            'label'             => 'ID do Site (elqSiteId)',
            'name'              => 'bx_cd_elqSiteId',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_Xxg6KDV7Lo0N',
            'label'             => 'Código do Portal (Brand)',
            'name'              => 'bx_cd_brand',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_9787f9cf15409',
            'label'             => 'Nome do Form (elqFormName)',
            'name'              => 'bx_cd_elqFormName',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_a9Xk3Bv7LtQ2',
            'label'             => 'Título do Formulário (Newsletter)',
            'name'              => 'bx_cd_form_title_newsletter',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_Kj7mP9vN4x2L',
            'label'             => 'Descrição do formulário (Newsletter)',
            'name'              => 'bx_cd_form_description_newsletter',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_3wR8nQ5hB9tY',
            'label'             => 'Descrição do formulário (Download)',
            'name'              => 'bx_cd_form_description_download',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value'     => '',
            'maxlength'         => '',
            'allow_in_bindings' => 1,
            'rows'              => '',
            'placeholder'       => '',
         ],
         [
            'key'               => 'field_67aa294e50412',
            'label'             => esc_attr__('Imagem de fundo do Header', 'canais-digitais'),
            'name'              => 'form_header_background_image',
            'aria-label'        => '',
            'type'              => 'image',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_675749aa2bbd7',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'return_format' => 'array',
            'library'       => 'all',
            'min_width'     => '',
            'min_height'    => '',
            'min_size'      => '',
            'max_width'     => '',
            'max_height'    => '',
            'max_size'      => '',
            'mime_types'    => '',
            'preview_size'  => 'medium',
         ],
      ];

      return $fields;
   }
}
