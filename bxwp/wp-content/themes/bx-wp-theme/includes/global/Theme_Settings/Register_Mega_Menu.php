<?php

/**
 * Name: Mega_Menu
 * Custom Fields: t<PERSON><PERSON>lo de link, tipo de link, página do link, categoria do link, título do link externo, url do link externo
 * Description: Opções de mega menu do header.
 */

namespace CanaisDigitais\Theme_Settings;

if (!defined('ABSPATH')) {
   exit;
}

class Register_Mega_Menu
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);

      add_action('init', [$this, 'register_mega_menu_fields']);
      add_filter('bx_cd_mega_menu_tabs', [$this, 'tab_mega_menu'], 5);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_options_sub_page')) {
         return;
      }

      acf_add_options_sub_page([
         'page_title'  => 'Mega Menu',
         'menu_title'  => 'Mega Menu',
         'menu_slug'   => 'mega-menu',
         'capability'  => 'bx_manage_theme_options',
         'post_id'     => 'mega-menu',
         'parent_slug' => 'theme-settings',
      ]);
   }

   public function register_mega_menu_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_6d5dgfdf48ddc1f3',
         'title'  => 'Mega Menu',
         'fields' => array_merge(...apply_filters('bx_cd_mega_menu_tabs', [])),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'mega-menu',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function tab_mega_menu($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_65bba079d0fb6',
            'label' => 'Mega Menu',
            'name' => 'bx_cd_mega_menu',
            'aria-label' => '',
            'type' => 'repeater',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'layout' => 'table',
            'pagination' => 0,
            'min' => 0,
            'max' => 0,
            'collapsed' => '',
            'button_label' => 'Adicionar linha',
            'rows_per_page' => 20,
            'sub_fields' => array(
               array(
                  'key' => 'field_65bba0b3d0fb7',
                  'label' => 'Titulo',
                  'name' => 'title',
                  'aria-label' => '',
                  'type' => 'text',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '22',
                     'class' => '',
                     'id' => '',
                  ),
                  'default_value' => '',
                  'maxlength' => '',
                  'placeholder' => '',
                  'prepend' => '',
                  'append' => '',
                  'parent_repeater' => 'field_65bba079d0fb6',
               ),
               array(
                  'key' => 'field_65bba188951bf',
                  'label' => 'Tipo',
                  'name' => 'type',
                  'aria-label' => '',
                  'type' => 'radio',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'choices' => array(
                     'page' => 'Página',
                     'post' => 'Post',
                     'category' => 'Categoria',
                     'external_link' => 'Link externo',
                  ),
                  'default_value' => '',
                  'return_format' => 'value',
                  'allow_null' => 0,
                  'other_choice' => 0,
                  'layout' => 'vertical',
                  'save_other_choice' => 0,
                  'parent_repeater' => 'field_65bba079d0fb6',
               ),
               array(
                  'key' => 'field_65bba0cad0fb8',
                  'label' => 'Links',
                  'name' => 'links',
                  'aria-label' => '',
                  'type' => 'repeater',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'layout' => 'table',
                  'pagination' => 0,
                  'min' => 0,
                  'max' => 0,
                  'collapsed' => '',
                  'button_label' => 'Adicionar linha',
                  'rows_per_page' => 20,
                  'sub_fields' => array(
                     array(
                        'key' => 'field_65bba2a473240',
                        'label' => 'Página',
                        'name' => 'page',
                        'aria-label' => '',
                        'type' => 'post_object',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => array(
                           array(
                              array(
                                 'field' => 'field_65bba188951bf',
                                 'operator' => '==',
                                 'value' => 'page',
                              ),
                           ),
                        ),
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'post_type' => array(
                           0 => 'page',
                        ),
                        'post_status' => '',
                        'taxonomy' => '',
                        'return_format' => 'id',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'ui' => 1,
                        'parent_repeater' => 'field_65bba0cad0fb8',
                     ),
                     array(
                        'key' => 'field_65bba2c573241',
                        'label' => 'Post',
                        'name' => 'post',
                        'aria-label' => '',
                        'type' => 'post_object',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => array(
                           array(
                              array(
                                 'field' => 'field_65bba188951bf',
                                 'operator' => '==',
                                 'value' => 'post',
                              ),
                           ),
                        ),
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'post_type' => array(
                           0 => 'post',
                        ),
                        'post_status' => '',
                        'taxonomy' => '',
                        'return_format' => 'id',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'ui' => 1,
                        'parent_repeater' => 'field_65bba0cad0fb8',
                     ),
                     array(
                        'key' => 'field_65bba2d973242',
                        'label' => 'Categoria',
                        'name' => 'category',
                        'aria-label' => '',
                        'type' => 'taxonomy',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => array(
                           array(
                              array(
                                 'field' => 'field_65bba188951bf',
                                 'operator' => '==',
                                 'value' => 'category',
                              ),
                           ),
                        ),
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'taxonomy' => 'category',
                        'add_term' => 1,
                        'save_terms' => 1,
                        'load_terms' => 0,
                        'return_format' => 'id',
                        'field_type' => 'select',
                        'allow_null' => 0,
                        'multiple' => 0,
                        'parent_repeater' => 'field_65bba0cad0fb8',
                     ),
                     array(
                        'key' => 'field_65bba2ff73244',
                        'label' => 'Titulo do Link',
                        'name' => 'external_link_title',
                        'aria-label' => '',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => array(
                           array(
                              array(
                                 'field' => 'field_65bba188951bf',
                                 'operator' => '==',
                                 'value' => 'external_link',
                              ),
                           ),
                        ),
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'default_value' => '',
                        'maxlength' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                        'parent_repeater' => 'field_65bba0cad0fb8',
                     ),
                     array(
                        'key' => 'field_65bba2f073243',
                        'label' => 'Link externo',
                        'name' => 'external_link',
                        'aria-label' => '',
                        'type' => 'url',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => array(
                           array(
                              array(
                                 'field' => 'field_65bba188951bf',
                                 'operator' => '==',
                                 'value' => 'external_link',
                              ),
                           ),
                        ),
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '',
                        'parent_repeater' => 'field_65bba0cad0fb8',
                     ),
                  ),
                  'parent_repeater' => 'field_65bba079d0fb6',
               ),
            ),
         ),

      ];

      return $fields;
   }
}
