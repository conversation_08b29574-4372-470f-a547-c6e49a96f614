<?php

/**
 * Name: Theme_Settings
 * Description: Cria a página raiz de opções customizadas.
 */

namespace CanaisDigitais\Theme_Settings;

if (!defined('ABSPATH')) exit;

class Register
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);
      add_filter('acf/fields/post_object/query', [$this, 'modify_all_post_object_order']);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_options_sub_page')) {
         return;
      }

      acf_add_options_page([
         'page_title'  => 'Opções do tema',
         'menu_title'  => 'Opções do tema',
         'menu_slug'   => 'theme-settings',
         'capability'  => 'bx_manage_theme_options',
         'position'    => 5,
         'parent_slug' => '',
         'icon_url'    => 'dashicons-admin-generic',
         'redirect'    => true,
         'post_id'     => 'theme-settings',
         'autoload'    => false,
      ]);
   }

   public function modify_all_post_object_order($args)
   {
      $args['orderby'] = 'date';
      $args['order'] = 'DESC';

      return $args;
   }
}
