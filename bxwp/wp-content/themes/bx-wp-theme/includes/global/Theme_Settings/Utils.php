<?php

namespace CanaisDigitais\Theme_Settings;

use <PERSON><PERSON><PERSON><PERSON>tais\CD_Post\Utils as PostUtils;

if (!defined('ABSPATH')) exit;

class Utils
{
   static $homepage_option_page = 'homepage-options';
   static $homepage_featured_materials_default_title = 'Mais destaques';

   static $selectable_sections = [
      'featured-materials' => 'featured-materials',
      'events'             => 'grid-events',
      'specialists'        => 'grid-specialists',
      'videos'             => 'videos',
      'newsletter'         => 'newsletter'
   ];

   static public function get_homepage_ordering_sections()
   {
      return get_field('ordering_sections', self::$homepage_option_page);
   }

   static public function get_homepage_featured_materials_data()
   {
      global $excluded_posts_ID;

      $featured_materials = get_field('homepage_featured_materials', self::$homepage_option_page) ?: [];
      $title = get_field('homepage_featured_materials_title', self::$homepage_option_page) ?: self::$homepage_featured_materials_default_title;
      $download = get_field('homepage_featured_materials_download', self::$homepage_option_page);
      $show_recent = get_field('homepage_featured_materials_show_recent_posts', self::$homepage_option_page);

      if ($show_recent) {
         $featured_materials = PostUtils::get_latest_articles_ids(7, $excluded_posts_ID);
      } else {
         $featured_materials = array_diff($featured_materials, $excluded_posts_ID);

         if (count($featured_materials) < 7) {
            $needed = 7 - count($featured_materials);
            $additional = PostUtils::get_latest_articles_ids($needed, array_merge($excluded_posts_ID, $featured_materials));
            $featured_materials = array_merge($featured_materials, $additional);
         }
      }

      if (!empty($download)) {
         $featured_materials = array_slice($featured_materials, 0, 6);

         $download = array_diff([$download], $excluded_posts_ID, $featured_materials);
         $download = $download ? reset($download) : PostUtils::get_latest_articles_ids(1, array_merge($featured_materials, $excluded_posts_ID))[0] ?? null;

         if (!empty($download)) {
            $excluded_posts_ID[] = $download;
         }
      }

      $excluded_posts_ID = array_merge($excluded_posts_ID, $featured_materials);

      return [
         'materials' => $featured_materials,
         'title'     => $title,
         'download'  => $download
      ];
   }

   static public function get_homepage_categories_list()
   {
      return get_field('homepage_categories', self::$homepage_option_page);
   }
}
