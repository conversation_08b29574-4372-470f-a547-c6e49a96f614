<?php

use CanaisDigitais\Form\Utils;

if (!defined('ABSPATH')) {
   exit;
}

add_action('wp_enqueue_scripts', 'bx_cd_add_javascript_variables');
function bx_cd_add_javascript_variables()
{
   if (wp_script_is('all', 'enqueued')) {

      wp_localize_script('all', 'ajax_object', array(
         'ajax_url' => admin_url('admin-ajax.php'),
         'nonce' => wp_create_nonce('secure_file_download')
      ));
   }
}

add_action('wp_ajax_secure_file_download', 'bx_cd_handle_download');
add_action('wp_ajax_nopriv_secure_file_download', 'bx_cd_handle_download');
function bx_cd_handle_download()
{

   if (!isset($_COOKIE[Utils::$has_newsletter_signup_cookie_name])) {
      wp_send_json_error([], WP_Http::UNAUTHORIZED);
      exit;
   }

   if (isset($_POST['file_id']) && is_numeric($_POST['file_id'])) {
      $file_id = intval($_POST['file_id']);
      $file_path = get_attached_file($file_id);
      $file_url = wp_get_attachment_url($file_id);

      if ($file_path && $file_url) {
         header('Content-Description: File Transfer');
         header('Content-Type: application/pdf');
         header('Content-Disposition: attachment; filename="' . basename($file_path) . '"');
         header('Expires: 0');
         header('Cache-Control: must-revalidate');
         header('Pragma: public');
         header('Content-Length: ' . filesize($file_path));
         readfile($file_path);
         exit;
      }
   }

   wp_send_json_error([], WP_Http::NOT_FOUND);
}
