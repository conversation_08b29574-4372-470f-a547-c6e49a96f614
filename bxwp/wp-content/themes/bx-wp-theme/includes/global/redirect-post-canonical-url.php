<?php

if (! defined('ABSPATH')) {
   exit;
}

add_action('template_redirect', 'bx_cd_redirect_post_to_canonical_url');
function bx_cd_redirect_post_to_canonical_url()
{
   if (!is_singular('post') || is_admin() || is_preview()) {
      return;
   }

   $current_url   = home_url($_SERVER['REQUEST_URI'] ?? '');
   $canonical_url = get_permalink(get_queried_object_id());

   $params = $_SERVER['QUERY_STRING'] ?? '';

   if (!empty($params)) {
      $canonical_url .= "?{$params}";
   }

   if ($current_url === $canonical_url) {
      return;
   }

   wp_redirect($canonical_url, 301);
   exit;
}
