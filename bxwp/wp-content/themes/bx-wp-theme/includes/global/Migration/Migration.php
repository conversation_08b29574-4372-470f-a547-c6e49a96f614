<?php

namespace CanaisDigitais\Migration;

use CanaisDigitais\Category\Utils;

require_once(ABSPATH . 'wp-admin/includes/user.php');

if (!defined('ABSPATH')) exit;

class Migration
{
   private $posts = [];
   private $type;
   private $is_simulation;

   public function __construct(string $type, bool $is_simulation = true)
   {
      $this->type = $type;
      $this->is_simulation = $is_simulation;

      $this->posts = \get_posts([
         'post_type'      => 'post',
         'fields'         => 'ids',
         'posts_per_page' => (isset($_GET['_migration_posts_per_page']) && is_numeric($_GET['_migration_posts_per_page'])) ? (int) $_GET['_migration_posts_per_page'] : 999,
         'order'          => 'DESC',
         'meta_query' => [
            [
               'key'     => '_bx_migration_done',
               'compare' => 'NOT EXISTS',
               'value'   => 'false',
            ]
         ]
      ]);
   }

   public function run()
   {
      if ($this->type === 'pre_posts') {
         $this->convert_authors();
         $this->process_categories();
      } else if ($this->type === 'only_posts') {
         $this->process_posts();
         //    $this->regenerate_thumbnails();
      } else if ($this->type === 'fix_authors') {
         $this->fix_authors();
      }
   }

   private function process_posts()
   {
      if (empty($this->posts)) {
         $this->print_alert('Nenhum post encontrado.');
         return;
      }

      foreach ($this->posts as $post_id) {

         $terms =  \get_the_terms($post_id, 'article_type');

         if (!is_wp_error($terms) && !empty($terms)) {
            $term  =  $terms[0]->slug;

            if ($term === 'video') {

               $this->process_video($post_id);
            } elseif ($term === 'gallery') {

               $this->process_gallery($post_id);
            } elseif ($term === 'whitepaper') {

               $this->process_white_paper($post_id);
            } elseif ($term === 'audio') {

               $this->process_podcast($post_id);
            } elseif ($term === 'article') {

               $this->process_article($post_id);
            }
         }

         $this->process_featured_image($post_id);
         $this->process_excerpt($post_id);
         $this->process_post_authors($post_id);
         $this->process_byline($post_id);
         $this->process_related_posts($post_id);

         if ($this->is_simulation === true) {
            continue;
         }

         update_post_meta($post_id, '_bx_migration_done', 'f');
      }
   }

   private function fix_authors()
   {
      $posts = \get_posts([
         'post_type'      => 'post',
         'fields'         => 'ids',
         'posts_per_page' => (isset($_GET['_migration_posts_per_page']) && is_numeric($_GET['_migration_posts_per_page'])) ? (int) $_GET['_migration_posts_per_page'] : 999,
         'order'          => 'DESC',
         'meta_query' => [
            [
               'key'     => '_fix_bx_migration_done',
               'compare' => 'NOT EXISTS',
               'value'   => 'false',
            ]
         ]
      ]);

      foreach ($posts as $post_id) {

         $this->process_post_authors($post_id);
         update_post_meta($post_id, '_fix_bx_migration_done', 'f');
      }
   }

   private function print_alert(string $message)
   {
      if (empty($message)) {
         return;
      }

      echo esc_html($message) . "<br>\n";
   }

   private function process_categories()
   {
      if ($this->is_simulation === true) {
         return;
      }

      $default_event_category = get_term_by('slug', Utils::get_default_category_slug('event'), 'category');
      $categories             = get_categories();

      foreach ($categories as $category) {

         $result_update_category_color =  update_term_meta($category->term_id, 'category_color', '#1CBF69');

         if (!is_wp_error($result_update_category_color) && $result_update_category_color !== false) {

            $this->print_alert('SUCCESS: Categoria ' . $category->term_id . ' recebeu cor destacada com sucesso.');
         } else {

            $this->print_alert('FAILURE: Categoria ' . $category->term_id . ' teve erro ao receber cor destacada.');
         }

         if ($category->parent === $default_event_category->term_id) {
            update_term_meta($category->term_id, 'category_type', 'event');
         }
      }
   }
   private function process_article($post_id)
   {
      if ($this->is_simulation === true) {
         return;
      }

      $this->attach_default_category(Utils::get_default_category_slug('article'), $post_id);
   }

   private function process_byline(int $post_id)
   {
      $byline = esc_html(get_field('penton_byline', $post_id));

      if ($this->is_simulation === true) {

         if (!empty($byline)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui byline.');
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui byline.');
            return;
         }
      } else {
         if (!empty($byline)) {

            delete_post_meta($post_id, 'post_byline');

            $result_updating_post_byline = update_post_meta($post_id, 'post_byline', $byline);

            if ($result_updating_post_byline != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu byline com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao receber byline.');
            }
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui byline.');
         }
      }
   }

   private function process_excerpt(int $post_id)
   {
      $excerpt = get_field('penton_content_summary', $post_id);

      if ($this->is_simulation === true) {

         if (!empty($excerpt)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui excerpt.');
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui excerpt.');
            return;
         }
      } else {

         if (!empty($excerpt)) {
            $result_updating_post = wp_update_post([
               'ID'           => $post_id,
               'post_excerpt' => wp_filter_kses($excerpt),
            ]);

            if (!is_wp_error($result_updating_post) && $result_updating_post != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' atualizou excerpt com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro na atualização do excerpt.');
            }
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui excerpt.');
         }
      }
   }

   private function process_video(int $post_id)
   {
      $video_url   = '';
      $media_embed = '';

      $media_entity = get_field('penton_link_media_entity', $post_id);

      if (!empty($media_entity)) {
         $media_embed = get_field('penton_media_embed', $media_entity[0]->ID);
      }

      if (empty($media_embed)) {
         $post_content = get_the_content(null, false, $post_id);
         preg_match('/<iframe.*<\/iframe>/', $post_content, $matches);

         if (!empty($matches)) {
            $media_embed = $matches[0];
         }
      }

      if (!empty($media_embed)) {

         $video_url = $this->extract_video_url($media_embed);
      }

      if ($this->is_simulation === true) {

         if (!empty($video_url)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui vídeo.');
         } else {
            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui vídeo.');
         }
      } else {

         $this->attach_default_category(Utils::get_default_category_slug('video'), $post_id);

         if (!empty($video_url)) {

            delete_post_meta($post_id, 'video_single_embed');

            $result_updating_video_embed = update_field('video_single_embed', $video_url, $post_id);

            if ($result_updating_video_embed != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu vídeo com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao receber vídeo.');
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui vídeo.');
         }
      }
   }

   private function process_gallery(int $post_id)
   {
      $has_slideshow_block = has_block('acf/cd-slideshow', $post_id);

      if ($has_slideshow_block === true) {

         $this->print_alert('INFO: Post ' . $post_id . ' já possui galeria incluída.');
         return;
      }

      $gallery = get_field('penton_link_image_gallery', $post_id);

      if ($this->is_simulation === true) {

         if (empty($gallery)) {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui galeria.');
         } else {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui galeria.');
         }
      } else {

         $this->attach_default_category(Utils::get_default_category_slug('article'), $post_id);

         if (!empty($gallery)) {

            $gallery = array_column($gallery, 'ID');

            $slideshow_block = [
               'name' => 'acf/cd-slideshow',
               'data' => [],
               'mode' => 'auto',
            ];

            $slides = [];
            $count = 0;

            foreach ($gallery as $gallery_image_id) {

               $image_file    = get_field('penton_media_image', $gallery_image_id);
               $image_title   = esc_html(get_field('image_alt_mirror', $gallery_image_id)) ?? '';
               $image_file_id = isset($image_file['ID']) ? (int) $image_file['ID'] : 0;

               if (empty($image_file) && $image_file_id === 0) {
                  continue;
               }

               $slides["block_slides_{$count}_block_single_slide_title"]        = $image_title;
               $slides["_block_slides_{$count}_block_single_slide_title"]       = 'field_6568c1490172b';
               $slides["block_slides_{$count}_block_single_slide_description"]  = '';
               $slides["_block_slides_{$count}_block_single_slide_description"] = 'field_6568c1610172c';
               $slides["block_slides_{$count}_block_single_slide_image"]        = $image_file_id;
               $slides["_block_slides_{$count}_block_single_slide_image"]       = 'field_6568c17b0172d';

               $count++;
            }

            $slides['block_slides']  = count($gallery);
            $slides['_block_slides'] = 'field_6568c12d0172a';

            $slideshow_block['data'] = $slides;

            $block_tag =  '<!-- wp:acf/cd-slideshow ' .
               json_encode($slideshow_block, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE)
               . " /-->\n";

            $post_content     = get_post_field('post_content', $post_id);
            $appended_content = $block_tag . $post_content;

            $result_updating_post = wp_update_post([
               'ID'           => $post_id,
               'post_content' => $appended_content,
            ]);

            if (!is_wp_error($result_updating_post) && $result_updating_post != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' teve galeria inserida com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao inserir galeria.');
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui galeria.');
         }
      }
   }

   private function process_white_paper(int $post_id)
   {
      $media_entity = get_field('penton_link_media_entity', $post_id);
      $attachment_id = '';

      if (!empty($media_entity)) {

         $media_file = get_field('penton_media_content', $media_entity[0]->ID);

         if (isset($media_file['ID'])) {
            $attachment_id = $media_file['ID'];
         } elseif (isset($media_file)) {
            $attachment_id = $media_file;
         }
      }

      if ($this->is_simulation === true) {

         if (empty($attachment_id)) {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui material de download.');
         } else {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui material de download.');
         }
      } else {

         $this->attach_default_category(Utils::get_default_category_slug('download-material'), $post_id);

         if (!empty($attachment_id)) {

            delete_post_meta($post_id, 'download_material_file_upload');

            $result_updating_download_material =  update_post_meta($post_id, 'download_material_file_upload', $attachment_id);

            if ($result_updating_download_material != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu material de download com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao receber material de download.');
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui material de download.');
         }
      }
   }

   private function process_podcast(int $post_id)
   {
      $podcast_url = '';
      $media_embed = '';

      $media_entity = get_field('penton_link_media_entity', $post_id);

      if (!empty($media_entity)) {
         $media_embed = get_field('penton_media_embed', $media_entity[0]->ID);
      }

      if (!empty($media_embed)) {

         $podcast_url = $this->extract_url_from_iframe($media_embed);
      }

      if ($this->is_simulation === true) {

         if (!empty($podcast_url)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui podcast.');
         } else {
            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui podcast.');
         }
      } else {

         $this->attach_default_category(Utils::get_default_category_slug('podcast'), $post_id);

         if (!empty($podcast_url)) {

            delete_post_meta($post_id, 'podcast_single_embed');

            $result_updating_podcast_embed = update_post_meta($post_id, 'podcast_single_embed', $podcast_url);

            if ($result_updating_podcast_embed != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu podcast com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao receber podcast.');
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui podcast.');
         }
      }
   }

   private function convert_authors()
   {
      $authors = get_users([
         'role' => 'author',
      ]);

      if ($this->is_simulation === true) {

         if (empty($authors)) {

            $this->print_alert('FAILURE: Não há autores (wp user).');
         } else {

            $this->print_alert('SUCCESS: há autores (wp user).');
         }
      } else {

         if (!empty($authors)) {
            foreach ($authors as $author) {

               $terms = get_terms('author_tax', [
                  'hide_empty' => false,
                  'meta_key'   => 'attached_wp_user_id',
                  'meta_value' => $author->ID
               ]);

               if ($this->wp_user_has_no_post($author->ID)) {
                  continue;
               }

               if (!empty($terms)) {
                  continue;
               }

               $new_term = wp_insert_term($author->user_firstname . ' ' . $author->user_lastname, 'author_tax');

               if (!is_wp_error($new_term)) {

                  $this->print_alert('SUCCESS: Autor (wp user) ' . $author->ID . ' inserido com sucesso como Autor (author_tax).');

                  update_term_meta($new_term['term_id'], 'attached_wp_user_id', $author->ID);
                  update_term_meta($new_term['term_id'], 'author_type', esc_html('journalist'));
                  $this->process_author_details($author->ID, $new_term['term_id']);
               } else {

                  $this->print_alert('FAILURE: Autor (wp user) ' . $author->ID . ' não pôde ser inserido como Autor (author_tax).');
               }
            }
         } else {

            $this->print_alert('FAILURE: Não há autores (wp user).');
         }
      }
   }

   private function wp_user_has_no_post($wp_user_id)
   {
      $args = [
         'post_type'  => 'post',
         'meta_query' => [
            'relation' => 'OR',
            [
               'key'     => 'penton_author',
               'value'   => '"' . $wp_user_id . '"',
               'compare' => 'LIKE',
            ],
            [
               'key'     => 'penton_author',
               'value'   => $wp_user_id,
               'compare' => '='
            ]
         ]
      ];

      $query_user_posts = new \WP_Query($args);

      \wp_reset_postdata();

      if (0 === $query_user_posts->found_posts) {
         return true;
      }

      return false;
   }
   private function process_author_details($wp_user_id, $author_id)
   {
      $summary      = get_field('penton_summary', 'user_' . $wp_user_id);
      $user_picture = get_field('picture', 'user_' . $wp_user_id);

      if (!empty($user_picture)) {

         $update_avatar_result = update_term_meta($author_id, 'author_avatar', $user_picture);

         if (!is_wp_error($update_avatar_result) && $update_avatar_result !== false) {

            $this->print_alert('SUCCESS: Autor (author_tax) ' . $author_id . ' recebeu avatar com sucesso.');
         } else {

            $this->print_alert('FAILURE: Autor (author_tax) ' . $author_id . ' teve erro ao receber avatar.');
         }
      } else {

         $this->print_alert('FAILURE: wp_user ' . $wp_user_id . ' não possui avatar.');
      }

      if (!empty($summary)) {

         $update_description_result = wp_update_term($author_id, 'author_tax', [
            'description' =>  wp_filter_nohtml_kses($summary),
         ]);

         if (!is_wp_error($update_description_result)) {

            $this->print_alert('SUCCESS: Autor (author_tax) ' . $author_id . ' recebeu descrição com sucesso.');
         } else {

            $this->print_alert('FAILURE: Autor (author_tax) ' . $author_id . ' teve erro ao receber descrição.');
         }
      } else {

         $this->print_alert('FAILURE: wp_user ' . $wp_user_id . ' não possui descrição.');
      }
   }

   private function process_featured_image(int $post_id)
   {
      $media_entity = get_field('penton_link_media_feat_img', $post_id);

      $image_file    = '';
      $attachment_id = '';

      if (!empty($media_entity)) {

         $image_file = get_field('penton_media_image', $media_entity->ID);

         if (empty($image_file)) {

            $this->print_alert('FAILURE: Post ' . $post_id . ' Não tem imagem destacada.');
            return;
         } else {

            if (isset($image_file['ID'])) {
               $attachment_id = $image_file['ID'];
            } elseif (isset($image_file)) {
               $attachment_id = $image_file;
            }
         }
      } else {

         $this->print_alert('FAILURE: Post ' . $post_id . ' Não tem imagem destacada.');
         return;
      }

      if ($this->is_simulation === true) {

         if (empty($attachment_id)) {

            $this->print_alert('FAILURE: Post ' . $post_id . ' Não tem imagem destacada.');
         } else {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' tem imagem destacada.');
         }
      } else {

         if (!empty($attachment_id)) {

            delete_post_thumbnail($post_id);

            $result_setting_thumbnail = set_post_thumbnail($post_id, $attachment_id);

            if ($result_setting_thumbnail != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu imagem destacada.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve erro ao receber imagem destacada.');
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' Não tem imagem destacada.');
         }
      }
   }

   private function process_post_authors(int $post_id)
   {
      $authors = get_field('penton_author', $post_id);

      if ($this->is_simulation === true) {

         if (!empty($authors)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui autores.');
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui autores.');
         }
      } else {

         if (!empty($authors)) {

            foreach ($authors as $author) {

               $terms = get_terms('author_tax', [
                  'hide_empty' => false,
                  'meta_key'   => 'attached_wp_user_id',
                  'meta_value' => $author['ID']
               ]);

               if (!empty($terms)) {

                  foreach ($terms as $term) {

                     $result_setting_term = wp_set_post_terms($post_id, [$term->term_id], 'author_tax', true);

                     if (!is_wp_error($result_setting_term) && $result_setting_term !== false) {

                        $this->print_alert('SUCCESS: Autor term_id ' . $term->term_id . ' inserido com sucesso no Post ' . $post_id) . '.';
                     } else {

                        $this->print_alert('FAILURE: Autor term_id ' . $term->term_id . ' não pôde ser inserido no Post ' . $post_id) . '.';
                     }
                  }
               } else {

                  $this->print_alert('FAILURE: Post ' . $post_id . ' não possui autores (wp user) com autores válidos (author_tax).');
               }
            }
         } else {

            $this->print_alert('FAILURE: Post ' . $post_id . ' não possui autores (wp user).');
         }
      }
   }

   private function attach_default_category(string $category_slug, int $post_id)
   {
      $category = get_term_by('slug', $category_slug, 'category');

      if ($this->is_simulation === true) {

         $this->print_alert('INFO: A categoria ' . $category_slug . ' pode ser inserida no Post ' . $post_id . '.');
      } else {

         if (!empty($category)) {

            $existing_categories = (array) wp_get_post_categories($post_id, ['fields' => 'ids']);
            $new_categories      = array_unique(array_merge($existing_categories, [$category->term_id]));

            $result_setting_categories = wp_set_post_categories($post_id, $new_categories, true);

            if (!is_wp_error($result_setting_categories) && $result_setting_categories !== false) {

               $this->print_alert('SUCCCESS: A categoria ' . $category_slug . ' foi inserida com sucesso no Post ' . $post_id . '.');
            } else {

               $this->print_alert('FAILURE: A categoria ' . $category_slug . ' não pôde ser inserida no Post ' . $post_id . '.');
            }
         }
      }
   }

   private function delete_unwanted_users()
   {
      $users_query = new \WP_User_Query([
         'role__in' => ['subscriber'],
      ]);

      $users_to_delete = $users_query->get_results();

      if (empty($users_to_delete)) {

         $this->print_alert('INFO: Não há usuários elegíveis para serem deletados.');
      } else {

         foreach ($users_to_delete as $user) {

            if ($this->is_simulation === true) {

               $this->print_alert('INFO: User ' . $user->ID . ' pode ser deletado.');
            } else {

               $result_deleting_user = \wp_delete_user($user->ID);

               if ($result_deleting_user === true) {

                  $this->print_alert('SUCCESS: User ' . $user->ID . ' deletado.');
               } else {

                  $this->print_alert('FAILURE: User ' . $user->ID . ' não pôde ser deletado.');
               }
            }
         }
      }
   }

   private function process_related_posts($post_id)
   {
      $related_posts = get_field('penton_inline_related', $post_id);

      $related_posts_ids = [];

      if (!empty($related_posts)) {

         $related_posts_ids = array_column((array) $related_posts, 'ID');
      }

      if ($this->is_simulation === true) {

         if (!empty($related_posts_ids)) {

            $this->print_alert('SUCCESS: Post ' . $post_id . ' possui posts relacionados.');
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui posts relacionados.');
         }
      } else {

         if (!empty($related_posts_ids)) {

            delete_post_meta($post_id, 'related_posts');

            $result_updating_post_related_posts  = update_post_meta($post_id, 'related_posts', $related_posts_ids);

            if ($result_updating_post_related_posts != false) {

               $this->print_alert('SUCCESS: Post ' . $post_id . ' recebeu posts relacionados com sucesso.');
            } else {

               $this->print_alert('FAILURE: Post ' . $post_id . ' teve um erro ao receber posts relacionados.');
            }
         } else {

            $this->print_alert('INFO: Post ' . $post_id . ' não possui posts relacionados.');
         }
      }
   }

   private function delete_unwanted_metadata()
   {
      global $wpdb;

      $count_post_meta = (int) $wpdb->get_var("
      SELECT COUNT(*)
      FROM {$wpdb->postmeta}
      WHERE meta_key LIKE '%penton_%'
  ");

      $count_term_meta = (int) $wpdb->get_var("
      SELECT COUNT(*)
      FROM {$wpdb->termmeta}
      WHERE meta_key LIKE '%penton_%'
  ");

      if ($this->is_simulation === true) {

         $this->print_alert('INFO: ' . $count_post_meta . ' post_metas podem ser deletados.');

         $this->print_alert('INFO: ' . $count_term_meta . ' term_metas podem ser deletados.');
      } else {

         $result_delete_post_meta =  $wpdb->query("
             DELETE FROM {$wpdb->postmeta}
             WHERE meta_key LIKE '%penton_%'
         ");

         $result_delete_term_meta  = $wpdb->query("
             DELETE FROM {$wpdb->termmeta}
             WHERE meta_key LIKE '%penton_%'
         ");

         if ($result_delete_post_meta === false) {

            $this->print_alert('FAILURE: Falha ao deletar post_metas.');
         } else {

            $this->print_alert('INFO: ' . $result_delete_post_meta . ' post_metas foram deletados.');
         }

         if ($result_delete_term_meta === false) {

            $this->print_alert('FAILURE: Falha ao deletar term_metas.');
         } else {

            $this->print_alert('INFO: ' . $result_delete_term_meta . ' term_metas foram deletados.');
         }
      }
   }

   private function extract_url_from_iframe(string $html)
   {
      $pattern = '/<iframe.*?src=[\'"](.*?)[\'"].*?<\/iframe>/i';

      preg_match($pattern, $html, $matches);

      if (isset($matches[1])) {
         return $matches[1];
      } else {
         return null;
      }
   }

   private function extract_video_url(string $content)
   {
      $providers = [
         'youtube' => [
            'pattern'  => '/(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/',
            'base_url' => 'https://www.youtube.com/watch?v='
         ],
         'vimeo' => [
            'pattern'  => '/(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/\n\s]+\/\S+\/)|)(\d+))/',
            'base_url' => 'https://vimeo.com/'
         ]
      ];

      foreach ($providers as $provider => $data) {
         if (preg_match($data['pattern'], $content, $matches)) {
            return $data['base_url'] . $matches[1];
         }
      }

      return null;
   }

   private function regenerate_thumbnails()
   {
      if ($this->is_simulation === true) {
         return;
      }

      $attachments = get_posts([
         'post_type'      => 'attachment',
         'posts_per_page' => -1,
         'post_mime_type' => 'image',
      ]);

      foreach ($attachments as $attachment) {

         $regenerated_metadata = wp_generate_attachment_metadata($attachment->ID, get_attached_file($attachment->ID));

         if ($regenerated_metadata !== false && is_array($regenerated_metadata) && !is_wp_error($regenerated_metadata)) {

            $this->print_alert('SUCCESS: Imagem ' . $attachment->ID . ' teve os thumbnails regenerados com sucesso.');
         } else {

            $this->print_alert('FAILURE: Imagem ' . $attachment->ID . ' teve um problema ao processar thumbnails.');
         }
      }
   }
}
