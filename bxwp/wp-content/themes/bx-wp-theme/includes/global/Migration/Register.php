<?php

namespace CanaisDigitais\Migration;

if (!defined('ABSPATH')) exit;

class Register
{
   private $current_options_page_url;

   function __construct()
   {
      $this->current_options_page_url = admin_url('tools.php?') . http_build_query($_GET);

      add_action('admin_init', [$this, 'register_page']);
      add_action('admin_menu', [$this, 'admin_page_options']);
      add_action('save_post', [$this, 'add_category_on_post_create']);
   }

   public function page_options_content()
   {

      if (isset($_POST['save_category_slug'])) {
         $this->set_category_options();
      } elseif (isset($_POST['execute_migration'], $_POST['migration_mode'], $_POST['simulation'])) {
         $this->execute_migration_task(
            filter_var($_POST['simulation'], FILTER_VALIDATE_BOOLEAN),
            esc_attr($_POST['migration_mode'])
         );
      }

?>
      <div class="wrap">
         <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
         <?php

         $this->render_add_category_fields();
         $this->render_migration_fields();

         ?>
      </div>
   <?php
   }

   public function register_page()
   {
      register_setting('cd_migration', 'cd_migration_options');

      $this->add_settings_section('Anexar Categoria', 'add_category_option_description', 'category');
      $this->add_settings_field('category_slug', 'Slug da Categoria', 'category_slug_field', 'category');

      $this->add_settings_section('Migração', 'main_description', 'migration');
      $this->add_settings_field('simulation_mode', 'Simular', 'simulation_field', 'migration');
      $this->add_settings_field('migration_mode', 'Opções', 'execute_field', 'migration');
   }

   private function add_settings_section($title, $callback, $section)
   {
      add_settings_section(
         'cd_migration_' . $section . '_options_section',
         esc_html__($title, 'canais_digitais'),
         [$this, $callback],
         'cd_migration_' . $section
      );
   }

   private function add_settings_field($id, $label, $callback, $section)
   {
      add_settings_field(
         $id,
         $label,
         [$this, $callback],
         'cd_migration_' . $section,
         'cd_migration_' . $section . '_options_section',
         ['label_for' => $id]
      );
   }

   public function simulation_field()
   {
      $this->render_radio_fields(
         [
            [
               'id'      => 'simulation_true',
               'name'    => 'simulation',
               'value'   => 1,
               'label'   => esc_html__('Sim', 'canais_digitais'),
               'checked' => true
            ],
            [
               'id'    => 'simulation_false',
               'name'  => 'simulation',
               'value' => 0,
               'label' => esc_html__('Não', 'canais_digitais')
            ]
         ]
      );
   }

   public function execute_field()
   {
      $this->render_radio_fields([
         [
            'id'    => 'pre_post_fields',
            'name'  => 'migration_mode',
            'value' => 'pre_posts',
            'label' => esc_html__('1 - Migrar autores, categorias (campos e termos)', 'canais_digitais')
         ],
         [
            'id'    => 'only_posts_fields',
            'name'  => 'migration_mode',
            'value' => 'only_posts',
            'label' => esc_html__('2 - Migrar posts (campos e termos)', 'canais_digitais')
         ],
         [
            'id'    => 'fix_authors_fields',
            'name'  => 'migration_mode',
            'value' => 'fix_authors',
            'label' => esc_html__('3 - Consertar autores', 'canais_digitais')
         ]
      ]);
   }

   private function render_radio_fields($fields)
   {
      echo '<fieldset>';

      foreach ($fields as $field) {

         $checked = (isset($field['checked'])) ? 'checked' : '';
         echo '<input type="radio"  id="' . $field['id'] . '" name="' . $field['name'] . '" value="' . $field['value'] . '" ' . $checked . '>';
         echo '<label for="' . $field['id'] . '">' . $field['label'] . '</label><br>';
      }

      echo '</fieldset>';
   }

   private function render_add_category_fields()
   {

   ?>
      <form action="<?php echo esc_url($this->current_options_page_url); ?>" method="post">
         <?php

         settings_fields('cd_migration');
         do_settings_sections('cd_migration_category');

         submit_button(esc_html__('Salvar'), 'primary', 'save_category_slug');

         ?>

      </form>
   <?php
   }

   private function render_migration_fields()
   {

   ?>
      <form action="<?php echo esc_url($this->current_options_page_url); ?>" method="post">
         <?php

         settings_fields('cd_migration');
         do_settings_sections('cd_migration_migration');

         submit_button(esc_html__('Executar Migração'), 'primary', 'execute_migration');

         ?>
      </form>
   <?php

   }

   public function execute_migration_task(bool $is_simulation, string $mode)
   {
      $Migration = new Migration($mode, $is_simulation);
      $Migration->run();
   }

   public function main_description()
   {
   ?>
      <p>
         <?php esc_html_e('Operações necessárias após migração do Drupal.', 'canais_digitais'); ?>
      </p>
   <?php

   }

   public function add_category_option_description()
   {
   ?>
      <p>
         <?php esc_html_e('Categoria para ser colocada em cada post importado pelos plugins do Drupal.', 'canais_digitais'); ?>
      </p>
<?php

   }

   public function admin_page_options()
   {
      add_management_page(
         'Migração',
         'Migração',
         'manage_options',
         'migration',
         [$this, 'page_options_content']
      );
   }

   public function set_category_options()
   {
      if (false === get_option('migration_add_category_on_post_create')) {
         add_option('migration_add_category_on_post_create', '');
      }

      if (isset($_POST['category_slug'])) {
         update_option('migration_add_category_on_post_create', $_POST['category_slug']);
      }
   }

   public function category_slug_field()
   {
      $category_slug = get_option('migration_add_category_on_post_create');
      echo '<input type="text" id="category_slug" name="category_slug" value="' . esc_attr($category_slug) . '">';
   }

   public function add_category_on_post_create($post_id)
   {
      if (get_option('migration_add_category_on_post_create')) {
         $category_slug = get_option('migration_add_category_on_post_create');

         $category = get_category_by_slug($category_slug);

         if ($category) {
            $category_id = $category->term_id;

            if (!wp_is_post_revision($post_id) && !wp_is_post_autosave($post_id)) {
               if (!has_term($category_id, 'category', $post_id)) {
                  wp_set_post_categories($post_id, [$category_id], true);
               }
            }
         }
      }
   }
}
