changeLoginState(false)

function irisProfileMgrCallback(obj) {
   if (obj.user) {
      changeLoginState(true, obj.user)
      loadProfileData(obj.user)
   }
}

function irisLoginCallback(obj) {
   if (obj.user) {
      changeLoginState(true, obj.user)
   }
}

function irisRegisterCallback(obj) {
   if (obj.user) {
      changeLoginState(true, obj.user)
   }
}

function irisLogoutCallback() {
   changeLoginState(false)
}

function changeLoginState(state, user) {
   const user_logged_in = state
   const cta_download_single = document.querySelector('.cta-download-single')

   if (!user_logged_in) {
      document.querySelectorAll('.profile-button').forEach((element) => {
         element.classList.add('disabled')
      })
      document.querySelectorAll('.login-button').forEach((element) => {
         element.classList.remove('disabled')
      })
   } else {
      document.querySelectorAll('.login-button').forEach((element) => {
         element.classList.add('disabled')
      })
      document.querySelectorAll('.profile-button').forEach((element) => {
         element.classList.remove('disabled')
      })
   }

   if (user) {
      const name = user.first_name + user.last_name
      const initials = getInitials(name)

      document.querySelectorAll('[js-profile-name]').forEach((element) => {
         element.innerHTML = name
      })

      document.querySelectorAll('[js-profile-initials]').forEach((element) => {
         element.innerHTML = initials
      })
   }
}

function loadProfileData(user) {
   const profileForm = document.querySelector('#profile-form')
   const authToken = getIIRISCookie('IRIS_AUTH')

   if (!profileForm || authToken == '') {
      return
   }

   profileForm.querySelector('[name="first_name"]').value = user['first_name']
   profileForm.querySelector('[name="last_name"]').value = user['last_name']
   profileForm.querySelector('[name="business_email"]').value = user['business_email']
   profileForm.querySelector('[name="state"]').value = user['state']
   profileForm.querySelector('[name="company"]').value = user['company']
   profileForm.querySelector('[name="job_title"]').value = user['job_title']
   profileForm.querySelector('[name="business_type"]').value = user['business_type']
   profileForm.querySelector('[name="job_function"]').value = user['job_function']
}

function editProfileData() {
   const profileForm = document.querySelector('#profile-form')
   const authToken = getIIRISCookie('IRIS_AUTH')

   if (!profileForm || authToken == '') {
      return
   }

   const user = {}
   user.first_name = profileForm.querySelector('[name="first_name"]').value
   user.last_name = profileForm.querySelector('[name="last_name"]').value
   user.business_email = profileForm.querySelector('[name="business_email"]').value
   user.state = profileForm.querySelector('[name="state"]').value
   user.company = profileForm.querySelector('[name="company"]').value
   user.job_title = profileForm.querySelector('[name="job_title"]').value
   user.business_type = profileForm.querySelector('[name="business_type"]').value
   user.job_function = profileForm.querySelector('[name="job_function"]').value

   IIRISProfileMgr.setIIRISProfile(user)
      .then(function (response) {
         console.log(response)
      })
      .catch((e) => {
         console.log(e)
      })
}

function getInitials(name) {
   var initials
   var nameSplit = name.split(' ')
   var nameLength = nameSplit.length
   if (nameLength > 1) {
      initials = nameSplit[0].substring(0, 1) + nameSplit[nameLength - 1].substring(0, 1)
   } else if (nameLength === 1) {
      initials = nameSplit[0].substring(0, 1)
   } else return

   return initials.toUpperCase()
}

export function getIIRISCookie(cname) {
   let name = cname + '='
   let decodedCookie = decodeURIComponent(document.cookie)
   let ca = decodedCookie.split(';')
   for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) == ' ') {
         c = c.substring(1)
      }
      if (c.indexOf(name) == 0) {
         return c.substring(name.length, c.length)
      }
   }
   return ''
}

document.querySelectorAll('.login-button').forEach((button) => {
   button.addEventListener('click', () => {
      document.getElementById('irisLoginBtn').click()
   })
})

// document.querySelectorAll('.register-button').forEach((button) => {
//    button.addEventListener('click', () => {
//       document.getElementById('irisRegisterBtn').click()
//    })
// })

document.querySelectorAll('.logout-button').forEach((button) => {
   button.addEventListener('click', () => {
      document.getElementById('irisLogoutBtn').click()
   })
})

if (document.querySelector('[js-profile-edit]')) {
   document.querySelector('[js-profile-edit]').addEventListener('click', function (e) {
      e.preventDefault()
      this.classList.add('remove')
      document.querySelector('#profile-form').classList.add('edit')
   })
}

if (document.querySelector('[js-profile-save]')) {
   document.querySelector('[js-profile-save]').addEventListener('click', function (e) {
      e.preventDefault()
      document.querySelector('#profile-form').classList.remove('edit')
      document.querySelector('[js-profile-edit]').classList.remove('remove')
      editProfileData()
   })
}
