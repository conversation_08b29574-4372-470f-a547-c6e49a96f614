document.addEventListener('DOMContentLoaded', () => {
   const menuParents = document.querySelectorAll(
      'nav.main-menu .menu-item-has-children'
   )

   if (menuParents.length === 0) {
      return
   }
   const separatorIndexes = [7, 14]

   menuParents.forEach((menuParent) => {
      const menuItems = menuParent.querySelectorAll('.menu-item')

      separatorIndexes.forEach((index) => {
         if (menuItems.length > index) {
            for (let i = index - 7; i < index; i++) {
               menuItems[i].classList.add('lg:menu-item-with-separator')
            }
         }
      })
   })
})

const showSearch = document.querySelector('#show-search')
if (showSearch) {
   showSearch.addEventListener('click', function () {
      document.querySelector('#menu-mobile').classList.remove('active')

      if (document.querySelector('#show-menu-mobile')) {
         document.querySelector('#show-menu-mobile').classList.remove('active')
      }
   })
}

const showMenuMobile = document.querySelector('#show-menu-mobile')
if (showMenuMobile) {
   showMenuMobile.addEventListener('click', function () {
      this.classList.toggle('active')

      document.querySelector('#menu-mobile').classList.toggle('active')
      document.body.classList.toggle('overflow-y-hidden')
   })
}

const menuItemsWithChildren = document.querySelectorAll(
   '#menu-mobile .menu-item-has-children .js-open-submenu'
)
if (menuItemsWithChildren.length) {
   menuItemsWithChildren.forEach(function (item) {
      item.addEventListener('click', function (e) {
         e.preventDefault()

         const subMenu = this.parentElement.querySelector('.sub-menu')
         const isOpen = subMenu.classList.contains('active')

         document
            .querySelectorAll('#menu-mobile .menu-item-has-children .active')
            .forEach(function (item) {
               item.classList.remove('active')
            })

         if (!isOpen) {
            subMenu.classList.add('active')
            this.classList.add('active')
         }
      })
   })
}
