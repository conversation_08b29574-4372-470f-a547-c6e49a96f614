class Loading_Spinner {
    constructor(spinnerSelector) {
      this.spinnerElement = document.querySelector(spinnerSelector);
      if (!this.spinnerElement) {
        throw new Error(`Spinner element not found with selector: ${spinnerSelector}`);
      }
    }
  
    show() {
      this.spinnerElement.classList.remove("hidden");
    }
  
    hide() {
      this.spinnerElement.classList.add("hidden");
    }
  } export default Loading_Spinner