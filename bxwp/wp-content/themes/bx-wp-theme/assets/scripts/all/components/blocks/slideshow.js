import Swiper from 'swiper/bundle'

document.addEventListener('DOMContentLoaded', () => {
   const blockSlideshowSliderParams = {
      slidesPerView: 'auto',
      autoplay: false,
      navigation: {
         nextEl: '.block-slider-button-next',
         prevEl: '.block-slider-button-prev',
      },
      speed: 1000,
   }

   const toggleFullscreen = (elementWrapper) => {
      elementWrapper.classList.toggle('is_fullscreen')
      document.body.classList.toggle('has_slideshow_fullscreen')
   }

   const slideshow_init = (slider, params) => {
      new Swiper(slider, params)

      const elementWrapper = slider.closest('.block-slider-slideshow-wrapper')
      const fullscreenToggleButton = slider.querySelector('.fullscreen-toggle')
      const closeButton = elementWrapper.querySelector('.close-button')

      closeButton.addEventListener('click', (event) => {
         event.preventDefault()
         toggleFullscreen(elementWrapper)
      })

      fullscreenToggleButton.addEventListener('click', (event) => {
         event.preventDefault()
         toggleFullscreen(elementWrapper)
      })
   }

   const blockSlideshowSliderElements = document.querySelectorAll('.block-slider-slideshow')
   Array.from(blockSlideshowSliderElements).forEach((slider) => slideshow_init(slider, blockSlideshowSliderParams))
})
