async function handleFileDownload(fileId) {
   try {
      const response = await fetch(ajax_object.ajax_url, {
         method: 'POST',
         headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-WP-Nonce': ajax_object.nonce,
         },
         body: `action=secure_file_download&file_id=${encodeURIComponent(fileId)}`,
      })

      if (!response.ok) {
         showToast(messages.download_error, false)
      }

      const blob = await response.blob()
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = ''

      if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
         const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
         const matches = filenameRegex.exec(contentDisposition)
         if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, '')
         }
      }

      const downloadUrl = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = filename
      document.body.appendChild(a)
      a.click()
      URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
   } catch (error) {
      showToast(messages.download_error, false)
   }
}
export default handleFileDownload
