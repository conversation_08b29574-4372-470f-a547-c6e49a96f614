function cookieExists(cname) {
   const cookies = document.cookie.split(';')
   for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim()
      if (cookie.startsWith(cname + '=')) {
         return true
      }
   }
   return false
}

function setCookie(cname, cvalue, exdays) {
   const d = new Date()
   d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000)
   const expires = 'expires=' + d.toUTCString()
   document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/'
}

function deleteCookie(name) {
   document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;'
}

export {cookieExists, setCookie, deleteCookie}
