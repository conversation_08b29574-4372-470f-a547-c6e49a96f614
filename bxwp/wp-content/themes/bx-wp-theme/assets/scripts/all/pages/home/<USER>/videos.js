import Swiper from 'swiper/bundle'

document.addEventListener('DOMContentLoaded', () => {
   const homepageVideosSliderParams = {
      loop: true,
      slidesPerView: 1.5,
      spaceBetween: 12,
      autoplay: false,
      speed: 1000,
      breakpoints: {
         1024: {
            slidesPerView: 4,
         },
      },
   }

   const homepageSpecialistsSliderElement = document.querySelector('.slider-homepage-videos')

   if (homepageSpecialistsSliderElement) {
      const swiper = new Swiper('.slider-homepage-videos', homepageVideosSliderParams)
   }
})
