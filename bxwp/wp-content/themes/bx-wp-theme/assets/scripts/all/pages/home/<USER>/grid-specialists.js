import Swiper from 'swiper/bundle'

document.addEventListener('DOMContentLoaded', () => {
   const homepageSpecialistsSliderParams = {
      slidesPerView: 1,
      spaceBetween: 16,
      autoplay: false,
      navigation: {
         nextEl: '.slider-button-next',
         prevEl: '.slider-button-prev',
      },
      speed: 1000,
      breakpoints: {
         1024: {
            slidesPerView: 2,
         },
      },
   }

   const homepageSpecialistsSliderElement = document.querySelector('.slider-home-specialists')

   if (homepageSpecialistsSliderElement) {
      const swiper = new Swiper('.slider-home-specialists', homepageSpecialistsSliderParams)
   }
})
