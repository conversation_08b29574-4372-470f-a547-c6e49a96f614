import Loading_Spinner from '../../../components/loading_spinner'
import handleFileDownload from '../../../utils/handle-download'

document.addEventListener('DOMContentLoaded', () => {
   const loadingSpinnerSelector = '.loading-spinner'

   const spinnerElement = document.querySelector(loadingSpinnerSelector)

   if (!spinnerElement) {
      return
   }

   let spinner

   spinner = new Loading_Spinner(loadingSpinnerSelector)

   const downloadButtons = document.querySelectorAll('.download-button-link')

   if (downloadButtons) {
      downloadButtons.forEach((button) => {
         button.addEventListener('click', async (e) => {
            e.preventDefault()
            const fileId = button.getAttribute('data-file-id')

            if (!fileId) {
               console.error('ID do arquivo não encontrado.')
               showToast(messages.download_error, false)
               return
            }

            spinner.show()

            try {
               await handleFileDownload(fileId)
               spinner.hide()
            } catch (error) {
               showToast(messages.download_error, false)
            }

            spinner.hide()
         })
      })
   }
})
