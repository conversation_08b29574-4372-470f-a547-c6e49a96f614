document.addEventListener('DOMContentLoaded', () => {
   if (!document.body.classList.contains('search')) {
      return
   }

   let changeTimer
   const searchForm = document.querySelector('#search_form')
   const formSelects = document.querySelectorAll('#search_form select')

   formSelects.forEach((formSelect) => {
      formSelect.addEventListener('input', () => {
         clearTimeout(changeTimer)

         changeTimer = setTimeout(() => {
            searchForm.submit()
         }, 1000)
      })
   })
})
