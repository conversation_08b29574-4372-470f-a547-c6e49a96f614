import Swiper from 'swiper/bundle'

document.addEventListener('DOMContentLoaded', () => {
   const homepageEventsSliderParams = {
      slidesPerView: 1,
      autoplay: false,
      navigation: {
         nextEl: '.slider-button-next',
         prevEl: '.slider-button-prev',
      },
      pagination: {
         el: '.slider-pagination',
         clickable: true,
      },
      speed: 1000,
   }

   const homepageEventsSliderElement = document.querySelector('.slider-home-events')

   if (homepageEventsSliderElement) {
      const swiper = new Swiper('.slider-home-events', homepageEventsSliderParams)
   }
})
