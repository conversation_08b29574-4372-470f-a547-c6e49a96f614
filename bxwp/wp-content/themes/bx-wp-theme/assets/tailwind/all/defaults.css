body.has_slideshow_fullscreen {
   @apply overflow-hidden w-screen h-screen;
}

body.is-scroll .hide-on-scroll {
   @apply opacity-0 fixed;
}

body.is-scroll .justify-start-on-scroll {
   @apply justify-start;
}

[x-cloak] {
   @apply !hidden;
}

.default-link-underline {
   @apply hover:underline underline-offset-2;
}

input[type='checkbox'] {
   @apply w-6 h-6 border-neutral-300 rounded-lg focus:ring-neutral-300 focus:ring-2;
}

textarea {
   @apply block border border-neutral-400 w-full max-h-14 py-2 px-4 rounded outline-none focus:border-neutral-700;
}

select,
select[multiple] {
   @apply w-full h-14 p-4 mt-4 rounded border border-neutral-400 bg-transparent bg-no-repeat bg-[length:1rem_1rem] outline-none cursor-pointer text-neutral-400 appearance-none focus:border-neutral-400 focus:ring-neutral-700 focus:text-neutral-700;
}

select {
   background-position-y: 50%;
   background-position-x: calc(100% - 0.9rem);
   -webkit-print-color-adjust: exact;
   color-adjust: exact;
   print-color-adjust: exact;
   background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.4601 5.54016L3.46009 5.54016C3.33814 5.41821 3.17274 5.34969 3.00028 5.34969C2.82781 5.34969 2.66241 5.41821 2.54046 5.54016C2.41851 5.66211 2.35 5.82751 2.35 5.99997C2.35 6.17244 2.41851 6.33784 2.54046 6.45979L7.5404 11.4597C7.54042 11.4597 7.54044 11.4598 7.54046 11.4598C7.60082 11.5202 7.67248 11.5681 7.75137 11.6008C7.83028 11.6335 7.91486 11.6504 8.00028 11.6504C8.0857 11.6504 8.17028 11.6335 8.24919 11.6008C8.32807 11.5681 8.39974 11.5202 8.46009 11.4598C8.46011 11.4598 8.46013 11.4597 8.46015 11.4597L13.4601 6.45979C13.5205 6.3994 13.5684 6.32772 13.6011 6.24882C13.6337 6.16993 13.6506 6.08537 13.6506 5.99997C13.6506 5.91458 13.6337 5.83002 13.6011 5.75112C13.5684 5.67223 13.5205 5.60054 13.4601 5.54016C13.3997 5.47977 13.328 5.43187 13.2491 5.39919C13.1702 5.36651 13.0857 5.34969 13.0003 5.34969C12.9149 5.34969 12.8303 5.36651 12.7514 5.39919C12.6725 5.43187 12.6008 5.47977 12.5405 5.54016L12.5405 5.54016L8.00028 10.081L3.4601 5.54016Z' fill='%233D3D3D' stroke='%233D3D3D' stroke-width='0.3'/%3E%3C/svg%3E%0A");
}

select[multiple] {
   @apply min-h-22;
}

input[type='radio'] {
   @apply w-5 h-5 border border-neutral-400 rounded-lg checked:border-green-300 checked:bg-green-700 focus:!border-green-300 focus:!bg-green-700 checked:focus:!border-green-300 checked:focus:!bg-green-700 checked:hover:!border-green-300 checked:hover:!bg-green-700 disabled:bg-neutral-300;
}
