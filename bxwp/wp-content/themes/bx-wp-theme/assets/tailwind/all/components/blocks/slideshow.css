.block-slider-slideshow-wrapper {
   @apply lg:w-full w-[90vw];
}

.block-slider-slideshow-wrapper.is_fullscreen {
   @apply w-full;
}

.block-editor .block-slider-slideshow-wrapper .swiper-wrapper {
   @apply overflow-hidden;
}

.block-editor .block-slider-slideshow-wrapper .block-slider-controls {
   @apply hidden;
}

.block-slider-slideshow-wrapper .swiper-button-disabled {
   @apply opacity-[.35] pointer-events-none cursor-auto;
}

.block-slider-slideshow-wrapper .swiper-slide {
   @apply w-full;
}
.block-slider-slideshow-wrapper .block-slider-controls {
   @apply absolute w-full aspect-[375/210] mt-12 flex flex-col lg:aspect-[656/368];
}
.block-slider-slideshow-wrapper .close-button-wrapper {
   @apply max-w-[69rem] mx-auto mb-6;
}

.block-slider-slideshow {
   @apply swiper w-full max-w-3xl mx-auto;
}

.block-slideshow-image {
   @apply w-full h-full aspect-[375/210] object-cover lg:aspect-[656/368];
}

.block-slider-slideshow-wrapper.is_fullscreen .block-slideshow-image {
   @apply object-contain;
}

.block-slider-slideshow-wrapper.is_fullscreen {
   @apply mx-0 z-[90] w-full h-screen fixed top-0 left-0 bg-black;
}
.block-slider-slideshow-wrapper.is_fullscreen .slide-text-style {
   @apply text-white;
}

.block-slider-slideshow-wrapper.is_fullscreen .block-slider-controls {
   @apply mt-0;
}

.block-slider-slideshow-wrapper.is_fullscreen .block-slider-slideshow {
   @apply max-w-[1104px];
}

.block-slider-slideshow-wrapper.is_fullscreen .pagination-bg-style {
   @apply bg-neutral-700 text-white;
}

.slide-description {
   @apply line-clamp-[12] !text-base !leading-6;
}

.block-slider-slideshow-wrapper.is_fullscreen .slide-description {
   @apply pr-2.5 !h-full !max-h-[20vh] !overflow-auto;
}

.block-slider-slideshow-wrapper.is_fullscreen .slide-description p {
   @apply !text-white !text-base !line-clamp-none;
}

.block-slider-slideshow-wrapper.is_fullscreen .slide-image {
   @apply order-first rounded-lg overflow-hidden h-[60vh] m-4;
}

.block-slider-slideshow-wrapper.is_fullscreen .slide-title,
.block-slider-slideshow-wrapper.is_fullscreen .slide-description {
   @apply max-w-[95vw] m-auto;
}

.block-slider-slideshow-wrapper.is_fullscreen .block-slider-arrows {
   @apply mt-[27vh];
}

.block-slider-slideshow-wrapper.is_fullscreen .close-button-wrapper {
   @apply flex absolute top-4 right-4 z-10;
}

.block-slider-slideshow-wrapper.is_fullscreen .fullscreen-toggle-wrapper {
   @apply hidden;
}
