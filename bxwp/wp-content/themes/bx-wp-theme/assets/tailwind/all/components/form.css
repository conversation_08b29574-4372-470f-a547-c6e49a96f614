.bx-form-col-white {
   @apply grid grid-cols-1 auto-rows-auto gap-6 lg:grid-cols-2 lg:gap-x-4 lg:gap-y-6 p-8 md:py-8 md:px-24 my-6 bg-white;
}

.bx-form-col-white p {
   @apply flex flex-col;
}

.bx-form-title {
   @apply flex items-center gap-2 text-2xl text-primary-300;
}

.bx-form-icon {
   @apply before:content-[''] before:block before:w-[1.563rem] before:h-[1.563rem];
}

.bx-form-icon.people {
   @apply before:icon-[ph-user-circle];
}

.bx-form-icon.business {
   @apply before:icon-[ph-briefcase];
}

.bx-form-icon.star {
   @apply before:icon-[ph-star];
}

.wpcf7-form input[type='text'],
.wpcf7-form input[type='email'],
.wpcf7-form input[type='tel'],
.wpcf7-form input[type='url'],
.wpcf7-form input[type='date'] {
   @apply block bg-transparent border border-neutral-400 w-full h-14 py-2 px-4 rounded outline-none text-neutral-700 focus:border-neutral-700 placeholder:text-neutral-400 focus:placeholder:text-neutral-700 focus:border-neutral-400 focus:ring-neutral-700 focus:text-neutral-700;
}

.wpcf7-checkbox,
.wpcf7-radio {
   @apply flex flex-row flex-wrap gap-4 mt-4;
}

.wpcf7-acceptance [type='checkbox']:checked,
.wpcf7-checkbox [type='checkbox']:checked {
   @apply !bg-primary-300;
}

.wpcf7-checkbox {
   @apply my-6;
}

.wpcf7-form .required-input {
   @apply ml-[0.125rem] text-base text-red-600;
}
.wpcf7-form br {
   @apply hidden;
}

.wpcf7-form p label {
   @apply w-full text-left text-neutral-700 text-base leading-normal;
}

.wpcf7-checkbox.bx-form-multi-selector .wpcf7-list-item-label {
   @apply inline-flex items-center gap-1.5 p-2.5 border border-solid border-neutral-700 cursor-pointer rounded-full;
}

.wpcf7-checkbox.bx-form-multi-selector .wpcf7-list-item-label:before {
   @apply w-[15px] h-[15px] bg-neutral-700 icon-[ph-plus];
}

.wpcf7-checkbox.bx-form-multi-selector input {
   @apply hidden;
}

.wpcf7-checkbox.bx-form-multi-selector input:checked + .wpcf7-list-item-label {
   @apply border-primary-300 text-primary-300;
}

.wpcf7-checkbox.bx-form-multi-selector input:checked + .wpcf7-list-item-label::before {
   @apply bg-primary-300 w-[15px] h-[15px] icon-[ph-check];
}

.bx-form-subtitle {
   @apply text-lg;
}

input.wpcf7-form-control:not([type='submit']) {
   @apply w-full mt-4;
}

.wpcf7-not-valid {
   @apply !border-red-600;
}

.wpcf7-not-valid-tip {
   @apply mt-2 text-sm;
}

.wpcf7-list-item {
   @apply m-0 flex gap-2 items-center;
}

.wpcf7-response-output {
   @apply hidden;
}

.wpcf7-form-control-wrap {
   @apply w-full flex flex-col;
}

.wpcf7-list-item label {
   @apply flex gap-3;
}

.form-full-width {
   @apply flex flex-col col-span-1 gap-6 w-full lg:col-span-2;
}

.form-full-width p,
.form-full-width label {
   @apply w-full;
}

.form-acceptance {
   @apply w-full flex flex-col gap-4 mt-6 lg:flex-row;
}

.form-acceptance a {
   @apply text-primary-500 font-bold;
}

.form-acceptance strong {
   @apply text-primary-500;
}

.form-acceptance p {
   @apply block text-neutral-600 text-base;
}

.form-acceptance p:first-child {
   @apply w-full lg:w-1/4;
}

.form-buttons {
   @apply mt-6 max-w-[26rem] mx-auto;
}

.form-buttons p {
   @apply w-auto flex flex-wrap justify-center gap-3 lg:gap-6;
}

.wpcf7-submit {
   @apply button-with-icon button-primary cursor-pointer;
}

.wpcf7-spinner {
   @apply block mt-2;
}

.form-acceptance a,
.wpcf7-acceptance a {
   @apply text-primary-500 font-bold underline;
}

.form-acceptance strong,
.wpcf7-acceptance strong {
   @apply text-primary-500;
}

.form-acceptance p,
.wpcf7-acceptance p {
   @apply block text-neutral-600 text-base;
}

.form-backtohome-button {
   @apply button button-secondary;
}
