.card-web-story a:after {
   @apply content-[''] w-full h-full absolute left-0 top-0;

   background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.4) 100%);
   box-shadow: 0px 6px 6px 0px rgba(102, 110, 122, 0.1);
}

.card-web-story:hover .excerpt,
.card-web-story:focus .excerpt {
   @apply max-h-[400px] opacity-100 transition-all duration-1000;
}

.card-web-story:hover img,
.card-web-story:focus img {
   @apply scale-125;
}
