.button {
   @apply inline px-5 py-3.5 text-white rounded text-xs font-medium uppercase leading-5 border border-transparent default-animation;
}

.button-with-icon {
   @apply inline-flex items-center gap-2 px-5 py-3.5 text-white rounded text-xs font-medium uppercase leading-5 border border-transparent default-animation;
}

.button-primary {
   @apply bg-primary-500;
}

.button-primary:hover {
   @apply bg-transparent border border-primary-500 text-primary-500;
}

.button-secondary {
   @apply border-primary-500 text-primary-500;
}

.icon-button {
   @apply w-11 h-11 p-3 flex justify-center items-center rounded-full border border-neutral-200 default-animation hover:bg-neutral-200;
}

.button-full-width {
   @apply flex justify-center items-center;
}

.icon-button-show-menu .icon-x {
   @apply hidden;
}

.icon-button-show-menu.active .icon-x {
   @apply block;
}

.icon-button-show-menu.active .icon-list {
   @apply hidden;
}
