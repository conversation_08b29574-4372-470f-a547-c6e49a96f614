[x-cloak] {
   display: none !important;
}

body.is-scroll .search-modal {
   @apply top-[6.125rem];
}

.search-animation {
   @apply fixed flex justify-center items-center z-[999] bg-[rgba(255,255,255,0.8)] inset-0;
}

.search-animation .lds-ellipsis {
   @apply inline-block relative w-20 h-20;
}

.search-animation .lds-ellipsis div {
   @apply absolute w-[13px] h-[13px] rounded-[50%] top-[33px] bg-primary-500;
   animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.search-animation .lds-ellipsis div:nth-child(1) {
   @apply animate-[lds-ellipsis1_0.6s_infinite] left-2;
}

.search-animation .lds-ellipsis div:nth-child(2) {
   @apply animate-[lds-ellipsis2_0.6s_infinite] left-2;
}

.search-animation .lds-ellipsis div:nth-child(3) {
   @apply animate-[lds-ellipsis2_0.6s_infinite] left-8;
}

.search-animation .lds-ellipsis div:nth-child(4) {
   @apply animate-[lds-ellipsis3_0.6s_infinite] left-14;
}

.search-animation.disabled {
   @apply invisible z-[-1] pointer-events-none;
}

@keyframes lds-ellipsis1 {
   0% {
      transform: scale(0);
   }
   100% {
      transform: scale(1);
   }
}

@keyframes lds-ellipsis3 {
   0% {
      transform: scale(1);
   }
   100% {
      transform: scale(0);
   }
}

@keyframes lds-ellipsis2 {
   0% {
      transform: translate(0, 0);
   }
   100% {
      transform: translate(24px, 0);
   }
}
