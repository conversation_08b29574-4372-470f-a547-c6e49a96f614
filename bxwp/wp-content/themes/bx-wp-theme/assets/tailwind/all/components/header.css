@layer utilities {
   .menu-item-with-separator {
      @apply after:content-[''] after:w-[1px] after:absolute after:top-0 after:right-0 after:h-full after:bg-neutral-200;
   }
}

body.is-scroll.admin-bar .header {
   @apply top-8;
}

.site_logo .logo_image {
   @apply max-w-[140px] lg:max-w-full w-auto max-h-[5.25rem] resize-animation;
}

nav.main-menu,
nav.top-menu {
   @apply relative;
}

nav.top-menu .sub-menu {
   @apply hidden;
}

nav.top-menu .menu-item,
nav.main-menu .menu-item {
   @apply text-neutral-700 font-medium text-xs uppercase leading-5 cursor-pointer;
}

nav.top-menu .menu-item {
   @apply grow-line-animation before:bg-neutral-700;
}

nav.main-menu .menu-item {
   @apply flex gap-4 h-full hover:bg-neutral-300 default-animation;
}

nav.main-menu .menu-item-has-children {
   @apply flex items-center gap-1;
}

nav.main-menu .menu-item-has-children > a:after {
   @apply content-[''] w-3 h-3 text-[9px] icon-[ph-caret-down-bold] default-animation;
}

nav.main-menu .menu-item-has-children {
   @apply inline-flex relative gap-1;
}

nav.main-menu .menu-item-has-children a {
   @apply relative;
}

nav.main-menu .menu-item-has-children a:hover:after {
   @apply rotate-180;
}

nav.main-menu .menu-item-has-children:hover > a:before {
   @apply content-[''] absolute top-12 left-0 z-50 w-full h-0.5 bg-primary-500;
}

nav.main-menu .menu-item-has-children .menu-item-has-children > a:before {
   @apply hidden;
}

nav.main-menu .menu-item-has-children .menu-item-has-children:after {
   @apply hidden;
}

nav.main-menu .menu-item.menu-item-has-children:hover ul.sub-menu {
   @apply grid;
}

nav.main-menu .sub-menu .menu-item:hover {
   @apply bg-neutral-200;
}

nav.main-menu .menu-item-has-children ul.sub-menu {
   @apply grid-flow-col auto-cols-auto hidden absolute w-max left-0 top-0 pt-3 pb-2 mt-12 bg-neutral-300 min-w-[11rem] rounded-bl rounded-br overflow-hidden;
   grid-template-rows: repeat(7, min-content);
}

nav.main-menu .menu-item:nth-last-child(-n + 3) ul.sub-menu {
   @apply left-auto right-0;
}

nav.main-menu .menu-item-has-children ul.sub-menu .menu-item {
   @apply w-[11rem] flex gap-4 relative flex-row-reverse;
}

nav.main-menu .menu-item-has-children ul.sub-menu .menu-item a {
   @apply w-full;
}

nav.main-menu .menu-item > a {
   @apply flex items-center gap-1 p-4;
}

/*Nav Mobile*/

#menu-mobile {
   @apply hidden absolute top-[92px] lg:top-[229px] inset-x-0;
}

body.is-scroll #menu-mobile {
   @apply top-[90px];
}

#menu-mobile.active {
   @apply block;
}

nav.main-menu-mobile .menu-item {
   @apply relative uppercase font-medium text-neutral-700 border-b text-xs;
}

nav.main-menu-mobile .menu-item:not(:last-child) {
   @apply border-neutral-300;
}

nav.main-menu-mobile .menu-item a {
   @apply py-4 px-2 w-full block;
}

nav.main-menu-mobile .menu-item a.active {
   @apply bg-neutral-300 mb-px rounded-tr rounded-tl;
}

nav.main-menu-mobile .menu-item-has-children {
   display: grid;
   grid-template-columns: 1fr auto;
   align-items: center;
}

nav.main-menu-mobile .menu-item-has-children a {
   grid-column: 1;
}

nav.main-menu-mobile .menu-item-has-children button {
   grid-column: 2;
   justify-self: end;
   aspect-ratio: 1 / 1;
   width: 2rem;
   background-repeat: no-repeat;
   background-position: center center;
   background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.00003 7.50791L2.62141 4.12884L2.6214 4.12883C2.52291 4.03033 2.38932 3.975 2.25003 3.975C2.11073 3.975 1.97714 4.03033 1.87865 4.12883C1.78015 4.22733 1.72482 4.36091 1.72482 4.50021C1.72482 4.6395 1.78015 4.77309 1.87865 4.87159L5.62859 8.62153L5.62865 8.62159L5.73471 8.51552L6.00003 7.50791ZM6.00003 7.50791L9.37864 4.12884L9.37865 4.12883C9.42742 4.08006 9.48532 4.04137 9.54904 4.01498C9.61276 3.98858 9.68106 3.975 9.75003 3.975C9.819 3.975 9.88729 3.98858 9.95101 4.01498C10.0147 4.04137 10.0726 4.08006 10.1214 4.12883C10.1702 4.1776 10.2089 4.2355 10.2353 4.29922C10.2616 4.36294 10.2752 4.43124 10.2752 4.50021C10.2752 4.56918 10.2616 4.63748 10.2353 4.7012C10.2089 4.76492 10.1702 4.82282 10.1214 4.87159L10.0153 4.76552L6.00003 7.50791Z' fill='%233D3D3D' stroke='%233D3D3D' stroke-width='0.3'/%3E%3C/svg%3E%0A");
}

nav.main-menu-mobile .menu-item-has-children button.active {
   @apply rotate-180;
}

nav.main-menu-mobile .menu-item-has-children ul.sub-menu {
   grid-column: 1 / -1;
   @apply hidden;
}

nav.main-menu-mobile .menu-item-has-children ul.sub-menu.active {
   @apply rounded-br rounded-bl block bg-neutral-300;
}

.profile-button.disabled,
.login-button.disabled,
.cta-download-logged.disabled,
.cta-download-not-logged.disabled {
   @apply hidden;
}
