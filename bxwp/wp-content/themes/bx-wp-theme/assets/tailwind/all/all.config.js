import {commonConfig, assetsFolder} from '../common/common.config'
const icons = require('@jcamp/tailwindcss-plugin-icons')

module.exports = {
   content: [
      `${assetsFolder}/pages/*.php`,
      `${assetsFolder}/pages/**/*.php`,
      `${assetsFolder}/components/*.php`,
      `${assetsFolder}/components/**/*.php`,
      `${assetsFolder}/assets/scripts/all/*.js`,
      `${assetsFolder}/assets/scripts/all/**/*.js`,
   ],
   safelist: [],
   theme: {
      container: {
         center: true,
         padding: {
            DEFAULT: '15px',
         },
      },
      fontFamily: {
         primary: commonConfig.fontFamilyPrimary,
         secondary: commonConfig.fontFamilysecondary,
      },
      extend: {
         colors: commonConfig.colors,
         boxShadow: {
            small: '0px 1px 2px 0px rgba(0, 0, 0, 0.12)',
         },
         opacity: {
            48: '.48',
         },
         spacing: {
            22: '5.5rem',
            75: '18.75rem',
         },
      },
   },
   plugins: [
      icons({
         scale: 1.6,
         unit: 'rem',
         prefix: 'icon-',
         mode: 'mask',
      }),
      require('@tailwindcss/forms'),
   ],
}
