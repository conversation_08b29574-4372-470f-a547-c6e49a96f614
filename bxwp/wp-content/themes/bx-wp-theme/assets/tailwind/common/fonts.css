@font-face {
   font-family: 'Tiempos Headline';
   src:
      url('../fonts/tiempos-headline/TiemposHeadline-Medium.woff2') format('woff2'),
      url('../fonts/tiempos-headline/TiemposHeadline-Medium.woff') format('woff');
   font-weight: 500;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-BoldItalic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-BoldItalic.woff') format('woff');
   font-weight: 700;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Black.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Black.woff') format('woff');
   font-weight: 900;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-BlackItalic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-BlackItalic.woff') format('woff');
   font-weight: 900;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Bold.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Bold.woff') format('woff');
   font-weight: 700;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-ExtraBold.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-ExtraBold.woff') format('woff');
   font-weight: 900;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-ExtraBoldItalic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-ExtraBoldItalic.woff') format('woff');
   font-weight: bold;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Medium.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Medium.woff') format('woff');
   font-weight: 500;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Italic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Italic.woff') format('woff');
   font-weight: normal;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-LightItalic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-LightItalic.woff') format('woff');
   font-weight: 300;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Light.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Light.woff') format('woff');
   font-weight: 300;
   font-style: normal;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-MediumItalic.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-MediumItalic.woff') format('woff');
   font-weight: 500;
   font-style: italic;
   font-display: swap;
}

@font-face {
   font-family: 'Barlow';
   src:
      url('../fonts/barlow/Barlow-Regular.woff2') format('woff2'),
      url('../fonts/barlow/Barlow-Regular.woff') format('woff');
   font-weight: normal;
   font-style: normal;
   font-display: swap;
}
