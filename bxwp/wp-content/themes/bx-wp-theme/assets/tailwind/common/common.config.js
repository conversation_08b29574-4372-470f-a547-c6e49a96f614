export const assetsFolder = './bxwp/wp-content/themes/bx-wp-theme'
const fallbackFonts = [
   'ui-sans-serif',
   'system-ui',
   '-apple-system',
   'BlinkMacSystemFont',
   'Segoe UI',
   'Roboto',
   'Helvetica Neue',
   'Arial',
   'Noto Sans',
   'sans-serif',
   'Apple Color Emoji',
   'Segoe UI Emoji',
   'Segoe UI Symbol',
   'Noto Color Emoji',
]
export const commonConfig = {
   fontFamilyPrimary: ['Barlow', ...fallbackFonts],
   fontFamilysecondary: ['Tiempos Headline', ...fallbackFonts],
   colors: {
      primary: {
         10: 'var(--primary-color-10)',
         100: 'var(--primary-color-100)',
         200: 'var(--primary-color-200)',
         300: 'var(--primary-color-300)',
         500: 'var(--primary-color-500)',
         800: 'var(--primary-color-800)',
      },
      secondary: {
         100: 'var(--secondary-color-100)',
         200: 'var(--secondary-color-200)',
         300: 'var(--secondary-color-300)',
         500: 'var(--secondary-color-500)',
         800: 'var(--secondary-color-800)',
      },
      neutral: {
         100: '#FAFAFA',
         200: '#F0F0F0',
         300: '#E0E0E0',
         400: '#B8B8B8',
         500: '#7A7A7A',
         600: '#616161',
         700: '#3D3D3D',
         800: '#141414',
      },
      red: {
         200: '#F5866C',
         300: '#BF301C',
         500: '#D31100',
      },
      yellow: {
         500: '#FCD923',
      },
      green: {
         300: '#81D463',
         400: '#74F4B0',
         700: '#1CBF69',
      },
   },
}
