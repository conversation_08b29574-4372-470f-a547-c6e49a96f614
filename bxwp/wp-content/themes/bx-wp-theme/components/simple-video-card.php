<?php

use CanaisDigitais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}

$video   = isset($args['video']) ? $args['video'] : '';
$is_wide = isset($args['is_wide']) ? $args['is_wide'] : false;

if (empty($video)) {
   return;
}

if ($is_wide === true) {
   $wrapper_classes = 'aspect-[311/176] lg:aspect-[656/420]';
   $anchor_classes  = 'px-4 py-6 lg:px-8 lg:py-12';
   $title_classes   = 'text-base leading-normal lg:text-3xl lg:leading-10';
   $date_classes    = 'text-base font-normal leading-normal';
} else {
   $wrapper_classes = 'aspect-[24/31] lg:aspect-[4/5]';
   $anchor_classes  = 'px-4 py-8';
   $title_classes   = 'text-base lg:text-xl leading-7';
   $date_classes    = 'text-xs font-medium leading-5 uppercase';
}

if (!empty($args['additional_wrapper_classes'])) {
   $wrapper_classes .= ' ' . $args['additional_wrapper_classes'];
}

$Video = new CD_Post($video);

?>
<div class="group relative block <?php echo esc_attr($wrapper_classes); ?>">
   <a class="w-full h-full relative flex flex-col justify-end overflow-hidden <?php echo esc_attr($anchor_classes); ?>" href="<?php echo $Video->get_link(); ?>">
      <?php

      if ($Video->has_thumbnail()) {
         echo $Video->get_thumbnail('medium_large', [
            'class' => 'absolute h-full object-cover object-center inset-0 group-hover:backdrop-blur-md group-hover:blur-md default-animation'
         ]);
      }

      ?>
      <div class="absolute inset-0 z-[1] bg-black/48"></div>
      <div class="relative z-[2]">
         <span class="h-12 w-12 mb-4 rounded-full flex items-center justify-center bg-neutral-800 bg-opacity-50 backdrop-blur-md lg:mb-8">
            <?php

            render_svg('play');

            ?>
         </span>
         <?php

         if (!empty($Video->get_category())) {

         ?>
            <span class="mb-3 text-white text-xs font-medium uppercase leading-5">
               <?php

               echo $Video->get_category()->get_name();

               ?>
            </span>
         <?php

         }

         if (!empty($Video->get_title())) {

         ?>
            <span class="mb-4 text-white font-normal line-clamp-2 group-hover:underline lg:line-clamp-3 <?php echo esc_attr($title_classes); ?>">
               <?php echo $Video->get_title(); ?>
            </span>
         <?php

         }

         if (!empty($Video->get_date())) {

         ?>
            <span class="text-white <?php echo esc_attr($date_classes); ?>">
               <?php echo $Video->get_date(); ?>
            </span>
         <?php

         }

         ?>
      </div>
   </a>
</div>
