<?php

if (!defined('ABSPATH')) {
   exit;
}

do_action('get_header', null, []);

?>
<!DOCTYPE html>
<html <?php echo get_language_attributes(); ?>>

<head>
   <?php

   wp_head();

   ?>
</head>

<body <?php body_class('font-primary bg-white'); ?>>

   <?php

   wp_body_open();

   get_component('bar-informa');

   ?>
   <header x-data="{ showSearch: false }" class="header w-full sticky top-0 bg-white z-50">
      <div class="flex flex-col">
         <div class="hidden lg:flex justify-between items-center px-6 py-1 border-b border-neutral-200">
            <div class="flex w-1/2">
               <?php

               if (has_nav_menu('header-top-menu')) {
               ?>
                  <div>
                     <?php

                     wp_nav_menu([
                        'menu_id'         => 'header-top-menu',
                        'theme_location'  => 'header-top-menu',
                        'container'       => 'nav',
                        'container_class' => 'top-menu',
                        'menu_class'      => 'flex gap-5',
                        'fallback_cb'     => '__return_empty_string',
                     ]);

                     ?>
                  </div>
               <?php

               }

               ?>
            </div>
            <div class="w-1/2 flex justify-end">
               <div>
                  <?php

                  do_action('bx_cd_get_social_media', 'light');

                  ?>
               </div>
            </div>
         </div>
         <div class="grid grid-cols-[80px_1fr_80px] lg:flex justify-between shadow-small">

            <?php $bx_cd_mega_menu = get_field('bx_cd_mega_menu', 'mega-menu'); ?>

            <div class="flex justify-center items-center p-6 border-r border-neutral-200 <?php echo empty($bx_cd_mega_menu) ? 'lg:border-0 lg:pl-24' : ''; ?>">

               <button id="show-menu-mobile" class="icon-button icon-button-show-menu <?php echo empty($bx_cd_mega_menu) ? 'lg:hidden' : ''; ?>">
                  <span class="text-sm icon-list icon-[ph-list] text-neutral-700"></span>
                  <span class="text-sm icon-x icon-[ph-x] text-neutral-700"></span>
               </button>

            </div>
            <div class="flex py-2.5 lg:pl-6 w-full justify-center lg:justify-start-on-scroll">
               <div class="site_logo relative flex items-center">
                  <?php

                  $custom_logo_id = get_theme_mod('custom_logo');
                  $logo           = wp_get_attachment_image_src($custom_logo_id, 'full');
                  $site_url       = esc_url(home_url());

                  if (has_custom_logo()) {

                     $logo_url  = esc_url($logo[0]);
                     $site_name = get_bloginfo('name');

                     if (is_home()) {

                  ?>
                        <h1 class="absolute -indent-[9999px] top-0 left-0 -z-10"><?php echo $site_name; ?></h1>
                     <?php

                     }

                     ?>
                     <a href="<?php echo $site_url; ?>">
                        <img class="logo_image" src="<?php echo $logo_url; ?>" alt="<?php echo $site_name; ?>" title="<?php echo $site_name; ?>">
                     </a>
                  <?php

                  } else {

                  ?>
                     <h1 class="uppercase">
                        <a href="<?php echo $site_url; ?>">
                           <?php echo get_bloginfo('name'); ?>
                        </a>
                     </h1>
                  <?php

                  }

                  ?>
               </div>
            </div>
            <div class="flex items-center justify-center gap-[1.875rem] border-l border-neutral-200 lg:border-0">
               <div>
                  <button x-on:click="showSearch = true" class="icon-button" id="show-search">
                     <span class="text-sm icon-[ph-magnifying-glass] [mask-size:1.4rem] [mask-position:center] text-neutral-700"></span>
                  </button>
               </div>
               <div class="hidden lg:flex justify-center items-center p-9 h-full border-neutral-200">
                  <!-- button button-primary login-button -->
                  <button class="hidden">
                     <?php

                     esc_html_e('Entrar', 'canais_digitais');

                     ?>
                  </button>

                  <?php

                  get_component('profile-button');

                  ?>

               </div>
            </div>
         </div>
         <?php

         if (has_nav_menu('header-main-menu')) {
         ?>
            <div class="hidden lg:flex justify-center border-t border-neutral-200 bg-neutral-200">
               <?php

               wp_nav_menu([
                  'menu_id'         => 'header-main-menu',
                  'theme_location'  => 'header-main-menu',
                  'container'       => 'nav',
                  'container_class' => 'main-menu',
                  'menu_class'      => 'flex',
                  'fallback_cb'     => '__return_empty_string',
               ]);

               ?>
            </div>
         <?php

         }

         ?>
      </div>

      <?php

      get_component('search-modal');

      get_component('nav-mobile');

      ?>

   </header>
   <main>
