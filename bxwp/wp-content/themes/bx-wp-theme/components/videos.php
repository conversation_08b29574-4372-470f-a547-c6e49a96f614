<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\Category\Utils as CategoryUtils;
use Canais<PERSON><PERSON>tais\CD_Post\Utils;

$category_ID = $args['category_id'] ?? 0;

$video_posts = !empty($category_ID) ? CategoryUtils::get_videos($category_ID) : Utils::get_homepage_videos();

$basic_info = !empty($category_ID) ? CategoryUtils::get_special_page_videos_basic_info($category_ID) : Utils::get_homepage_videos_basic_info();

if (!empty($video_posts)) {

?>
   <section class="py-16 bg-neutral-800">
      <div class="container">
         <div class="grid grid-cols-1 gap-12 lg:gap-52  md:grid-cols-2">
            <div class="mt-14 px-6 m-w-[31.25rem] lg:mt-20">
               <?php

               if (!empty($basic_info['title'])) {

               ?>
                  <h2 class="mb-4 text-white text-6xl font-normal leading-[4rem]">
                     <?php

                     echo esc_html($basic_info['title']);

                     ?>
                  </h2>
               <?php

               }

               if (!empty($basic_info['description'])) {

               ?>
                  <p class="text-neutral-500 mb-8 text-2xl font-normal leading-8">
                     <?php

                     echo esc_html($basic_info['description']);

                     ?>
                  </p>

               <?php

               }

               ?>
               <a href="<?php echo esc_url(CategoryUtils::get_default_category_url('video')); ?>" class="button button-primary">
                  <?php

                  esc_html_e('Acessar Acervo', 'canais_digitais');

                  ?>
               </a>
            </div>
            <?php

            if (!empty($video_posts)) {

            ?>
               <div class="px-4">
                  <?php

                  get_component('simple-video-card', [
                     'video'   => $video_posts[0],
                     'is_wide' => true,
                  ]);

                  ?>
               </div>
            <?php

               unset($video_posts[0]);
            }

            ?>
         </div>
         <?php

         if (!empty($video_posts)) {

         ?>
            <div class="border-t border-neutral-700 pt-4 mt-14">
               <h3 class="mb-9 text-xl text-white">
                  <?php

                  esc_html_e('Assista outros vídeos', 'canais_digitais');

                  ?>
               </h3>
               <div class="slider-homepage-videos swiper">
                  <div class="swiper-wrapper grid grid-cols-1 md:grid-cols-4">
                     <?php

                     foreach ($video_posts as $video) {
                     ?>

                        <div class="swiper-slide">
                           <?php

                           get_component('simple-video-card', [
                              'video' => $video
                           ]);

                           ?>
                        </div>
                     <?php

                     }

                     ?>
                  </div>
               </div>
            </div>
         <?php

         }

         ?>
      </div>
   </section>
<?php

}
