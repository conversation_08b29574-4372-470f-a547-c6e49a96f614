<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\Page\Utils as PageUtils;
use CanaisDigitais\Form\Utils as FormUtils;
use CanaisDigitais\Category\Category;

if (!FormUtils::is_lead_form_enabled()) {
   return;
}

bx_sntls_Utils::load_class('Form');

if (!class_exists('bx_sntls_Form')) {
   return;
}

$form_newsletter_page = PageUtils::get_form_newsletter_page();
$Page                 = new CD_Post($form_newsletter_page);

if (empty($Page)) {
   return;
}

$category_ID = $args['category_id'] ?? 0;

if (!empty($category_ID)) {
   $Category        = new Category($category_ID);
   $newsletter_data = $Category->get_special_page_newsletter();
} else {
   $newsletter_data = [
      'title'            => get_field('homepage_newsletter_title', 'homepage-options'),
      'text'             => get_field('homepage_newsletter_text', 'homepage-options'),
      'background_type'  => get_field('homepage_newsletter_background_type', 'homepage-options'),
      'image'            => get_field('homepage_newsletter_image', 'homepage-options'),
      'background_color' => get_field('homepage_newsletter_background_color', 'homepage-options'),
      'inner_background' => get_field('homepage_newsletter_inner_background', 'homepage-options'),
   ];
}

$newsletter_background = '';

if ($newsletter_data['background_type'] == 'image') {
   $newsletter_background = 'style="background-image: url(\'' . $newsletter_data['image'] . '\')"';
} else if ($newsletter_data['background_type'] == 'color') {
   $newsletter_background = 'style="background-color: ' . $newsletter_data['background_color'] . '"';
}

?>
<section x-cloak x-data="newsletterSignUpCheck" x-show="!isCookieSet" class="py-16 bg-center bg-cover" <?php echo $newsletter_background; ?>>
   <div class="container">
      <div class="px-6 py-12 flex flex-col justify-between items-center lg:px-[7rem] lg:py-[10rem] lg:flex-row <?php echo $newsletter_data['inner_background'] == true ? "bg-white" : ""; ?>">
         <div class="w-full lg:w-[26.875rem]">
            <?php

            if (!empty($newsletter_data['title'])) {

            ?>
               <h2 class="mb-4 text-2xl text-neutral-700 lg:text-5xl">
                  <?php echo $newsletter_data['title']; ?>
               </h2>
            <?php

            }

            if (!empty($newsletter_data['text'])) {

            ?>
               <p class="text-sm leading-snug text-neutral-500 lg:leading-normal lg:text-xl">
                  <?php echo $newsletter_data['text']; ?>
               </p>
            <?php

            }

            ?>
         </div>
         <form action="<?php echo esc_url($Page->get_link()); ?>" method="get" class="w-full lg:w-[34rem] h-auto lg:h-[5.063rem] mt-6 rounded-lg px-[8px] py-[12px] flex flex-col gap-5 justify-between items-center bg-transparent lg:bg-white lg:flex-row">
            <?php

            $Form = new bx_sntls_Form();

            $Form->input([
               'id'        => 'emailAddress',
               'type'      => 'email',
               'name'      => 'emailAddress',
               'container' => false,
               'attrs'     => [
                  'class'       => 'w-full lg:w-4/5 h-full lg:h-[5.063rem] pl-[10px] py-4 outline-0 text-xl lg:text-[2.5rem] tracking-tight text-neutral-500 border-0 border-b border-neutral-300 placeholder:text-neutral-300 active:border-0 active:border-b active:border-b-neutral-300 active:ring-0 focus:border-0 focus:border-b focus:ring-0 focus:border-b-neutral-300',
                  'placeholder' => esc_html__('Digite seu email', 'canais-digitais'),
                  'required'    => true,
               ],
            ]);

            $Form->button([
               'id'        => 'signup-button',
               'type'      => 'submit',
               'value'     => esc_html__('Assinar', 'canais-digitais'),
               'container' => false,
               'attrs'     => [
                  'class' => 'button button-primary !flex !items-center w-[5.625rem] lg:w-[5.688rem] h-11 lg:h-[3.75rem]',
               ],
            ]);

            ?>
         </form>
      </div>
   </div>
</section>
