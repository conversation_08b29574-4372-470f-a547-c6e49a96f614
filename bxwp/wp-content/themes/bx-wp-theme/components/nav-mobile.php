<?php

if (!defined('ABSPATH')) {
   exit;
}

?>

<div id="menu-mobile" class="bg-neutral-200">

   <div class="lg:hidden">
      <div class="hidden py-6 px-4">

         <button class="button button-primary button-full-width w-full login-button" id="loginBtnMobile">
            <?php

            esc_html_e('Entrar', 'canais_digitais');

            ?>
         </button>

         <?php

         get_component('profile-button', [
            'profile_button_classes' => 'my-5 px-3 py-4 bg-neutral-100'
         ]);

         ?>

      </div>

      <?php

      if (has_nav_menu('header-main-menu')) {

      ?>
         <div class="px-2 pt-4 flex flex-col justify-center border-t border-neutral-300 bg-neutral-200">
            <?php

            wp_nav_menu([
               'menu_id'         => 'header-main-menu',
               'theme_location'  => 'header-main-menu',
               'container'       => 'nav',
               'container_class' => 'main-menu-mobile',
               'menu_class'      => 'flex flex-col',
               'fallback_cb'     => '__return_empty_string',
               'walker'          => new _BX_CD_Mobile_Menu_Walker(),
            ]);

            ?>
         </div>
      <?php

      }

      ?>

      <div class="pt-6 px-4 pb-12 flex justify-center border-t-2 border-neutral-300">
         <?php

         do_action('bx_cd_get_social_media', 'white');

         ?>
      </div>

   </div>

   <?php
   $bx_cd_mega_menu = get_field('bx_cd_mega_menu', 'mega-menu');
   if (!empty($bx_cd_mega_menu)) {
   ?>

      <div class="hidden lg:block bg-neutral-300 pt-12 pb-36">

         <div class="container">

            <div class="grid gap-4 grid-cols-1 lg:grid-cols-[1fr_1fr_1fr_1fr]">

               <?php foreach ($bx_cd_mega_menu as $item) { ?>

                  <div>

                     <?php if (!empty($item['title'])) { ?>

                        <div class="text-primary-500 text-xs font-medium uppercase leading-4 mb-5">
                           <?php echo esc_html_e($item['title'], 'canais_digitais'); ?>
                        </div>

                     <?php } ?>



                     <?php if (!empty($item['links'])) { ?>

                        <nav>
                           <ul>

                              <?php

                              foreach ($item['links'] as $link) {

                                 if ($item['type'] == 'page') {

                                    $link_title = get_the_title($link['page']);
                                    $link_url = get_the_permalink($link['page']);
                                 } else if ($item['type'] == 'post') {

                                    $link_title = get_the_title($link['post']);
                                    $link_url = get_the_permalink($link['post']);
                                 } else if ($item['type'] == 'category') {

                                    $link_title = get_cat_name($link['category']);
                                    $link_url = get_term_link($link['category']);
                                 } else if ($item['type'] == 'external_link') {

                                    $link_title = esc_html($link['external_link_title']);
                                    $link_url = esc_url($link['external_link']);
                                 }

                              ?>

                                 <li>
                                    <a href="<?php echo $link_url; ?>" class="py-1 inline-block">
                                       <?php

                                       if (!empty($link_title)) {

                                          echo $link_title;
                                       }

                                       ?>
                                    </a>
                                 </li>

                              <?php } ?>

                           </ul>
                        </nav>

                     <?php } ?>

                  </div>

               <?php } ?>

            </div>

         </div>

      </div>

   <?php } ?>


</div>
