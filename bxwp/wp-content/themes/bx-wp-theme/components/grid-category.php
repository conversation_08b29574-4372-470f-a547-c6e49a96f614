<?php

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;
use CanaisDigitais\Utils;

if (!defined('ABSPATH')) {
   exit;
}

$section = $args['section'] ?? false;

if (empty($section)) {
   return;
}

$articles = $section['news'];

if (empty($articles)) {
   return;
}

$Category              = new Category(get_category($section['category_id']));
$category_color        = $Category->get_color();
$category_title_format = $Category->get_title_format();
$ad_name               = $args['ad_name'] ?? false;

$left_border = '';
switch ($category_title_format) {
   case 'duo_tone':
      $background_opacity        = Utils::hex2rgb($category_color, 0.3);
      $category_title_style      = "style=\"background-color: $background_opacity\"";
      $category_title_text_style = "style=\"background-color: $category_color\"";
      break;

   case 'left_border':
      $category_title_text_style = 'style="padding-left: 0; display: flex; align-items: center; color: ' . $category_color . '"';
      $left_border               = '<div style="margin-right: 24px; width: 2px; height: 24px; background-color: ' . $category_color . '"></div>';
      break;

   case 'text_only':
      $category_title_text_style = 'style="padding-left: 0; color: ' . $category_color . '"';
      break;

   default:
      $category_title_style      = "style=\"border-top: 2px solid $category_color\"";
      $category_title_text_style = "style=\"background-color: $category_color\"";
      break;
}

?>
<div class="container">
   <div class="flex flex-col gap-8 py-6">

      <div class="flex" <?php echo $category_title_style ?? ''; ?>>

         <?php

         $link = esc_url($Category->get_link());

         echo <<<CATEGORYTAB
            <a class="inline px-6 py-4 hover:pr-[10%] transition-all duration-500 ease-linear text-white text-3xl font-normal leading-10" href="$link" $category_title_text_style>
               {$left_border}
               {$Category->get_name()}
            </a>
            CATEGORYTAB;

         ?>
      </div>
      <div class="flex flex-col gap-4 lg:gap-8">
         <div class="flex flex-col gap-4 lg:flex-row">
            <div class="w-full lg:w-1/2">
               <?php

               get_component('article-card-overlay', [
                  'article'          => $articles[0],
                  'size_class'       => 'aspect-[343/180] lg:aspect-[656/420]',
                  'section_category' => $Category,
               ]);

               ?>
            </div>
            <div class="flex flex-col mt-3 gap-4 w-full lg:w-1/2 lg:mt-0">
               <div>
                  <?php

                  get_component('article-card', [
                     'article_id'        => $articles[1] ?? '',
                     'style'             => 'vertical',
                     'section_category'  => is_category() ? $Category : null,
                     'size_class'        => ' h-[6.5rem] w-auto aspect-square lg:h-auto lg:aspect-[320/202]',
                     'thumbnail_classes' => 'w-[6.5rem]'
                  ]);

                  ?>
               </div>
               <div>
                  <?php

                  get_component('article-card', [
                     'article_id'        => $articles[2] ?? '',
                     'style'             => 'vertical',
                     'section_category'  => is_category() ? $Category : null,
                     'size_class'        => ' h-[6.5rem] w-auto aspect-square lg:h-auto lg:aspect-[320/202]',
                     'thumbnail_classes' => 'w-[6.5rem]'
                  ]);

                  ?>
               </div>
            </div>
         </div>
         <div class="grid grid-cols-1 gap-4 lg:grid-cols-4">
            <?php

            $sliced_articles = array_slice($articles, 3, 3);

            if (!empty($sliced_articles)) {

               foreach ($sliced_articles as $article_item) {
            ?>
                  <div>
                     <?php

                     get_component('article-card', [
                        'article_id'        => $article_item,
                        'section_category'  => is_category() ? $Category : '',
                        'size_class'        => ' h-[6.5rem] w-auto aspect-square lg:h-auto lg:aspect-[320/202]',
                        'thumbnail_classes' => 'w-[6.5rem]'
                     ]);

                     ?>
                  </div>
            <?php
               }
            }

            ?>
            <div>
               <?php

               if (!empty($ad_name)) {
                  new Ad($ad_name, 'ad-bg-placeholder');
               }

               ?>
            </div>
         </div>
      </div>
   </div>
</div>
