<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\CD_Post\Utils as PostUtils;
use CanaisDigitais\Theme_Settings\Utils as ThemeSettingsUtils;
use CanaisDigitais\Ad\Ad;
use CanaisDigitais\Category\Category;
use CanaisDigitais\Category\Utils as CategoryUtils;

$category_ID = $args['category_id'] ?? 0;

if (!empty($category_ID)) {
   $Category = new Category($category_ID);

   $sections = [
      'grid-category'      => 'grid-category',
      'grid-specialists'   => 'grid-specialists',
      'grid-events'        => 'grid-events',
      'newsletter'         => 'newsletter',
      'videos'             => 'videos',
      'featured-materials' => 'featured-materials',
      'sponsors'           => 'sponsors',
   ];

   $ordering_sections   = $Category->get_special_page_ordering_sections();
   $categories_sections = CategoryUtils::get_special_page_categories_list($category_ID);
} else {
   $sections            = ThemeSettingsUtils::$selectable_sections;
   $ordering_sections   = ThemeSettingsUtils::get_homepage_ordering_sections();
   $categories_sections = ThemeSettingsUtils::get_homepage_categories_list();
}

$categories_sections_ids = !empty($categories_sections) ? array_column($categories_sections, 'category_id') : [];

$home_ads = ['300_2_rht', '300_2_lft'];

if (!is_array($ordering_sections) || empty($ordering_sections)) {
   return;
}

$chosen_sections = array_column($ordering_sections, 'single_section');

?>
<div class="flex flex-col">
   <?php

   $count_sections           = array_count_values($chosen_sections);
   $count_category_sections  = isset($count_sections['category']) ? $count_sections['category'] : 0;
   $current_category_section = 0;

   $ad_list = [
      'nativekey_main_ge1',
      'nativekey_main_ge2',
      'nativekey_main_ge3'
   ];

   foreach ($chosen_sections as $key => $section) {
      if ($key === 2) {

   ?>
         <div class="container flex flex-col lg:flex-row lg:justify-center">
            <?php

            foreach ($ad_list as $ad) {
               new Ad($ad, 'my-4');
            }

            ?>
         </div>
   <?php

      }

      if ($section === 'category') {
         $section_category_id = $categories_sections_ids[$current_category_section] ?? null;

         if (empty($section_category_id)) {
            $current_category_section = 0;
         }

         if (!empty($args['category_id'])) {
            $category = CategoryUtils::get_special_page_category_sections_with_articles($section_category_id, $categories_sections);
         } else {
            $category = PostUtils::get_homepage_category_sections_with_articles($section_category_id);

            $category = !empty($category) ? reset($category) : [];
         }

         if ($count_category_sections > 0 && !empty($category)) {
            if ($current_category_section <= $count_category_sections - 1) {
               get_component(
                  'grid-category',
                  [
                     'section' => $category,
                     'ad_name' => $home_ads[$current_category_section] ?? null,
                  ]
               );

               $current_category_section++;
            }
         }
      } else {
         get_component($sections[$section], [
            'category_id' => !empty($category_ID) ? (int) $category_ID : 0,
         ]);
      }
   }

   ?>
</div>
