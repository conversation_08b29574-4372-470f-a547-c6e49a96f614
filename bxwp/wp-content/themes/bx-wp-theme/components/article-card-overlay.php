<?php

use CanaisDigitais\CD_Post\CD_Post;


if (!defined('ABSPATH')) {
   exit;
}


if (empty($args['article'])) {
   return;
}

$CD_Post = new CD_Post($args['article']);

if (!empty($CD_Post)) {
   $has_thumbnail = $CD_Post->has_thumbnail();
   $category      = !empty($args['section_category']) ? $args['section_category'] : $CD_Post->get_category();

?>
   <div class="group">
      <a class="flex <?php echo $args['size_class'] ?? 'w-full h-[28.25rem]' ?>" href="<?php echo esc_url($CD_Post->get_link()); ?>">
         <div class="flex w-full h-full relative default-animation">
            <div class="absolute w-full h-full overflow-hidden">
               <div class="z-[2] absolute w-full h-full bg-black/48"></div>
               <div class="w-full h-full group-hover:blur-md default-animation scale-110">
                  <?php

                  if ($has_thumbnail) {
                     echo $CD_Post->get_thumbnail('medium_large', [
                        'class' => 'w-full h-full object-cover'
                     ]);
                  }

                  ?>
               </div>
            </div>
            <div class="z-[3] flex flex-col justify-end w-full h-full p-4 lg:py-8 lg:px-4 text-white">
               <?php

               if (!empty($category)) {

               ?>
                  <span class="mb-2 text-white text-base lg:text-xs font-medium uppercase leading-5">
                     <?php

                     echo esc_html($category->get_name());

                     ?>
                  </span>
               <?php

               }

               ?>
               <span class="mb-4 text-white text-base lg:text-[1.75rem] font-normal leading-6 lg:leading-7 line-clamp-2 lg:line-clamp-3 group-hover:underline">
                  <?php

                  printf(
                     "%s %s",
                     !empty($args['is_download']) ? esc_html__('[E-book]', 'bx-wp') : '',
                     $CD_Post->get_title(),
                  );

                  ?>
               </span>
               <?php

               if ($CD_Post->get_date()) {

               ?>
                  <span class="text-white text-base font-normal leading-normal">
                     <?php

                     echo esc_html($CD_Post->get_date());

                     ?>
                  </span>
               <?php

               }

               ?>
            </div>
         </div>
      </a>
   </div>
<?php

}

?>
