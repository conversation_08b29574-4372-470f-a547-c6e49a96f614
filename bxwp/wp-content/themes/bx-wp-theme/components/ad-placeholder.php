<?php

if (!defined('ABSPATH')) {
   exit;
}

$ad_name         = $args['ad_name'] ?? '';
$ad              = $args['ad_info'] ?? [];
$ad_sizes        = $args['ad_sizes'] ?? [];
$wrapper_classes = $args['wrapper_classes'] ?? 'uu';

if (empty($ad)) {
   return;
}

if (!empty($wrapper_classes)) {

?>
   <div class="<?php echo esc_attr($wrapper_classes); ?>">
   <?php

}

   ?>
   <div class="ad-wrapper-placeholder">
      <img class="inline-block max-w-full h-auto object-cover" src="<?php printf('https://placehold.co/%dx%d?text=%s', $ad_sizes['desktop'][0], $ad_sizes['desktop'][1], $ad_name); ?>" alt>
   </div>
   <?php

   if (!empty($wrapper_classes)) {

   ?>
   </div>

<?php

   }

?>
