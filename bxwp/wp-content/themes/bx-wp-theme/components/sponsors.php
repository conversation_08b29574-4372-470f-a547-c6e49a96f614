<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\Category\Category;

$Category = new Category($args['category_id']);

$sponsors = $Category->get_sponsors();

if (empty($sponsors)) {
   return;
}

?>

<div class="flex justify-center items-center pt-6">
   <div class="flex justify-center items-center lg:flex-row flex-col lg:gap-6 gap-0 bg-white max-w-[64.188rem] w-full h-auto px-6">
      <div class="uppercase pt-3 lg:pt-0 text-center w-[7.438rem] text-xs text-neutral-500">
         <?php esc_html_e('Patrocínio', 'canais_digitais'); ?>
      </div>

      <div class="sponsors-slide w-full h-24 py-2 overflow-hidden bg-white">
         <div class="swiper-wrapper">
            <?php

            foreach ($sponsors as $sponsor) {

            ?>
               <div class="swiper-slide flex justify-center items-center h-full">
                  <picture class="h-full flex items-center justify-center">
                     <img class="select-none max-h-full object-contain" alt="<?php echo sprintf(esc_html__('Patrocínio - %s', 'canais_digitais'), get_bloginfo('name')); ?>" title="<?php echo sprintf(esc_html__('Patrocínio - %s', 'canais_digitais'), get_bloginfo('name')); ?>" src="<?php echo esc_url($sponsor['image']); ?>">
                  </picture>
               </div>
            <?php

            }

            ?>
         </div>
      </div>
   </div>
</div>
