<?php


if (!defined('ABSPATH')) {
   exit;
}

$Breadcrumbs = new CanaisDigitais\Breadcrumbs();
$trail       = $Breadcrumbs->get_trail();

if (empty($trail)) {
   return;
}

?>
<nav>
   <ul class="flex items-center gap-2">
      <?php

      foreach ($trail as $item) {

         if (empty($item['title'])) {
            continue;
         }

      ?>
         <li>
            <a class="text-xs font-medium uppercase leading-tight" href="<?php echo esc_url($item['url']); ?>">
               <?php echo esc_html($item['title']); ?>
            </a>
         </li>
         <li>
            <?php

            render_svg('arrow/right', 'w-3 h-3 fill-current stroke-current');

            ?>
         </li>
      <?php

      }

      ?>
   </ul>
</nav>
