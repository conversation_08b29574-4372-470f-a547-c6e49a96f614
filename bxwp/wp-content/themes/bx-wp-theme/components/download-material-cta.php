<?php

use CanaisDigitais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}

$Download = new CD_Post($args['download']);

$download_title       = get_field('download_material_cta_title', 'general-options');
$download_description = get_field('download_material_cta_description', 'general-options');

?>

<div class="row-span-2 flex flex-col justify-between border border-primary-500 bg-white <?php echo $args['classes'] ?? 'h-[597px]' ?> p-4 ">
   <div class="flex flex-col items-center text-center my-auto">

      <div class="text-[2rem] lg:text-[2.5rem] text-neutral-700 mb-4">
         <?php echo !empty($download_title) ? esc_html($download_title) : esc_html__('Downloads', 'canais-digitais'); ?>
      </div>
      <?php

      if (!empty($download_description)) {

      ?>
         <p class="text-sm lg:text-base text-neutral-700 mb-6">
            <?php echo esc_html($download_description); ?>
         </p>
      <?php

      }

      ?>
      <a href="<?php echo site_url('downloads'); ?>" class="text-primary-500 uppercase text-xs font-medium">
         <?php esc_html_e('Ver todos', 'canais-digitais') ?>
      </a>
   </div>
   <div class="group relative p-6 lg:p-8 <?php echo $args['post_size_classes'] ?? 'h-[22.5rem]' ?> overflow-hidden flex items-end">
      <a href="<?php echo $Download->get_link(); ?>">
         <div class="absolute inset-0 z-[2] bg-black/48"></div>
         <?php

         if ($Download->has_thumbnail()) {
            echo $Download->get_thumbnail('medium', [
               'class' => 'absolute w-full h-full object-cover object-center inset-0 group-hover:backdrop-blur-md group-hover:blur-md default-animation'
            ]);
         }

         ?>

         <div class="absolute top-4 right-4 z-30 flex items-center justify-center w-8 h-8 rounded-full bg-white/50 text-white">
            <?php

            render_svg('download', 'w-4 h-4');

            ?>
         </div>

         <div class="relative z-20 flex flex-col justify-end">
            <?php

            if (!empty($Download->get_category())) {

            ?>
               <span class="mb-2 text-xs uppercase font-medium text-white">
                  <?php echo $Download->get_category()->get_name(); ?>
               </span>
            <?php

            }

            if (!empty($Download->get_title())) {

            ?>
               <span class="mb-4 text-base lg:text-xl text-white line-clamp-4 group-hover:underline">
                  <?php echo $Download->get_title(); ?>
               </span>
            <?php

            }

            ?>
            <?php if (!empty($Download->get_date())) { ?>
               <span class="text-white text-xs font-medium uppercase leading-5">
                  <?php echo $Download->get_date(); ?>
               </span>
            <?php

            }

            ?>
         </div>
      </a>
   </div>
</div>
