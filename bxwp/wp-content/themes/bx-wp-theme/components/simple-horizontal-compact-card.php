<?php

use Canais<PERSON>igitais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}

$cd_post           = isset($args['cd_post']) ? $args['cd_post'] : '';
$wrapper_classes   = isset($args['wrapper_classes']) ? $args['wrapper_classes'] : '';
$thumbnail_classes = isset($args['thumbnail_classes']) ? $args['thumbnail_classes'] : '';



if (empty($cd_post)) {

   return;
}

$CD_Post = new CD_Post($args['cd_post']);
$date    = isset($args['date']) ? $args['date'] : $CD_Post->get_date();


?>
<div class="<?php echo esc_attr($wrapper_classes); ?>">
   <a class="flex gap-4 w-full" href="<?php echo esc_url($CD_Post->get_link()); ?>">
      <div class="w-auto h-full">
         <?php

         $category = $CD_Post->get_category();

         if ($CD_Post->has_thumbnail()) {
            echo $CD_Post->get_thumbnail('thumbnail', [
               'class' => 'object-cover ' . $thumbnail_classes
            ]);
         }

         ?>
      </div>
      <div class="flex flex-col grow justify-between">
         <?php

         if (!empty($category)) {

         ?>
            <span class="text-primary-500 text-xs font-medium uppercase leading-4 line-clamp-2">
               <?php

               echo esc_html($category->get_name());

               ?>
            </span>
         <?php

         }

         ?>
         <span class="text-neutral-700 text-base font-normal leading-6 line-clamp-2">
            <?php

            echo esc_html($CD_Post->get_title());
            ?>

         </span>
         <?php

         if (!empty($date)) {

         ?>
            <span class="text-neutral-500 text-xs font-medium uppercase leading-4">
               <?php

               echo esc_html($date);

               ?>
            </span>
         <?php

         }

         ?>
      </div>
   </a>
</div>
