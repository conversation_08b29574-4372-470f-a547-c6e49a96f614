<?php

use CanaisDigitais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}

$article_id = isset($args['article_id']) ? $args['article_id'] : null;

if (is_null($article_id) || empty($article_id)) {
   return false;
}

$CD_Post = new CD_Post($article_id);

if (empty($CD_Post)) {
   return;
}

$has_thumbnail   = $CD_Post->has_thumbnail();
$category        = !empty($args['section_category']) ? $args['section_category'] : $CD_Post->get_category();
$style           = isset($args['style']) ? $args['style'] : 'horizontal';
$wrapper_classes = isset($args['wrapper_classes']) ? $args['wrapper_classes'] : '';
$classes         = [
   'horizontal' => 'flex lg:flex-col',
   'vertical'   => 'flex lg:grid lg:grid-cols-2'
];
$thumbnail_classes = isset($args['thumbnail_classes']) ? $args['thumbnail_classes'] : '';

?>
<div class="group <?php echo esc_attr($wrapper_classes); ?>">
   <a class="gap-4 <?php echo esc_attr($classes[$style]); ?>" href="<?php echo esc_url($CD_Post->get_link()); ?>">
      <div class="<?php echo $args['size_class'] ?? 'w-auto h-full' ?>">
         <?php

         if ($has_thumbnail) {
            echo $CD_Post->get_thumbnail('medium_large', [
               'class' => 'flex max-h-60 w-full h-full object-cover ' . $thumbnail_classes
            ]);
         }

         ?>
      </div>
      <div class="flex flex-col justify-between col-span-2 lg:col-span-1 lg:justify-center">
         <?php

         if (!empty($category)) {

            $category_color = $category->get_color();

            $default_text_color = 'text-primary-500';
            $text_color         = '';

            if (!empty($category_color)) {
               $text_color         = 'style="color: ' . $category_color . '"';
               $default_text_color = '';
            }

         ?>
            <div>
               <span class="<?php echo $default_text_color; ?> text-xs font-medium uppercase leading-4 grow-line-animation" <?php echo $text_color; ?>>
                  <?php

                  echo esc_html($category->get_name());

                  ?>
               </span>
            </div>
         <?php

         }

         ?>
         <span class="max-w-96 mt-2 text-neutral-700 text-base font-normal leading-6 line-clamp-2 group-hover:underline">
            <?php

            echo esc_html($CD_Post->get_title());

            ?>
         </span>
         <?php

         if ($CD_Post->get_date()) {

         ?>
            <span class="mt-auto text-neutral-500 text-xs font-medium uppercase leading-4 lg:mt-3">
               <?php

               echo esc_html($CD_Post->get_date());

               ?>
            </span>
         <?php

         }

         ?>
      </div>
   </a>
</div>
