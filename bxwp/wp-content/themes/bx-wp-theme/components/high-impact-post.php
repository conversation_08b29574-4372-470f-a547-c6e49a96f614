<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\Category\Utils;
use Canais<PERSON><PERSON>tais\Category\Category;
use CanaisD<PERSON>tais\CD_Post\CD_Post;

$high_impact_post_id = (int) $args['post_id'];

if (!empty($high_impact_post_id)) {
   global $excluded_posts_ID;

   $excluded_posts_ID[] = $high_impact_post_id;

   $high_impact_post = new CD_Post($high_impact_post_id);

   $category_id = $args['category_id'] ?? 0;

   if (!empty($category_id)) {
      $category = new Category($category_id);
   }

   $post_format            = !empty($category_id) ? $category->get_slug() : $high_impact_post->get_format('key');
   $post_format_name       = !empty($category_id) ? $category->get_name() : $high_impact_post->get_format('name');
   $post_type_archive_link = !empty($category_id) ? $category->get_link() : Utils::get_default_category_url($post_format);

   $high_impact_post_thumb = $high_impact_post->get_thumbnail_url('1536x1536');

   if ($post_format === 'event' && !empty($high_impact_post->get_event_start_date())) {
      $formatted_date = $high_impact_post->get_event_start_date();
   } else {
      $formatted_date = $high_impact_post->get_date();
   }

?>
   <section class=" bg-neutral-800 lg:py-12">
      <div class="lg:container">
         <div class="py-24 grid grid-cols-1 bg-cover bg-center lg:grid-cols-2 lg:px-10 px-4 min-h-[400px]" style="background-image: url('<?php echo $high_impact_post_thumb ?>')">
            <div class="py-[0.25rem] px-4 inline-flex flex-col self-center bg-black bg-opacity-50 backdrop-blur-lg lg:py-[0.25rem] lg:px-8">
               <?php

               if (!empty($post_format_name)) {

               ?>

                  <a href="<?php echo $post_type_archive_link; ?>" class="font-medium uppercase text-primary-500">
                     <?php echo esc_html($post_format_name); ?>
                  </a>

               <?php

               }

               ?>
               <a href="<?php echo $high_impact_post->get_link(); ?>" class="mt-2 text-4xl text-white tracking-tight">
                  <?php echo $high_impact_post->get_title(); ?>
               </a>
               <?php

               if (!empty($formatted_date)) {

               ?>
                  <time class="mt-4 text-neutral-400" datetime="<?php echo $formatted_date; ?>">
                     <?php echo $formatted_date; ?>
                  </time>
               <?php

               }

               ?>
            </div>
         </div>
      </div>
   </section>
<?php

}
