<?php

use CanaisDigitais\CD_Post\Utils as PostUtils;
use Canais<PERSON>igitais\Author\Author;

if (!defined('ABSPATH')) {
   exit;
}

$author_id = get_field('block_author_id');

if (empty($author_id)) {
   return;
}

$Author           = new Author($author_id);
$show_most_recent = get_field('block_show_most_recent');
$articles         = get_field('block_articles');
$current_post_id  = get_the_ID() ?  get_the_ID() : '';

if ($show_most_recent !== true) {
   $articles = $articles ? array_column(get_field('block_articles'), 'block_single_article') : [];
} else {

   $articles = PostUtils::get_latest_articles_by_author_term_id($author_id, 3, [$current_post_id]);
}

if (empty($articles)) {
   return;
}

?>
<div class="container font-primary my-10 py-4 max-w-[69rem] border-y border-neutral-200">
   <div class="flex flex-col gap-6 lg:gap-7">
      <div>
         <?php

         echo sprintf(
            '<span class="text-neutral-500 text-xs font-medium uppercase leading-5">%s <b class="text-neutral-700">%s</b></span>',
            __('Leia mais de', 'canais_digitais'),
            esc_html($Author->get_name())
         );

         ?>
      </div>
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-3">
         <?php

         foreach ($articles as $article) {

            get_component('simple-horizontal-compact-card', [
               'cd_post'           => $article,
               'wrapper_classes'   => 'h-[6.5rem] flex',
               'thumbnail_classes' => 'flex w-[6.5rem] h-full aspect-square max-w-none grayscale',
            ]);
         }

         ?>
      </div>
   </div>
</div>
