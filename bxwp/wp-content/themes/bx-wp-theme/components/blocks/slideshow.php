<?php

use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

$slides = get_field('block_slides');

if (empty($slides)) {
   return;
}

$unique_id    = wp_unique_id('cd-block-slideshow_');
$count_slides = count($slides);

if ($count_slides === 1) {
   $ad_slide_target = 1;
} else {
   $ad_slide_target = (int) ceil($count_slides / 2);
}
array_splice($slides, $ad_slide_target, 0, [['is_ad' => true]]);
$count_slides++;

?>
<div>
   <div class="block-slider-slideshow-wrapper flex flex-col justify-center items-center pt-12 pb-14 default-animation">
      <div class="hidden close-button-wrapper w-full justify-end mb-4">
         <button class="close-button justify-center items-center p-3 bg-white bg-opacity-20 rounded-3xl backdrop-blur-xl aspect-square cursor-pointer">
            <span class="block icon-[ph-x] bg-white"></span>
         </button>
      </div>
      <div id="<?php echo esc_attr($unique_id); ?>" class="block-slider-slideshow">
         <div class="block-slider-controls">
            <div class="fullscreen-toggle-wrapper w-full flex justify-end z-[3] p-4">
               <button class="fullscreen-toggle inline-flex justify-center items-center p-3 bg-white bg-opacity-20 rounded-3xl backdrop-blur-xl aspect-square cursor-pointer">
                  <span class="block icon-[ph-corners-out] bg-white"></span>
               </button>
            </div>
            <div class="block-slider-arrows w-full flex items-center justify-between gap-3 z-[2] px-4 sm:mt-[3rem] xl:mt-[5rem]">
               <div class="block-slider-button-prev inline-flex justify-center items-center p-3 bg-white bg-opacity-20 rounded-3xl shadow-md  drop-shadow-md backdrop-blur-xl aspect-square">
                  <span class="block-slider-button-arrow block icon-[ph-caret-left] bg-white"></span>
               </div>
               <div class="block-slider-button-next inline-flex justify-center items-center p-3 bg-white bg-opacity-20 rounded-3xl shadow-md drop-shadow-md backdrop-blur-xl aspect-square">
                  <span class="block-slider-button-arrow block icon-[ph-caret-right] bg-white"></span>
               </div>
            </div>
         </div>
         <div class="swiper-wrapper">
            <?php

            foreach ($slides as $key => $slide) {
               if (isset($slide['is_ad'])) {
            ?>
                  <div class="swiper-slide">
                     <div class="w-full flex flex-col gap-4">
                        <div class="slide-title flex gap-3 items-center">
                           <div class="inline-flex items-center justify-center px-3 py-2 bg-neutral-200 text-neutral-700 pagination-bg-style">
                              <span class="text-xs font-medium leading-none">
                                 <?php

                                 echo ($key + 1) . '/' . $count_slides;

                                 ?>
                              </span>
                           </div>
                           <div>
                              <span class=" text-neutral-700 text-base font-normal leading-6 line-clamp-1 slide-text-style">
                                 <?php

                                 esc_html_e('Propaganda', 'canais_digitais');

                                 ?>
                              </span>
                           </div>
                        </div>
                        <div class="slide-image">
                           <div class="block-slideshow-image">
                              <?php

                              new Ad('gallery_interstitial', 'ad-bg-placeholder');

                              ?>
                           </div>
                        </div>
                     </div>
                  </div>
               <?php

                  continue;
               }

               $slide_title       = $slide['block_single_slide_title'];
               $slide_description = $slide['block_single_slide_description'];
               $slide_image_id    = $slide['block_single_slide_image'];

               ?>
               <div class="swiper-slide">
                  <div class="w-full flex flex-col gap-4">
                     <div class="slide-title flex gap-3 items-center">
                        <div class="inline-flex items-center justify-center px-3 py-2 bg-neutral-200 text-neutral-700 pagination-bg-style">
                           <span class="text-xs font-medium leading-none">
                              <?php

                              echo ($key + 1) . '/' . $count_slides;

                              ?>
                           </span>
                        </div>
                        <div>
                           <span class="text-neutral-700 text-base font-normal leading-6 line-clamp-1 slide-text-style">
                              <?php

                              echo esc_html($slide_title);

                              ?>
                           </span>
                        </div>
                     </div>
                     <div class="slide-image">
                        <?php

                        echo wp_get_attachment_image($slide_image_id, 'large', '',  [
                           'class' => ' block-slideshow-image'
                        ]);

                        ?>
                     </div>
                     <?php

                     if (!empty($slide_description)) {

                     ?>
                        <div class="slide-description">
                           <?php

                           echo wp_kses_post($slide_description);

                           ?>
                        </div>
                     <?php

                     }

                     ?>
                  </div>
               </div>
            <?php

            }

            ?>
         </div>
      </div>
   </div>
</div>
