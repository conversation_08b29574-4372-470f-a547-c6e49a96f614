<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\CD_Post\CD_Post;

$CD_Post = new CD_Post();

if ($CD_Post->has_thumbnail()) {

?>
   <figure class="flex flex-col mb-8 w-full max-w-3xl mx-auto">
      <?php

      echo $CD_Post->get_thumbnail(
         'medium_large',
         [
            'class'      => 'max-h-[25rem] aspect-[77/40] object-cover',
            'has_retina' => true
         ]
      );

      $caption = $CD_Post->get_thumbnail_caption();
      if (!empty($caption)) {

      ?>
         <figcaption class="mt-4">
            <?php echo $caption; ?>
         </figcaption>
      <?php

      }

      ?>
   </figure>
<?php

}

?>
