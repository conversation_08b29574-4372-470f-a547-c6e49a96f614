<?php

if (!defined('ABSPATH')) {
   exit;
}

$socials = isset($args['socials']) ? $args['socials'] : [];

if (empty($socials)) {
   return;
}

$style = isset($args['style']) ? $args['style'] : 'light';

$common_wrapper_classes = 'group flex justify-center items-center rounded-full default-animation ';
$common_icon_classes    = 'w-auto h-5 ';

if ($style === 'light') {

   $classes      = $common_wrapper_classes . 'border border-neutral-200  w-10 h-10 p-3 hover:bg-neutral-200';
   $icon_classes = 'fill-neutral-700';
} elseif ($style == 'dark') {

   $classes      = $common_wrapper_classes . 'w-12 h-12 p-2 bg-neutral-700 rounded-full hover:bg-white';
   $icon_classes = 'fill-white group-hover:fill-neutral-700';
} elseif ($style == 'white') {

   $classes      = $common_wrapper_classes . 'bg-white border border-neutral-200  w-11 h-11 p-3';
   $icon_classes = 'fill-neutral-700';
} else {

   return;
}

?>
<nav>
   <ul class="flex gap-1">
      <?php

      foreach ($socials as $social) {

      ?>
         <li>
            <a class="<?php echo esc_attr($classes); ?>" target="_blank" href="<?php echo esc_url($social['url']); ?>">
               <?php

               render_svg('social-media/' . $social['icon_name'], $icon_classes);

               ?>
            </a>
         </li>
      <?php

      }

      ?>
   </ul>
</nav>
