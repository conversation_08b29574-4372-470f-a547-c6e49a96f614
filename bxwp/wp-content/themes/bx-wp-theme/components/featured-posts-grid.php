<?php

use Canais<PERSON>igitais\CD_Post\CD_Post;
use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

if (empty($args['posts_list'])) {
   return;
}

$ad_name = isset($args['ad_name']) ? $args['ad_name'] : [];

?>
<div class="grid grid-cols-1 lg:grid-cols-[1fr_2fr_1fr] gap-4 mt-8">
   <?php

   foreach ($args['posts_list'] as $key => $item) {

      $CD_Post = new CD_Post($item);

      unset($args['posts_list'][$key]);

      $Category = !empty($args['section_category']) ? $args['section_category'] : $CD_Post->get_category();
   ?>
      <div class="group relative px-4 py-8 overflow-hidden flex items-end <?php echo $key === 1 ? 'row-span-2 h-[11.25rem] lg:h-[37.313rem] lg:px-8' : 'h-[11.25rem] lg:h-[18.125rem]'; ?> <?php echo $key === 4 ? 'col-start-3' : ''; ?>">
         <a href="<?php echo $CD_Post->get_link(); ?>" class="cursor-pointer">
            <div class="absolute inset-0 z-[2] bg-black/48"></div>

            <?php

            if ($CD_Post->has_thumbnail()) {
               echo $CD_Post->get_thumbnail('medium_large', [
                  'class' => 'absolute h-full w-full object-cover object-center inset-0 group-hover:backdrop-blur-md group-hover:blur-md default-animation'
               ]);
            }

            ?>
            <div class="relative z-[2] flex flex-col justify-end">
               <?php

               if (!empty($Category)) {

               ?>
                  <span class="mb-2 text-xs uppercase font-medium text-white">
                     <?php echo $Category->get_name(); ?>
                  </span>
               <?php

               }

               if (!empty($CD_Post->get_title())) {

               ?>
                  <span class="cursor-pointer mb-4 text-white line-clamp-2 group-hover:underline <?php echo $key === 1 ? 'text-base leading-6 lg:text-[2rem] lg:leading-10' : 'text-base lg:text-xl'; ?>">
                     <?php echo $CD_Post->get_title(); ?>
                  </span>
               <?php

               }

               if (!empty($CD_Post->get_date())) {

               ?>

                  <span class="text-white text-xs font-medium uppercase leading-5">
                     <?php echo $CD_Post->get_date(); ?>
                  </span>

               <?php

               }

               ?>

            </div>
         </a>
      </div>
   <?php

   }

   if (!empty($ad_name)) {

      foreach ($ad_name as $ad) {

         new Ad(
            $ad,
            'ad-bg-placeholder h-[11.25rem] lg:h-[18.125rem]'
         );
      }
   }

   ?>
</div>
