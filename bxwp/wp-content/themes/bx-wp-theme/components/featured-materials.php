<?php

use Canais<PERSON><PERSON>tais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\Theme_Settings\Utils;
use Canais<PERSON>igitais\Category\Category;
use CanaisDigitais\Category\Utils as CategoryUtils;

if (!defined('ABSPATH')) {
   exit;
}

$category_ID = $args['category_id'] ?? 0;

$featured_materials_data = [];

if (!empty($category_ID)) {
   $Category                = new Category($category_ID);
   $featured_materials_data = CategoryUtils::get_special_page_featured_materials($category_ID);
} else {
   $featured_materials_data = Utils::get_homepage_featured_materials_data();
}

if (!empty($featured_materials_data)) {

?>
   <div class="container py-6">
      <?php

      get_component('featured-posts-grid', [
         'posts_list' => array_slice($featured_materials_data['materials'], 0, 3),
         'ad_name'    => ['300_1_lft', '300_1_rht']
      ]);

      $remaining = array_slice($featured_materials_data['materials'], 3, !empty($featured_materials_data['download']) ? 3 : 4);

      if (!empty($featured_materials_data['download'])) {
         array_unshift($remaining, $featured_materials_data['download']);
      }

      if (!empty($remaining)) {

      ?>
         <h3 class="mt-12 pt-4 border-t border-neutral-300 text-xl text-neutral-700">
            <?php

            echo esc_html($featured_materials_data['title']);

            ?>
         </h3>
         <div class="grid grid-cols-1 lg:grid-cols-[1fr_2fr_1fr] gap-4 mt-8">
            <?php

            foreach ($remaining as $key => $item) {
               $CD_Post = new CD_Post($item);

               if ($key === 0 && !empty($featured_materials_data['download'])) {
                  get_component('download-material-cta', [
                     'download' => $featured_materials_data['download'],
                     'classes'  => 'mt-3 gap-8 order-last lg:order-first lg:mt-0 lg:h-[597px] lg:gap-0',
                  ]);
               } else {

            ?>
                  <div class="hidden group relative px-8 py-8 h-[290px] overflow-hidden items-end <?= ($key === 0 || $key === 2) ? 'row-span-2 h-[597px]' : ''; ?> <?= $key > 3 ? 'post-remove-ads' : ''; ?> lg:flex">
                     <a href="<?php echo $CD_Post->get_link(); ?>" class="cursor-pointer">
                        <div class="absolute inset-0 z-[2] bg-black/48"></div>
                        <?php

                        if ($CD_Post->has_thumbnail()) {
                           echo $CD_Post->get_thumbnail('medium_large', [
                              'class' => 'absolute w-full h-full object-cover object-center inset-0 group-hover:backdrop-blur-md group-hover:blur-md default-animation'
                           ]);
                        }

                        ?>
                        <div class="relative z-[2] flex flex-col justify-end">
                           <?php

                           if (!empty($CD_Post->get_category())) {

                           ?>
                              <span class="mb-2 text-xs uppercase font-medium text-white">
                                 <?php echo $CD_Post->get_category()->get_name(); ?>
                              </span>
                           <?php

                           }

                           if (!empty($CD_Post->get_title())) {

                           ?>
                              <span class="mb-4 text-[1.6rem] 2xl:text-[2rem] text-white group-hover:underline cursor-pointer">
                                 <?php echo $CD_Post->get_title(); ?>
                              </span>
                           <?php

                           }

                           if (!empty($CD_Post->get_date())) {

                           ?>
                              <div class="text-white text-xs font-medium uppercase leading-5">
                                 <?php echo $CD_Post->get_date(); ?>
                              </div>
                           <?php

                           }

                           ?>
                        </div>
                     </a>
                  </div>
                  <?php

                  get_component('simple-horizontal-compact-card', [
                     'cd_post'           => $item,
                     'wrapper_classes'   => 'h-[6.5rem] flex lg:hidden',
                     'thumbnail_classes' => 'w-[6.5rem] aspect-square max-w-none',
                  ]);

                  ?>
            <?php

               }
            }

            ?>
         </div>
      <?php

      }

      ?>
   </div>
<?php

}
