<?php

if (!defined('ABSPATH')) {
   exit;
}

$menu_locations = get_nav_menu_locations();
$footer_logo = get_field('footer_logo', 'general-options');
$footer_text = get_field('footer_text', 'general-options');
$footer_copyright_text = get_field('footer_copyright_text', 'general-options');

do_action('get_footer', null, []);

?>
</main>

<footer class="bg-neutral-800 py-14 mt-4">
   <div class="container">
      <div class="grid grid-cols-1 gap-x-4 lg:grid-cols-2">
         <div>
            <?php
            if (!empty($footer_logo)) {

            ?>
               <a href="<?php echo esc_url(home_url()); ?>">
                  <img class="h-12 w-auto" src="<?php echo esc_url($footer_logo); ?>" alt="">
               </a>
            <?php

            }


            if (!empty($footer_text)) {
            ?>
               <div class="mb-14">
                  <p class="text-neutral-400 text-sm mt-6 mb-10 max-w-[541px]"><?php echo $footer_text; ?></p>
               </div>
            <?php

            }

            ?>
            <div class="social-icons hidden mb-16 lg:flex <?php echo empty($footer_text) ? 'mt-12' : ''; ?>">
               <?php

               do_action('bx_cd_get_social_media', 'dark');

               ?>
            </div>
         </div>
         <div class="max-w-[37.5rem]">
            <div>
               <?php

               if (has_nav_menu('footer-main-menu')) {

                  $footer_main_menu_items = wp_get_nav_menu_items($menu_locations['footer-main-menu']);

                  if (!empty($footer_main_menu_items)) {

               ?>
                     <span class="block mb-4 text-neutral-500 text-xs uppercase font-medium">
                        <?php esc_html_e('Categorias', 'canais_digitais') ?>
                     </span>
               <?php

                     wp_nav_menu([
                        'menu_id'         => 'footer-main-menu',
                        'theme_location'  => 'footer-main-menu',
                        'container'       => 'nav',
                        'menu_class'      => 'footer-main-menu grid grid-cols-2 gap-x-2 text-white leading-8 lg:grid-cols-3',
                        'container_id'    => 'footer-main-menu',
                        'fallback_cb'     => '__return_empty_string',
                     ]);
                  }
               }

               ?>
            </div>
            <div class="social-icons w-full flex justify-center lg:hidden my-8">
               <?php

               do_action('bx_cd_get_social_media', 'dark');

               ?>
            </div>
         </div>
      </div>
      <div class="flex flex-col gap-4 justify-between items-center border-t border-neutral-700 pt-10 lg:flex-row">
         <?php

         render_svg('logo/informa', 'h-12 w-auto');
         if (!empty($footer_copyright_text)) {

         ?>
            <p class="text-neutral-300 text-center text-sm max-w-[430px] lg:text-left"><?php echo $footer_copyright_text; ?></p>
         <?php

         }

         ?>
      </div>
      <?php

      if (has_nav_menu('footer-secondary-menu')) {

         $footer_secondary_menu_items = wp_get_nav_menu_items($menu_locations['footer-secondary-menu']);

         if (!empty($footer_secondary_menu_items)) {

            wp_nav_menu([
               'menu_id'        => 'footer-secondary-menu',
               'theme_location' => 'footer-secondary-menu',
               'container'      => 'nav',
               'menu_class'     => 'footer-secondary-menu flex flex-wrap justify-center items-center gap-x-8 gap-y-4  mt-12 text-white text-xs uppercase font-medium lg:flex-nowrap lg:gap-8',
               'container_id'   => 'footer-secondary-menu',
               'fallback_cb'    => '__return_empty_string',
            ]);
         }
      }

      ?>
   </div>
</footer>
<?php

wp_footer();

?>
</body>

</html>
