<?php

use CanaisDigitais\Category\Category;
use CanaisDigitais\Tag\Tag;

if (!defined('ABSPATH')) {
   exit;
}

$name               = post_type_archive_title('', false);
$description        = '';
$background_color   = '';
$background_image   = '';
$background_opacity = '';
$text_color         = '';
$show_color         = '';
$parent             = 0;

if (is_category() || is_tag()) {
   $Term               = is_category() ? new Category(get_queried_object()) : new Tag(get_queried_object());
   $name               = $Term->get_name();
   $description        = $Term->get_description();
   $background_color   = $Term->get_color();
   $background_image   = $Term->get_background_image();
   $background_opacity = $Term->get_background_opacity();
   $text_color         = $Term->get_text_color();
   $show_color         = $Term->get_show_color_status();
   $parent             = is_category() ? $Term->get_parent() : 0;
} else if (is_post_type_archive('video')) {
   if (have_rows('videos_archive_header', 'general-options')) {
      while (have_rows('videos_archive_header', 'general-options')) {
         the_row();

         $name               = get_sub_field('title');
         $description        = get_sub_field('description');
         $background_color   = get_sub_field('featured_color');
         $background_image   = get_sub_field('featured_image');
         $background_opacity = get_sub_field('featured_color_with_opacity');
         $text_color         = get_sub_field('text_color');
         $show_color         = get_sub_field('show_color');
      }
   }
}

?>
<div class="relative mt-12 mb-4 bg-cover bg-center" <?php echo $background_image ? "style='background-image: url({$background_image})'" : ''; ?>>
   <?php

   if (!empty($background_opacity)) {

   ?>
      <div class="absolute inset-0" style="background: <?php echo $background_opacity; ?>"></div>
   <?php

   }

   ?>
   <div class="relative container z-[2]">
      <div class="py-16 bg-primary-400 text-center" <?php echo $show_color !== false ? "style='background: {$background_color}'" : ''; ?>>
         <div style="color: <?php echo $text_color; ?>">
            <?php

            if ($parent != 0) {

            ?>
               <div class="flex justify-center mb-5">
                  <?php get_component('breadcrumbs'); ?>
               </div>
            <?php

            }

            ?>
            <h1 class="text-5xl font-medium mb-4 mt-2">
               <?php echo esc_html($name); ?>
            </h1>
            <?php

            if (!empty($description)) {

            ?>
               <div class="flex justify-center">
                  <p class="max-w-2xl text-xl">
                     <?php echo esc_html($description); ?>
                  </p>
               </div>
            <?php

            }

            ?>
         </div>
      </div>
   </div>
</div>
