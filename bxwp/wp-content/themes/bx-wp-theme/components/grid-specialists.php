<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\Author\Author;
use Canais<PERSON><PERSON>tais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\CD_Post\Utils as PostUtils;
use CanaisDigitais\Author\Utils as AuthorUtils;

use CanaisDigitais\Category\Category;
use CanaisDigitais\Category\Utils as CategoryUtils;

$category_ID = $args['category_id'] ?? 0;

if (!empty($category_ID)) {
   $Category = new Category($category_ID);

   $section_title      = $Category->get_special_page_specialists_title();
   $slider_authors     = CategoryUtils::get_special_page_specialists_with_articles($category_ID);
   $most_read_articles = CategoryUtils::get_special_page_most_read_section_articles($category_ID);
} else {
   $section_title      = AuthorUtils::get_homepage_specialists_section_title();
   $slider_authors     = AuthorUtils::get_homepage_specialists_with_articles();
   $most_read_articles = PostUtils::get_homepage_most_read_section_articles();
}

$specialist_archive_url = AuthorUtils::get_specialists_archive_url();

?>
<div class="bg-neutral-200 py-6">
   <div class="flex flex-col container gap-[2.5625rem] lg:gap-[3.25rem]">
      <div class="w-full">
         <?php

         if (!empty($slider_authors)) {

         ?>
            <div class="slider-home-specialists swiper border-t-2 border-neutral-700">
               <div class="flex flex-col justify-between gap-4 lg:gap-[12.25rem] lg:flex-row">
                  <div class="lg:mb-8 w-full">
                     <?php

                     if (!empty($section_title)) {

                     ?>
                        <div class="flex">
                           <?php

                           $title_classes = 'inline px-6 py-4 hover:pr-[10%] transition-all duration-500 ease-linear text-white text-3xl font-normal leading-10 bg-neutral-800';

                           if (!empty($specialist_archive_url)) {

                           ?>
                              <a href="<?php echo esc_url($specialist_archive_url); ?>" class="<?php echo $title_classes; ?>">
                                 <?php echo esc_html($section_title); ?>
                              </a>
                           <?php

                           } else {

                           ?>
                              <span class="<?php echo $title_classes; ?>">
                                 <?php echo esc_html($section_title); ?>
                              </span>
                           <?php

                           }

                           ?>
                        </div>
                     <?php

                     }

                     ?>
                  </div>
                  <div class="flex justify-center gap-3 mt-3 mb-3 lg:mb-0 lg:justify-end">
                     <div class="slider-button slider-button-prev">
                        <span class="slider-button-arrow icon-[ph-caret-left] [mask-size:1.5rem] [mask-position:center]"></span>
                     </div>
                     <div class="slider-button slider-button-next">
                        <span class="slider-button-arrow icon-[ph-caret-right] [mask-size:1.5rem] [mask-position:center]"></span>
                     </div>
                  </div>
               </div>
               <div class="swiper-wrapper">
                  <?php

                  foreach ($slider_authors as $author) {
                     $Author = new Author($author['author_id']);

                     if (empty($author['articles'])) {
                        continue;
                     }

                  ?>
                     <div class="swiper-slide relative">
                        <div class="flex flex-col gap-4">
                           <div class="flex flex-col gap-4 lg:flex-row">
                              <div class="w-full lg:w-1/2">
                                 <a class="block w-full h-full aspect-[343/256]" href="<?php echo esc_url($Author->get_link()); ?>">
                                    <?php

                                    echo $Author->get_avatar('medium_large', [
                                       'class' => 'w-full h-full object-cover'
                                    ]);

                                    ?>
                                 </a>
                              </div>
                              <div class="hidden grid-cols-1 grid-rows-3 w-full gap-4 lg:grid lg:w-1/2">
                                 <?php

                                 foreach ($author['articles'] as $article) {

                                    get_component('simple-horizontal-compact-card', [
                                       'cd_post'           => $article,
                                       'wrapper_classes'   => 'h-[6.5rem]',
                                       'thumbnail_classes' => 'w-[6.5rem] aspect-square max-w-none',
                                    ]);
                                 }

                                 ?>
                              </div>
                           </div>
                           <div>
                              <?php

                              if (!empty($author['featured_article'])) {
                                 $Article = new CD_Post($author['featured_article']);

                              ?>
                                 <div class="w-full">
                                    <a class="flex flex-col text-neutral-700 text-3xl font-normal leading-10 line-clamp-2" href="<?php echo esc_url($Article->get_link()); ?>">
                                       <span class="text-primary-500 text-xl font-normal leading-7">
                                          <?php echo esc_html($Author->get_name()); ?>
                                       </span>
                                       <span class="mb-3">
                                          <?php echo esc_html($Article->get_title()); ?>
                                       </span>
                                       <?php

                                       if ($Article->get_date()) {

                                       ?>
                                          <span class="text-neutral-500 text-xs font-medium uppercase leading-4">
                                             <?php echo esc_html($Article->get_date()); ?>
                                          </span>
                                       <?php

                                       }

                                       ?>
                                    </a>
                                 </div>
                              <?php

                              }

                              ?>
                           </div>
                        </div>
                     </div>
                  <?php

                  }

                  ?>
               </div>
            </div>
         <?php

         }

         ?>
      </div>
      <div>
         <?php

         if (!empty($most_read_articles)) {

         ?>
            <div class="mt-4 mb-8 border-t border-neutral-300 pt-4">
               <span class="text-neutral-700 text-xl font-normal leading-7">
                  <?php esc_html_e('Artigos mais lidos', 'canais_digitais'); ?>
               </span>
            </div>
            <div class="grid gap-6 grid-cols-1 w-full lg:grid-cols-4 lg:gap-4">
               <?php

               foreach ($most_read_articles as $article) {
                  get_component('simple-vertical-card', [
                     'article' => $article,
                  ]);
               }

               ?>
            </div>
         <?php

         }

         ?>

      </div>
   </div>
</div>
