<?php

use Canais<PERSON>igitais\CD_Post\CD_Post;

if (!defined('ABSPATH')) {
   exit;
}

$cd_post  = isset($args['article']) ? $args['article'] : '';

if (empty($cd_post)) {
   return;
}

$CD_Post           = new CD_Post($args['article']);
$date              = isset($args['date']) ? $args['date'] : $CD_Post->get_date();
$thumbnail_classes = isset($args['thumbnail_classes']) ? $args['thumbnail_classes'] : '';

?>
<div class="group">
   <a class="flex gap-4 lg:flex-wrap" href="<?php echo esc_url($CD_Post->get_link()); ?>">
      <?php

      $has_thumbnail = $CD_Post->has_thumbnail();
      $category      = $CD_Post->get_category();

      if ($has_thumbnail) {
         echo $CD_Post->get_thumbnail('medium', [
            'class' => 'object-cover aspect-square w-[6.5rem] h-[6.5rem] lg:w-full lg:h-[12.625rem]' . $thumbnail_classes
         ]);
      }

      ?>
      <div class="flex flex-col">
         <?php

         if (!empty($category)) {

         ?>
            <div>
               <span class="mt-2 text-primary-500 text-xs font-medium uppercase leading-4 grow-line-animation before:bg-primary-500">
                  <?php echo esc_html($category->get_name()); ?>
               </span>
            </div>
         <?php

         }

         ?>
         <span class="max-w-96 text-neutral-700 text-base font-normal leading-6 line-clamp-2 group-hover:underline lg:text-xl lg:leading-7">
            <?php echo esc_html($CD_Post->get_title()); ?>
         </span>
         <?php

         if (!empty($date)) {

         ?>
            <span class="<?php echo $has_thumbnail ? '' : 'pt-4'; ?> mt-auto text-neutral-500 text-xs font-medium uppercase leading-4 lg:mt-3">
               <?php echo esc_html($date); ?>
            </span>
         <?php

         }

         ?>
      </div>
   </a>
</div>
