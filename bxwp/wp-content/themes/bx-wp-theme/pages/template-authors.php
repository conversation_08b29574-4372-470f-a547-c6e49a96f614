<?php

/* Template Name: Especialistas */

use <PERSON><PERSON><PERSON><PERSON>tais\Author\Author;
use Canais<PERSON><PERSON>tais\Ad\Ad;
use Canais<PERSON><PERSON>tais\Author\Utils as AuthorUtils;
use CanaisDigitais\CD_Post\Utils as PostUtils;
use <PERSON>ais<PERSON><PERSON>tais\Utils;

if (!defined('ABSPATH')) {
   exit;
}

get_component('header');

$paged          = Utils::current_page();
$posts_per_page = 10;

$specialist_naming = AuthorUtils::get_custom_specialist_naming('plural');
$description       = AuthorUtils::get_template_authors_description();

if ($paged === 1) {
   $authors  = AuthorUtils::get_template_authors_chosen_specialists();
}

?>
<div class="bg-neutral-100 pt-12">
   <div class="container">
      <div class="flex flex-col">
         <div class="px-4 py-16 bg-neutral-200">
            <div class="flex flex-col gap-4 text-center max-w-[41rem] mx-auto">
               <h1 class="font-secondary text-neutral-700 text-4xl font-medium leading-10 tracking-wide lg:text-5xl">
                  <?php

                  echo esc_html($specialist_naming);

                  ?>
               </h1>
               <?php

               if (!empty($description)) {

               ?>
                  <span class="text-neutral-700 text-base font-normal leading-7 lg:text-xl">
                     <?php

                     echo esc_html($description);

                     ?>
                  </span>
               <?php

               }

               ?>
            </div>
         </div>
         <div>
            <?php

            if (!empty($authors) && $paged === 1) {

            ?>
               <div class="grid grid-cols-1 gap-4 mt-12 pb-16 border-b border-neutral-200 lg:grid-cols-4 lg:gap-x-4 lg:gap-y-12">
                  <?php

                  foreach ($authors as $author) {

                     $Author = new Author($author);

                  ?>
                     <div class="group">
                        <a class="flex gap-2 lg:flex-col lg:gap-0" href="<?php echo esc_url($Author->get_link()) ?>">
                           <div class="relative">
                              <div class="flex-col gap-2 items-center justify-center hidden absolute w-full h-full z-[2] group-hover:lg:flex">
                                 <span class=" bg-white icon-[ph-plus] [mask-size:1.5rem] [mask-position:center]"></span>
                                 <span class=" text-white text-xs font-medium uppercase leading-none">
                                    <?php

                                    esc_html_e('Ver Matérias', 'canais_digitais');
                                    ?>
                                 </span>
                              </div>
                              <div class="w-full h-full overflow-hidden lg:bg-black">
                                 <?php

                                 echo $Author->get_avatar('thumbnail', [
                                    'class' => 'w-[92px] h-[98px] max-w-none object-cover default-animation group-hover:lg:blur-xl group-hover:lg:opacity-30 lg:aspect-[320/344] lg:w-full lg:h-full lg:max-w-full'
                                 ]);

                                 ?>
                              </div>
                           </div>
                           <div class="flex flex-col justify-between">
                              <div class="lg:mt-4">
                                 <span class="text-primary-500 text-base font-normal leading-7 line-clamp-1 lg:text-xl">
                                    <?php

                                    echo esc_html($Author->get_name());

                                    ?>
                                 </span>
                              </div>
                              <?php

                              if (!empty($Author->get_description())) {

                              ?>
                                 <div class="mt-2">
                                    <span class="text-neutral-700 text-sm font-normal leading-normal line-clamp-3 lg:line-clamp-2 lg:text-base">
                                       <?php

                                       echo esc_html($Author->get_description());

                                       ?>
                                    </span>
                                 </div>
                              <?php

                              }

                              ?>
                           </div>
                        </a>
                     </div>
                  <?php

                  }

                  ?>
               </div>
            <?php

            }

            ?>
         </div>
         <?php

         $most_read_articles       = PostUtils::get_most_read_articles($posts_per_page, 30, [], $paged);
         $count_most_read_articles = $most_read_articles->found_posts;

         if ($count_most_read_articles > 0) {

         ?>
            <div class="mb-[7.5rem]">
               <div class="mb-8 pt-4">
                  <span class="text-neutral-700 text-xl font-normal leading-7">
                     <?php

                     esc_html_e('Artigos mais lidos', 'canais_digitais');

                     ?>
                  </span>
               </div>
               <div class="flex flex-col gap-4 lg:grid lg:grid-cols-4">
                  <?php

                  foreach ($most_read_articles->posts as $key => $article) {

                     if ($key === 3) {
                        new Ad('template-authors-side_left', 'ad-bg-placeholder');
                     }

                     if ($key === 7) {

                        new Ad('template-authors-middle_content', 'col-span-4 hidden lg:ad-bg-placeholder');
                     }

                     get_component('simple-vertical-card', [
                        'article' => $article,
                     ]);
                  }

                  ?>
               </div>
               <div class="mt-12">
                  <?php

                  $result_pages = $count_most_read_articles / $posts_per_page;
                  $remainder    = $count_most_read_articles % $posts_per_page;

                  if ($remainder > 0) {
                     $result_pages += 1;
                  }

                  get_component('pagination', [
                     'total' => $result_pages,
                     'paged' => $paged
                  ]);

                  ?>
               </div>
            </div>
         <?php

         }

         ?>
      </div>
   </div>
</div>
<?php

get_component('footer');

?>
