<?php

use <PERSON>ais<PERSON>igitais\Author\Author;
use CanaisDigitais\CD_Post\Utils;
use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

$author = isset($args['author']) ? $args['author'] : null;

if (is_null($author)) {
   return;
}

$Author = new Author($author);

$paged          = get_query_var('paged');
$paged          = $paged === 0 ? 1 : $paged;
$articles_per_page = 11;

$featured_articles  = $Author->get_featured_articles();
$all_articles       = Utils::get_latest_articles_by_author_term_id($Author->term_ID, -1, $featured_articles, $articles_per_page, $paged);
$count_all_articles = $all_articles->found_posts;

?>
<div class="flex flex-col gap-4">
   <div class="">

      <?php

      get_component('author-bar', [
         'is_archive' => true,
      ]);
      ?>

   </div>
   <div class="container">

      <?php

      if (!empty($featured_articles) && $paged === 1) {

      ?>
         <div class="grid grid-cols-1 grid-rows-3 gap-4 lg:grid-cols-2 lg:grid-rows-2">
            <div class="lg:row-span-2">
               <?php

               get_component('article-card-overlay', [
                  'article'       => $featured_articles[0] ?? '',
                  'size_class' => 'w-full aspect-[343/180] lg:aspect-[656/420]',

               ]);

               ?>
            </div>
            <div>
               <?php

               get_component('article-card-overlay', [
                  'article'       => $featured_articles[1] ?? '',
                  'size_class' => 'w-full aspect-[343/180] lg:aspect-[656/420] lg:hidden',

               ]);

               get_component('article-card', [
                  'article_id'           => $featured_articles[1] ?? '',
                  'style'             => 'vertical',
                  'wrapper_classes'   => 'hidden lg:block',
                  'size_class'        => 'h-[6.5rem] w-auto aspect-square lg:h-auto lg:aspect-[320/202]',
                  'thumbnail_classes' => 'w-[6.5rem]'
               ]);

               ?>
            </div>
            <div class="lg:col-start-2 lg:row-start-2">
               <?php

               get_component('article-card-overlay', [
                  'article'       => $featured_articles[2] ?? '',
                  'size_class' => 'w-full aspect-[343/180] lg:aspect-[656/420] lg:hidden',

               ]);

               get_component('article-card', [
                  'article_id'           => $featured_articles[2] ?? '',
                  'style'             => 'vertical',
                  'wrapper_classes'   => 'hidden lg:block',
                  'size_class'        => 'h-[6.5rem] w-auto aspect-square lg:h-auto lg:aspect-[320/202]',
                  'thumbnail_classes' => 'w-[6.5rem]'
               ]);

               ?>
            </div>
         </div>
      <?php

      }

      ?>
      <div>
         <?php

         if ($count_all_articles > 0) {




         ?>
            <div class="mt-12 mb-[7.5rem] border-t border-neutral-200">
               <div class="mb-8 pt-4">
                  <span class="text-neutral-700 text-xl font-normal leading-7">
                     <?php

                     echo sprintf(
                        '%s %s',
                        esc_html('Todos os artigos de', 'canais_digitais'),
                        esc_html($Author->get_name())
                     );

                     ?>
                  </span>
               </div>
               <div class="flex flex-col gap-4 lg:grid lg:grid-cols-4">
                  <?php

                  foreach ($all_articles->posts as $key => $article) {

                     if ($key === 3) {

                        new Ad('300_1_rht', 'ad-bg-placeholder');
                     }

                     if ($key === 7) {

                        new Ad('300_1_lft', 'col-span-4 hidden lg:ad-bg-placeholder');
                     }

                     get_component('simple-vertical-card', [
                        'article' => $article,

                     ]);
                  }

                  ?>
               </div>
               <div class="mt-12">
                  <?php

                  $result_pages = $count_all_articles / $articles_per_page;
                  $remainder    = $count_all_articles % $articles_per_page;

                  if ($remainder > 0) {
                     $result_pages += 1;
                  }

                  get_component('pagination', [
                     'total' => $result_pages,
                     'paged' => $paged
                  ]);

                  ?>
               </div>
            </div>
         <?php

         }

         ?>
      </div>
   </div>
</div>
</div>
<div>
   <?php

   new Ad('728_10_a');

   ?>
</div>
<?php
