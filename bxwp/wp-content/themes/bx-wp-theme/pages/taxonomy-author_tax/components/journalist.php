<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON>igitais\Ad\Ad;
use Canais<PERSON>igitais\Author\Author;

$Author = new Author();

?>
<div class="container my-8">

   <div class="h-32 bg-primary-500 flex items-center justify-center">
      <h1 class="text-white text-2xl">
         <?php

         if (!empty($Author->get_name())) {
            echo esc_html($Author->get_name());
         } else {
            echo get_the_author();
         }

         ?>
      </h1>
   </div>

</div>
<?php

global $wp_query;

if (!$wp_query->have_posts()) {
   return;
}

$posts_list = $wp_query->posts;

?>

<main>

   <div class="container">

      <div class="text-xl font-normal leading-7 text-neutral-700 mb-8 pt-4 border-t border-neutral-300">

         <?php echo esc_html('Todas as matérias por'); ?>

         <span class="text-primary-500">
            <?php

            if (!empty($Author->get_name())) {
               echo esc_html($Author->get_name());
            } else {
               echo get_the_author();
            }

            ?>
         </span>

      </div>

      <div class="flex flex-wrap justify-start gap-4">
         <?php

         foreach ($posts_list as $post_index => $post_object) {

            echo <<<DIV
            <div class="w-full lg:w-[calc(25%-12px)]">
            DIV;

            get_component('article-card', [
               'article_id' => $post_object,
               'style'      => 'horizontal',
               'size_class' => 'w-[6.5rem] h-[6.5rem] aspect-square lg:w-full lg:h-60 lg:aspect-[320/208]',
            ]);


            echo <<<DIV
         </div>
         DIV;

            if ($post_index === 11) {
               echo <<<DIV
                  <div class="w-full py-9">
                  <div class="flex w-full h-full justify-center items-center text-black">
                  DIV;

               new Ad('category-middle_content');

               echo <<<DIV
                  </div>
               </div>
            DIV;
            }
         }

         ?>
      </div>


      <section class="mt-[3.25rem] mb-[7.5rem]">
         <?php get_component('pagination'); ?>
      </section>

   </div>

</main>
