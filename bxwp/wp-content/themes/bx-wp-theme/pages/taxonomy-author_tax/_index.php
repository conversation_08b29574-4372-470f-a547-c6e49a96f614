<?php

use CanaisDigitais\Author\Author;
use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

$Author = new Author();
$author_type = $Author->get_author_type();

get_component('header');

?>
<div>
   <?php

   new Ad('728_1_a', 'my-4');

   ?>
</div>
<?php

if ($author_type === 'specialist') {

   get_page_component('taxonomy-author_tax', 'specialist', [
      'author' => $Author->term_ID,
   ]);
} else {

   get_page_component('taxonomy-author_tax', 'journalist');
}
?>
<div>
   <?php

   new Ad('footnote');

   ?>
</div>
<?php

get_component('footer');
