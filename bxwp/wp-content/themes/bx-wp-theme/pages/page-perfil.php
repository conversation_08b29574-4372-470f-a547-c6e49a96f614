<?php

namespace CanaisDigitais\Ad;

if (!defined('ABSPATH')) {
   exit;
}

get_component('header');
?>

<div class="container my-8">

   <div class="h-32 bg-primary-500 flex items-center justify-center">

      <h1 class="text-white text-2xl">
         <?php

         esc_html_e('Meu Perfil', 'canais-digitais')

         ?>
      </h1>

   </div>

</div>


<div class="flex flex-col gap-4 pb-36">
   <div>
      <?php

      new Ad('page-profile-top');

      ?>
   </div>


   <div class="container">

      <div class="inset-x-0 m-auto max-w-4xl">

         <div class="flex justify-between items-center border-b pb-6 mb-8">
            <span class="text-neutral-700 text-xl font-normal leading-7">
               <?php

               esc_html_e('Detalhes da Conta', 'canais-digitais')

               ?>
            </span>
            <button js-profile-edit class="button button-primary">
               <?php

               esc_html_e('Editar', 'canais-digitais')

               ?>
            </button>
         </div>


         <form action="" id="profile-form">

            <div class="mb-10 grid grid-cols-2 gap-5">

               <div>
                  <label for="first_name" class="mb-1 block">Nome</label>
                  <input type="text" name="first_name" id="first_name" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
               </div>

               <div>
                  <label for="last_name" class="mb-1 block">Sobrenome</label>
                  <input type="text" name="last_name" id="last_name" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
               </div>

            </div>


            <div class="my-8">
               <label for="business_email" class="mb-1 block">Email</label>
               <input type="email" name="business_email" id="business_email" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>


            <div>
               <label for="name" class="mb-1 block">Localização</label>
               <input type="text" name="state" id="state" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>


            <div class="my-8">
               <span class="text-neutral-700 text-xl font-normal leading-7">Informações pessoais</span>
            </div>

            <div class="mb-10">
               <label for="name" class="mb-1 block">Empresa</label>
               <input type="text" name="company" id="company" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>

            <div class="mb-10">
               <label for="name" class="mb-1 block">Cargo</label>
               <input type="text" name="job_title" id="job_title" value="" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>

            <div class="mb-10">
               <label for="name" class="mb-1 block">Tipo de Negócio</label>
               <input type="text" name="business_type" id="business_type" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>

            <div class="mb-10">
               <label for="name" class="mb-1 block">Função</label>
               <input type="text" name="job_function" id="job_function" class="block border w-full py-2 px-4 bg-neutral-100 outline-none">
            </div>

            <button js-profile-save class="button button-primary">
               <?php

               esc_html_e('Salvar Informações', 'canais-digitais')

               ?>
            </button>

         </form>


      </div>

   </div>

   <div>
      <?php

      new Ad('page-profile-side_left');

      get_component('footer');

      ?>
   </div>
</div>
