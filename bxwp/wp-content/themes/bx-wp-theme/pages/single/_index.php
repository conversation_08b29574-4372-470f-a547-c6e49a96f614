<?php

use Canais<PERSON>igitais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\Ad\Ad;
use Canais<PERSON><PERSON>tais\CD_Post\Utils as PostUtils;

if (!defined('ABSPATH')) {
   exit;
}

$single  = new CD_Post();
$post_format = $single->get_format();

$is_download_material = (bool) $single->has_download_material();

get_component('header');

if ($single->has_gallery()) {

   new Ad('gallery_728_a');
} else {

   new Ad('728_1_a');
}

get_component('author-bar');

if ($post_format === 'video') {

?>
   <div class="pb-12">
      <?php get_page_component('single', 'video-embed'); ?>
   </div>
<?php

}

?>
<div class="container">
   <?php

   get_page_component('single', 'top');

   if ($post_format !== 'video' && $post_format !== 'podcast') {
      get_component('featured-image');
   }

   if ($is_download_material) {
      get_page_component(
         'single',
         'download-paywall',
         [
            'article_id' => $single->get_the_ID(),
            'file_id'    => $single->get_download_material_file_id()
         ]
      );
   }

   if ($post_format === 'podcast') {

   ?>
      <div class="pb-12 max-w-[41rem] inset-x-0 mx-auto">
         <?php get_page_component('single', 'podcast-embed'); ?>
      </div>
   <?php

   }

   get_page_component('single', 'content');

   ?>
</div>

<?php

if ($is_download_material) {
   get_page_component(
      'single',
      'download-paywall',
      [
         'article_id' => $single->get_the_ID(),
         'file_id'    => $single->get_download_material_file_id()
      ]
   );
}

get_component('horizontal-post-list', [
   'list_title' => esc_html__('Notícias relacionadas', 'canais_digitais'),
   'posts_ID'   => $single->get_related_posts_IDs()
]);

new Ad('728_10_a');

new Ad('adhesion');

new Ad('footnote');

get_component('footer');
