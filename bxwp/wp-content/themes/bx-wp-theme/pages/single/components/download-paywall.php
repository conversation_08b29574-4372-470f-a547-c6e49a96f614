<?php

use Canais<PERSON>igitais\CD_Post\Utils as PostUtils;
use CanaisDigitais\Form\Utils as FormUtils;

if (!defined('ABSPATH')) {
   exit;
}

if (!FormUtils::is_lead_form_enabled()) {
   return;
}

bx_sntls_Utils::load_class('Form');

if (!class_exists('bx_sntls_Form')) {
   return;
}

$article_id = isset($args['article_id']) ? $args['article_id'] : null;
$file_upload = isset($args['file_id']) ? $args['file_id'] : null;

if ($article_id === null || $file_upload === null) {
   return;
}

?>
<section class="py-2 mb-3 cta-download-single">
   <div class="container">
      <div class="flex justify-center">
         <div class="relative max-w-[48rem] px-[2rem] py-[1.2rem] bg-primary-10 text-center rounded-[1.25rem] shadow-[0_4px_4px_rgba(0,0,0,0.3)]">
            <?php

            get_component('loading-spinner', [
               'classes' => 'z-[2]',
            ]);

            ?>
            <div x-cloak x-data="newsletterSignUpCheck" x-show="!isCookieSet">

               <div class="flex flex-col lg:flex-row items-center gap-5">
                  <div class="w-full lg:w-8/12">
                     <h2 class="lg:text-2xl text-xl text-neutral-700 font-medium lg:text-start text-center mb-1">
                        <?php

                        esc_html_e('Acesse conteúdo exclusivo!', 'canais-digitais');

                        ?>
                     </h2>
                     <p class="text-neutral-600 lg:text-start text-center max-w-2xl">
                        <?php

                        esc_html_e('Cadastre-se e acesse informações, estudos e conteúdos premium do setor.', 'canais-digitais');

                        ?>
                     </p>
                  </div>
                  <div class="flex lg:justify-end justify-center w-full lg:w-4/12">
                     <a href="<?php echo esc_url(PostUtils::get_download_material_signup_link()); ?>" class="ga-formulario-lead-btn-download button btn button-primary button-with-icon group register-button !px-4 !py-2">
                        <span class="text-sm icon-[ph-download-simple] [mask-size:1.4rem] [mask-position:center] text-white group-hover:text-primary-500">
                        </span>
                        <?php

                        esc_html_e('Baixar Material', 'canais-digitais');

                        ?>
                     </a>
                  </div>
               </div>

               <div class="hidden mt-10 mb-8 border border-neutral-300"></div>

            </div>
            <div x-cloak x-data="newsletterSignUpCheck" x-show="isCookieSet" class="download-box relative">

               <div class="flex flex-col lg:flex-row items-center gap-5">
                  <div class="w-full lg:w-8/12">
                     <h2 class="lg:text-2xl text-xl text-neutral-700 font-medium lg:text-start text-center mb-1">
                        <?php

                        esc_html_e('Acesse conteúdo exclusivo!', 'canais-digitais');

                        ?>
                     </h2>
                     <p class="text-neutral-600 lg:text-start text-center max-w-2xl">
                        <?php

                        esc_html_e('Agora você tem acesso a conteúdos exclusivos preparados especialmente para você.', 'canais-digitais');

                        ?>
                     </p>
                  </div>
                  <div class="flex lg:justify-end justify-center w-full lg:w-4/12">
                     <button id="download-button" class="download-button-link group button-with-icon button-primary !px-4 !py-2" data-file-id="<?php echo esc_attr($file_upload); ?>">
                        <span class="text-sm icon-[ph-download-simple] [mask-size:1.4rem] [mask-position:center] text-white group-hover:text-primary-500">
                        </span>
                        <?php

                        esc_html_e('Baixar material', 'canais-digitais');

                        ?>
                     </button>
                  </div>
               </div>

            </div>
         </div>
      </div>
   </div>
</section>
