<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON>igitais\Ad\Ad;
use CanaisDigitais\CD_Post\CD_Post;

$single_post = new CD_Post();

?>
<div class="flex flex-col justify-center">
   <div>
      <?php

      $authors = $single_post->get_authors();
      $byline  = $single_post->get_byline();

      ?>
      <div class="flex items-center gap-1 max-w-[41rem] mx-auto mb-9 uppercase text-xs font-medium">
         <?php

         if (!empty($authors) || !empty($byline)) {
            if (!empty($authors)) {

         ?>
               <span>
                  <?php

                  esc_html_e('Por', 'canais_digitais');

                  ?>
               </span>
               <span class="text-primary-500"><?php echo $authors; ?></span>
            <?php

            } else {

            ?>
               <span>
                  <?php

                  echo esc_html($single_post->get_byline());

                  ?>
               </span>
         <?php
            }

            echo '-';
         } else {

            esc_html_e('Publicado em', 'canais_digitais');
         }

         ?>
         <span><?php echo $single_post->get_date(); ?></span>
      </div>

   </div>
   <div class="flex flex-col gap-4 lg:flex-row min-h-full">
      <div class="flex flex-col justify-center items-center">
         <div class="w-75 flex items-center justify-center h-full">
            <?php

            new Ad('300_1_lft');

            ?>
         </div>
      </div>
      <div class="post-content max-w-3xl mx-auto">
         <?php

         echo $single_post->get_content();

         $tags = $single_post->get_post_terms();

         if (!empty($tags)) {

         ?>
            <div>
               <p class="uppercase text-neutral-500 text-xs font-medium mb-3 mt-12">
                  <?php esc_html_e('Tags', 'canais-digitais'); ?>
               </p>

               <div class="flex flex-wrap gap-3 mb-14">
                  <?php

                  foreach ($tags as $tag) {

                  ?>
                     <a href="<?php echo get_term_link($tag); ?>" rel="nofollow" class="py-2 px-4 border uppercase border-neutral-500 text-neutral-500 text-xs font-medium transition-all hover:bg-neutral-500 hover:text-white">
                        <?php echo $tag->name; ?>
                     </a>
                  <?php

                  }

                  ?>
               </div>
            </div>

         <?php

         }

         ?>
      </div>
      <div class="flex flex-col justify-center items-center">
         <div class="w-75 flex items-center justify-center h-full">
            <?php

            if ($single_post->has_gallery()) {
               new Ad('gallery_300_right');
            } else {
               new Ad('300_1_rht');
            }

            ?>
         </div>
      </div>
   </div>
   <?php

   $ad_list = [
      'nativekey_1_lft',
      'nativekey_2_lft',
      'nativekey_13_1',
      'nativekey_12_1',
      'adhesion'
   ];

   ?>
   <div class="container flex flex-col lg:flex-row lg:justify-center">
      <?php

      foreach ($ad_list as $ad) {
         new Ad($ad);
      }

      ?>
   </div>
   <?php

   new Ad('728_2_a');

   ?>
</div>
