<?php

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

global $wp_query;

if (!$wp_query->have_posts()) {
   return;
}

$posts_list = $wp_query->posts;
$tag = new Category(get_queried_object());

?>

<div class="text-xl font-normal leading-7 text-neutral-700 mb-8 pt-4 border-t border-neutral-300">
   Todas as matérias para “<span class="text-primary-500"><?php echo $tag->get_name(); ?></span>”
</div>

<div class="flex flex-wrap justify-start gap-4">
   <?php

   foreach ($posts_list as $post_index => $post_object) {

      echo <<<DIV
      <div class="w-full lg:w-[calc(25%-12px)]">
      DIV;

      get_component('article-card', [
         'article_id' => $post_object,
         'style'      => 'horizontal',
         'size_class' => 'w-[6.5rem] h-[6.5rem] aspect-square lg:w-full lg:h-60 lg:aspect-[320/208]',
      ]);

      echo <<<DIV
         </div>
      DIV;

      if ($post_index === 3) {

         echo '<div class="w-full lg:w-[calc(25%-12px)]">';

         new Ad('300_1_rht', 'ad-bg-placeholder');

         echo '</div>';
      }

      if ($post_index === 10) {
         echo <<<DIV
               <div class="w-full py-9">
                  <div class="flex w-full h-full justify-center items-center text-black">
         DIV;

         new Ad('728_10_a');

         echo <<<DIV
                  </div>
               </div>
         DIV;
      }
   }

   ?>
</div>
<?php

$ad_list = [
   'nativekey_1_lft',
   'nativekey_2_lft',
   'nativekey_3',
   'nativekey_8',
   'nativekey_10',
];
?>
<div class="container flex flex-col lg:flex-row lg:justify-center">
   <?php

   foreach ($ad_list as $ad) {
      new Ad($ad);
   }

   ?>
</div>
