<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;
use CanaisDigitais\CD_Post\Utils;
use CanaisDigitais\Utils as CD_Utils;

$Category            = new Category(get_queried_object());
$category_ads        = ['300_1_rht', '300_2_rht'];
$categories_sections = Utils::category_archive_get_child_section_posts($Category->get_the_ID());

global $wp_query;

if (!empty($wp_query->have_posts())) {

?>
   <div class="container flex flex-col gap-12 mb-8">
      <?php

      if (CD_Utils::current_page(true)) {
         get_component('featured-posts-grid', [
            'posts_list' => array_slice($wp_query->posts, 0, 4),
            'ad_name'    => ['300_1_lft']
         ]);
      }

      ?>
      <section>
         <div class="pb-8">
            <hr class="pb-4 w-full text-neutral-300">
            <span class="text-xl	font-normal leading-7 text-neutral-700">
               <?php esc_html_e('Todas as matérias', 'canais-digitais') ?>
            </span>
         </div>

         <?php get_page_component('category', 'all-posts-grid', ['posts_offset' => 4]); ?>
      </section>
   </div>

   <section class="mt-[3.25rem] mb-[7.5rem]">
      <?php get_component('pagination'); ?>
   </section>
<?php

}

?>
<div class="container flex flex-col lg:flex-row lg:justify-center">
   <?php

   $ad_list = [
      'nativekey_1_lft',
      'nativekey_2_lft',
      'nativekey_3',
      'nativekey_8',
      'nativekey_10',
   ];

   foreach ($ad_list as $ad) {
      new Ad($ad);
   }

   ?>
</div>
<?php

if (CD_Utils::current_page(true) && !empty($categories_sections)) {

?>
   <div class="container flex flex-col gap-12 mb-8">
      <?php

      foreach ($categories_sections as $key => $category_section) {

         get_component('grid-category', [
            'section' => $category_section,
            'ad_name' => $category_ads[$key] ?? null,
         ]);
      }

      ?>
   </div>
<?php

}

new Ad('728_10_a');
