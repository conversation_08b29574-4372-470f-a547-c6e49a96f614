<?php

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

$middle_col_posts = $args['middle_col_posts'] ?? [];

?>
<div class="flex flex-col lg:grid grid-cols-1 gap-4 grid-flow-col lg:grid-rows-3 lg:grid-cols-4">
   <?php

   if (!empty($args['middle_col_posts'])) {
      foreach ($args['middle_col_posts'] as $col_post) {
         if (empty($col_post)) {
            continue;
         }

   ?>
         <div class="col-span-2">
            <?php

            get_component('article-card', [
               'article_id'       => $col_post,
               'style'            => 'vertical',
               'size_class'       => 'w-[6.5rem] h-[6.5rem] aspect-square lg:w-full lg:h-60 lg:aspect-[320/208]',
               'section_category' => new Category(get_queried_object()),
            ]);

            ?>
         </div>
   <?php

      }
   }

   ?>
   <div class="col-span-1 row-span-3">
      <?php

      new Ad(
         '300_1_lft',
         'ad-bg-placeholder flex flex-col gap-4 w-full'
      );

      ?>
   </div>
</div>
