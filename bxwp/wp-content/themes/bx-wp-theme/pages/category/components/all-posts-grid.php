<?php

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;
use CanaisDigitais\Utils;

if (!defined('ABSPATH')) {
   exit;
}

global $wp_query;

if (!$wp_query->have_posts()) {
   return;
}

$is_first_page = Utils::current_page(true);

$posts_list = $is_first_page ? array_slice($wp_query->posts, $args['posts_offset']) : $wp_query->posts;

?>

<div class="grid grid-cols-1 gap-4 lg:grid-cols-4">
   <?php

   foreach ($posts_list as $post_index => $post_object) {

      if ($is_first_page && $post_index === 6) {
         echo <<<DIV
            <div class="col-span-1 flex w-full h-full justify-center items-center py-8 lg:col-span-4">
         DIV;

         new Ad('728_10_a');

         echo <<<DIV
            </div>
         DIV;
      } else if (!$is_first_page && $post_index === 4) {

         echo <<<DIV
         <div class="col-span-1 flex w-full h-full justify-center items-center py-8 lg:col-span-4">
      DIV;

         new Ad('728_10_a');

         echo <<<DIV
         </div>
      DIV;
      } else if ($is_first_page && $post_index === 3) {
         echo <<<DIV

               <div class="w-full row-span-2">
                  <div class="flex w-full h-full justify-center items-center text-black">
         DIV;

         new Ad('300_1_rht', 'ad-bg-placeholder');

         echo <<<DIV
                  </div>
               </div>
         DIV;
      }

      get_component('article-card', [
         'article_id'       => $post_object ?? '',
         'style'            => 'horizontal',
         'size_class'       => 'w-[6.5rem] h-[6.5rem] aspect-square lg:w-full lg:h-60 lg:aspect-[320/208]',
         'section_category' => is_category() ? new Category(get_queried_object()) : null,
      ]);
   }

   ?>
</div>
