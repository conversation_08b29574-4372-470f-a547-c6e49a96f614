<?php

use <PERSON>ais<PERSON><PERSON>tais\Ad\Ad;
use Canais<PERSON><PERSON>tais\Ad\Utils as Ad_Utils;

if (!defined('ABSPATH')) {
   exit;
}

global $wp_query;

if (empty($wp_query->have_posts())) {
   return;
}

?>
<div class="container">
   <section>
      <div class="grid grid-cols-1 gap-4 lg:grid-cols-4">
         <?php

         for ($i = 0; $i < 7; $i++) {
            if (empty($wp_query->posts[$i])) {
               continue;
            }

            if ($i === 6) {

         ?>
               <div class="flex flex-col justify-center block h-[26.26rem] h-[unset] lg:aspect-[320/420]
    ">
                  <?php

                  new Ad('300_1_rht', 'ad-bg-placeholder');

                  ?>
               </div>
            <?php
            } else {

               get_component('simple-video-card', [
                  'video'                      => $wp_query->posts[$i],
                  'is_wide'                    => $i === 0 ? true : false,
                  'additional_wrapper_classes' => $i > 0 ? 'h-[unset] lg:aspect-[320/420]' : 'col-span-1 lg:col-span-2',
               ]);
            }
            ?>

         <?php

         }

         ?>
      </div>
   </section>
   <section class="pt-4">
      <?php

      if (!empty($wp_query->posts[6])) {

      ?>
         <div class="pt-20 pb-8">
            <hr class="pb-4 w-full text-neutral-300">

            <span class="text-xl	font-normal leading-7 text-neutral-700">
               <?php esc_html_e('Assista à outras matérias', 'canais-digitais') ?>
            </span>
         </div>
      <?php

      }

      ?>
      <div class="flex flex-wrap gap-4">
         <?php

         for ($i = 6; $i < count($wp_query->posts); $i++) {
            if ($i === 10) {
               new Ad('300_1_lft', 'w-full');
            }

         ?>
            <div class="w-full lg:w-[calc(25%-15px)]">
               <?php

               get_component('simple-video-card', [
                  'video' => $wp_query->posts[$i],
                  'is_wide'  => $i === 0 ? true : false,
                  'additional_wrapper_classes' => $i > 0 ? 'h-[unset] lg:aspect-[320/420]' : '',
               ]);

               ?>

            </div>
         <?php

         }

         ?>
      </div>
   </section>
   <section class="mt-[3.25rem] mb-[7.5rem]">
      <?php

      get_component('pagination');

      ?>
   </section>
</div>
<?php
