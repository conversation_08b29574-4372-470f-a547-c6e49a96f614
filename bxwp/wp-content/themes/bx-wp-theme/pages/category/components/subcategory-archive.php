<?php

use Canais<PERSON>igitais\Category\Category;
use CanaisDigitais\Utils as CD_Utils;
use Canais<PERSON>igitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

global $wp_query;

if (empty($wp_query->have_posts())) {
   return;
}

$Category = new Category(get_queried_object());

?>
<div class="container">
   <?php

   if (CD_Utils::current_page(true)) {

   ?>

      <section>
         <?php

         get_component('featured-posts-grid', [
            'posts_list'       => array_slice($wp_query->posts, 0, 4),
            'section_category' => $Category,
            'ad_name'          => ['300_1_rht']
         ]);

         ?>
      </section>

      <section class="mt-16">
         <div class="mb-8">
            <hr class="pb-4 w-full text-neutral-300">

            <span class="text-xl	font-normal leading-7 text-neutral-700">
               <?php esc_html_e('Todas as matérias', 'canais-digitais') ?>
            </span>
         </div>
         <?php

         get_page_component('category', 'subcategory-posts-with-ebook-grid', [
            'middle_col_posts' => array_slice($wp_query->posts, 4, 6),
         ]);

         ?>
      </section>

   <?php

   }

   ?>
   <div class="container flex flex-col lg:flex-row lg:justify-center">
      <?php

      $ad_list = [
         'nativekey_1_lft',
         'nativekey_2_lft',
         'nativekey_3',
         'nativekey_8',
         'nativekey_10',
      ];

      foreach ($ad_list as $ad) {
         new Ad($ad);
      }

      ?>
   </div>
   <section class="mt-8">
      <?php

      get_page_component('category', 'all-posts-grid', [
         'posts_offset' => 10,
      ]);

      ?>
   </section>
   <div>
      <?php

      new Ad('728_10_a');

      ?>
   </div>
   <section class="mt-[3.25rem] mb-[7.5rem]">
      <?php get_component('pagination'); ?>
   </section>
</div>
