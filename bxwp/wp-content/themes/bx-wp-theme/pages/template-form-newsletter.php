<?php

/* Template Name: Form de Newsletter */

use CanaisDigitais\CD_Post\Utils as PostUtils;
use CanaisDigitais\Form\Utils as FormUtils;



if (!defined('ABSPATH')) {
   exit;
}

get_component('header');

$form_background = FormUtils::get_header_background();

?>
<div>
   <?php

   get_component('loading-spinner');

   ?>
   <div class="relative bg-[url('../images/template-form-pattern.png')] bg-repeat bg-neutral-100 after:content-[''] after:absolute after:bottom-0 after:w-full after:h-[28rem] after:bg-gradient-to-t after:from-white after:from-75% after:to-transparent">

      <div class="template-form-newsletter-header absolute z-[5] w-full h-[28rem] bg-primary-300 overflow-hidden">
         <?php

         if (!empty($form_background)) {

         ?>
            <picture>
               <img class="w-full h-full object-cover bg-no-repeat" src="<?php echo esc_url($form_background['url']); ?>" alt="<?php echo esc_attr($form_background['alt']); ?>" title="<?php echo esc_attr($form_background['title']); ?>">
            </picture>
         <?php

         }

         ?>
      </div>

      <div class="container">
         <div class="form-newsletter relative z-10 mx-auto max-w-[53.875rem] pt-8 pb-4 lg:pt-16 lg:pb-8">
            <div class="form-successful-message flex flex-col justify-center hidden">
               <?php

               render_svg('feature-available', 'inline-block m-auto mb-5');

               ?>
               <h2 class="text-xl text-neutral-700 mb-5 text-center">
                  <?php

                  echo esc_html__('Cadastro concluído!', 'canais-digitais');

                  ?>
               </h2>
               <p class="text-neutral-600 mb-8 text-center">
                  <?php

                  echo esc_html__('Sua assinatura foi realizada com sucesso.', 'canais-digitais');

                  ?>
               </p>
            </div>
            <div class="form-download-message flex flex-col justify-center hidden">
               <?php

               render_svg('feature-available', 'inline-block m-auto mb-5');

               ?>
               <h2 class="text-xl text-neutral-700 mb-5 text-center">
                  <?php

                  echo esc_html__('Seu download iniciará em instantes!', 'canais-digitais');

                  ?>
               </h2>
               <p class="text-neutral-600 mb-8 text-center">
                  <?php

                  echo esc_html__('Se isso não acontecer, clique no botão abaixo para começar manualmente.', 'canais-digitais');

                  ?>
               </p>
               <div class="flex justify-center">
                  <a href="#" class="group direct-download-button button-with-icon button-primary">
                     <span class="text-sm icon-[ph-download-simple] [mask-size:1.4rem] [mask-position:center] text-white group-hover:text-primary-500">
                     </span>
                     <?php

                     echo esc_html__('Baixar documento', 'canais-digitais');

                     ?>
                  </a>
               </div>
            </div>
            <div class="form-newsletter-content text-center">
               <div class="flex flex-col gap-[1.375rem] justify-center items-center">
                  <div class="flex flex-col gap-[1.125rem] items-center">
                     <div class="flex md:flex-row flex-col justify-center items-center gap-3">
                        <?php

                        $download_content = isset($_GET['ContentName']) ? $_GET['ContentName'] : null;
                        $form_title = FormUtils::get_form_title($download_content);
                        $form_icon = FormUtils::get_form_icon((bool) $download_content);

                        if (!empty($form_icon)) {
                           echo $form_icon;
                        }

                        if (!empty($form_title)) {

                        ?>
                           <h2 class="text-[2rem] text-white ">
                              <?php echo $form_title; ?>
                           </h2>
                        <?php

                        }

                        ?>
                     </div>
                     <?php

                     $form_description = FormUtils::get_form_description((bool) $download_content);

                     if (!empty($form_description)) {

                     ?>
                        <p class="text-xl	text-white">
                           <?php echo $form_description; ?>
                        </p>
                     <?php

                     }

                     ?>
                  </div>

                  <div>
                     <div class="form-wrapper text-left relative">
                        <?php

                        echo FormUtils::get_form_by_title('Formulário Lead');

                        ?>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>

   <div class="relative container">

      <?php

      get_component('horizontal-post-list', [
         'list_title' => esc_html__('Últimas Notícias', 'canais_digitais'),
         'posts_ID'   => PostUtils::get_latest_articles_ids()
      ]);

      ?>
   </div>
</div>
<?php

get_component('footer');

?>
