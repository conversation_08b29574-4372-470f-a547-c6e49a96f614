<?php

use CanaisDigitais\Category\Utils;

if (!defined('ABSPATH')) {
   exit;
}

$request_string = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
$chosen_category = isset($_GET['cat']) ? sanitize_text_field($_GET['cat']) : null;

if ($chosen_category !== null) {
   if (strpos($chosen_category, ',') !== false) {
      list($chosen_category, $chosen_default_category) = explode(',', $chosen_category, 2);
      $chosen_category = trim($chosen_category);
      $chosen_default_category = trim($chosen_default_category);
   } else {
      $chosen_default_category = $chosen_category;
   }
} else {
   $chosen_default_category = null;
}

$chosen_author = isset($_GET['author_tax']) ? sanitize_text_field($_GET['author_tax']) : null;
$chosen_order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : null;

if (!empty($request_string)) {
   $chosen_category = $chosen_category ?? 0;
   $chosen_author = $chosen_author ?? 0;
}
?>

<div>

   <form action="<?php echo esc_url(site_url('/')); ?>" id="search_form" method="get">

      <div class="flex justify-center px-4">
         <div class="flex justify-between items-center mb-12 pb-4 max-w-[55rem] w-full mt-24">

            <input type="search" placeholder="Digite sua busca..." value="<?php echo $request_string ?>" name="s" title="Digite sua busca" class="w-full pb-2.5 text-[2rem] md:text-5xl text-neutral-700 placeholder-neutral-400 bg-transparent outline-none border-0 border-b border-neutral-300 focus:border-b-2 focus:border-neutral-700 focus:ring-0 active:ring-0 active:border-b-2 active:border-neutral-700" />

            <button type="submit">
               <?php render_svg('search'); ?>
            </button>

         </div>
      </div>

      <div class="container">

         <div class="mb-4 text-neutral-700">
            <?php echo esc_html_e('Filtrar resultados', 'canais_digitais'); ?>
         </div>

         <div class="flex justify-between items-center flex-col md:flex-row pb-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 w-full lg:w-1/2 gap-3 md:gap-4 ">

               <?php
               $default_categories_ids = Utils::get_default_categories_ids();
               $terms_args = [
                  'taxonomy' => 'category',
                  'orderby'  => 'name',
                  'exclude'  => array_merge([1], $default_categories_ids),
               ];
               $terms = get_terms($terms_args);
               if (!empty($terms)) {
               ?>

                  <select name="cat" id="category_all" class="custom-select">

                     <option value="" disabled selected>
                        <?php echo esc_html_e('Categoria', 'canais_digitais'); ?>
                     </option>

                     <option value=""><?php echo esc_html_e('Todas', 'canais_digitais'); ?></option>

                     <?php
                     $category_options = [];
                     foreach ($terms as $category) {
                        $category_options[] = ['value' => esc_attr($category->term_id), 'name' => esc_attr($category->name)];
                     }
                     foreach ($category_options as $category_option) {
                     ?>
                        <option value="<?php echo $category_option['value']; ?>" <?php echo selected($chosen_category ?? null, $category_option['value']); ?>>
                           <?php echo $category_option['name']; ?>
                        </option>
                     <?php
                     }
                     ?>

                  </select>

               <?php } ?>

               <?php
               $author_terms = [
                  'taxonomy' => 'author_tax',
                  'orderby' => 'name',
               ];
               $author_terms = get_terms($author_terms);
               if (!empty($author_terms)) {
               ?>

                  <select name="author_tax" class="custom-select">

                     <option value="" disabled selected>
                        <?php echo esc_html_e('Autor', 'canais_digitais'); ?>
                     </option>

                     <option value=""><?php echo esc_html_e('Todos', 'canais_digitais'); ?></option>

                     <?php
                     $author_options = [];
                     foreach ($author_terms as $author) {
                        $author_options[] = ['value' => esc_attr($author->slug), 'name' => esc_attr($author->name)];
                     }
                     foreach ($author_options as $author_option) {
                     ?>
                        <option value="<?php echo $author_option['value']; ?>" <?php echo selected($chosen_author ?? null, $author_option['value']); ?>>
                           <?php echo $author_option['name']; ?>
                        </option>
                     <?php
                     }
                     ?>

                  </select>
               <?php } ?>

               <?php
               $default_categories_ids = Utils::get_default_categories_ids();
               $terms_args = [
                  'taxonomy' => 'category',
                  'include' => $default_categories_ids,
               ];
               $terms = get_terms($terms_args);
               ?>

               <select name="category_name" id="category_default" class="custom-select">

                  <option value="" disabled selected>
                     <?php echo esc_html_e('Tipo de Publicação', 'canais_digitais'); ?>
                  </option>

                  <option value=""><?php echo esc_html_e('Todos', 'canais_digitais'); ?></option>

                  <?php
                  $category_options = [];
                  foreach ($terms as $category) {
                     $category_options[] = ['value' => esc_attr($category->slug), 'name' => esc_attr($category->name)];
                  }
                  foreach ($category_options as $category_option) {
                  ?>

                     <option value="<?php echo $category_option['value']; ?>" <?php echo selected($chosen_default_category ?? null, $category_option['value']); ?>>
                        <?php echo $category_option['name']; ?>
                     </option>

                  <?php
                  }
                  ?>
               </select>

            </div>

            <div class="w-full lg:w-52 mt-3 md:mt-0">

               <select name="order" class="custom-select">
                  <option value="DESC" disabled selected><?php echo esc_html_e('Ordenar', 'canais_digitais'); ?></option>
                  <?php
                  $order_options = [
                     'DESC' => esc_html__('Mais recentes', 'canais_digitais'),
                     'ASC' => esc_html__('Mais antigos', 'canais_digitais'),
                  ];
                  foreach ($order_options as $key => $option) {
                  ?>
                     <option value="<?php echo $key; ?>" <?php echo selected($chosen_order, $key); ?>>
                        <?php echo $option; ?>
                     </option>
                  <?php
                  }
                  ?>
               </select>

            </div>
         </div>
      </div>
   </form>
</div>
