<?php

use CanaisDigitais\Ad\Ad;

if (!defined('ABSPATH')) {
   exit;
}

global $wp_query;

if (!$wp_query->have_posts()) {
   return;
}

$posts_list = $wp_query->posts;

$search_term = sanitize_text_field($_GET['s']);

?>
<div class="border-t border-neutral-300 pb-32">
   <div>
      <?php new Ad('search-top', 'my-6') ?>
   </div>
   <div class="container">

      <?php if (!empty($search_term)) { ?>

         <div class="leading-7 text-neutral-700 mb-8 pt-12">

            <?php echo $wp_query->found_posts; ?>
            <?php echo esc_html_e('resultados para', 'canais_digitais'); ?>
            <span class="text-primary-500">“<?php echo $search_term; ?>”</span>

         </div>

      <?php } ?>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 <?= empty($search_term) ? 'mt-4 md:mt-8' : ''; ?>">
         <?php

         foreach ($posts_list as $post_index => $post_object) {

            if ($post_index === 4) {

               new Ad('300_1_lft', 'ad-bg-placeholder col-span-1');
            }

            echo <<<DIV
         <div class="w-full">
         DIV;

            get_component('article-card', [
               'article_id' => $post_object,
               'style'      => 'horizontal',
               'size_class' => 'w-[6.5rem] h-[6.5rem] aspect-square lg:w-full lg:h-60 lg:aspect-[320/208]',
            ]);

            echo <<<DIV
      </div>
      DIV;
         }

         ?>
      </div>

      <section class="mt-[4.25rem] mb-[7.5rem]">
         <?php get_component('pagination'); ?>
      </section>

   </div>

</div>
