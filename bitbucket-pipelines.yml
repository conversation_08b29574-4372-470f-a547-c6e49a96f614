image: alpine
clone:
  depth: 1

definitions:
  steps:
    - step: &build
        name: Building assets
        image: node:18.16.0-slim
        script:
          - npm i && npm run assets
          - rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/
          - rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/typescript/
        artifacts:
          - bxwp/**

    - step: &deploy-aws
        name: Deploying to AWS
        image: ubuntu:latest
        clone:
          enabled: false
        script:
          - apt-get update -qqy && apt-get upgrade -qqy
          - apt-get install -qqy curl unzip
          - curl https://rclone.org/install.sh | bash
          - rclone config create remote sftp host=${AWS_BX_HOST} user=${AWS_BX_USER} key_file=/opt/atlassian/pipelines/agent/ssh/id_rsa_tmp
          - rclone copyto bxwp/bx-env-all.php remote:${PATH_DIR}/bx-env-all.php
          - rclone copyto bxwp/${ENV_FILE} remote:${PATH_DIR}/${ENV_FILE}
          - rclone sync --delete-after bxwp/wp-content/themes/bx-wp-theme remote:${PATH_DIR}/wp-content/themes/bx-wp-theme -v -u --check-first --modify-window=5m

    - step: &deploy-github
        name: Deploying to GitHub
        image: ubuntu:latest
        clone:
          depth: full
        script:
          - apt-get -qqy update && apt-get -qqy upgrade
          - apt-get -qqy install openssh-client git
          - ssh-keyscan github.com >> ~/.ssh/known_hosts
          - git config --global user.email "<EMAIL>"
          - git config --global user.name "Raphael Mirai"
          - cp /opt/atlassian/pipelines/agent/ssh/id_rsa_tmp ~/.ssh/id_rsa
          - chmod 600 ~/.ssh/id_rsa
          - git config core.sshCommand 'ssh -i ~/.ssh/id_rsa'
          - git remote add github-repo **************:Informa-Unity/infra-live-latam-port-dev-front.git
          - git remote set-url github-repo **************:Informa-Unity/infra-live-latam-port-dev-front.git
          - git push github-repo --verbose ${BITBUCKET_BRANCH}:stage

    - step: &send-notify
        name: Sending notify
        clone:
          enabled: false
        script:
          - pipe: atlassian/email-notify:0.13.1
            variables:
              USERNAME: ${SMTP_USER}
              PASSWORD: ${SMTP_PASS}
              FROM: ${SMTP_USER}
              TO: ${SMTP_TO}
              HOST: ${SMTP_HOST}
              SUBJECT: "[${BITBUCKET_REPO_SLUG}] Ambiente ${BITBUCKET_DEPLOYMENT_ENVIRONMENT} atualizado"
              BODY_PLAIN: 'Endereço atualizado:<br><a href="https://${DOMAIN}">https://${DOMAIN}</a><br><br>Branch:<br>${BITBUCKET_BRANCH}<br><br>Pipeline:<br><a href="https://bitbucket.org/${BITBUCKET_REPO_FULL_NAME}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}">build#${BITBUCKET_BUILD_NUMBER}</a><br><br>Repositório:<br><a href="https://bitbucket.org/${BITBUCKET_REPO_FULL_NAME}">${BITBUCKET_REPO_SLUG}</a>'

pipelines:
  branches:
    dev:
      - stage:
          name: Deploying to Test
          deployment: Test
          steps:
            - step: *build
            - step: *deploy-aws

    stage:
      - stage:
          name: Deploying to Staging
          deployment: Staging
          steps:
            - step: *deploy-github
            - step: *send-notify
