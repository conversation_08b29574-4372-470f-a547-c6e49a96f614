version: '3.8'

services:
  wordpress:
    image: buildbox/canais_digitais-wordpress
    container_name: wordpress
    build:
      context: .
      dockerfile: deploy/env-local/Dockerfile
    ports:
      - 8000:80
      - 4430:443
    depends_on:
      - database
    environment:
      - WP_ENVIRONMENT_TYPE=staging
      - WP_SITE_URL=http://localhost:8000
      - WP_DB_NAME=canais_digitais
      - WP_DB_USER=wordpress_user
      - WP_DB_PASSWORD=wordpress_password
      - WP_DB_HOST=database

  database:
    image: mariadb
    container_name: database
    ports:
      - 3309:3306
    environment:
      - MYSQL_DATABASE=canais_digitais
      - MYSQL_USER=wordpress_user
      - MYSQL_PASSWORD=wordpress_password
      - MYSQL_ROOT_PASSWORD=root

  database-cli:
    image: phpmyadmin
    container_name: database-cli
    ports:
      - 8080:80
    environment:
      - PMA_ARBITRARY=1
