<?php

$config = new PhpCsFixer\Config();
return $config->setRiskyAllowed(true)->setIndent('   ')
   ->setRules([
      '@PhpCsFixer'                             => true,
      'array_push'                              => true,
      'ereg_to_preg'                            => true,
      'no_unset_cast'                           => false,
      'no_alias_functions'                      => true,
      'final_internal_class'                    => true,
      'no_php4_constructor'                     => true,
      'ordered_interfaces'                      => true,
      'self_static_accessor'                    => true,
      'non_printable_character'                 => true,
      'modernize_types_casting'                 => true,
      'ordered_traits'                          => true,
      'self_accessor'                           => true,
      'control_structure_braces'                => false,
      'simplified_if_return'                    => true,
      'implode_call'                            => true,
      'no_useless_sprintf'                      => true,
      'dir_constant'                            => true,
      'function_to_constant'                    => true,
      'get_class_to_class_keyword'              => true,
      'logical_operators'                       => true,
      'ternary_to_null_coalescing'              => true,
      'blank_line_after_opening_tag' => false,
      'concat_space'                            => ['spacing' => 'one'],
      'list_syntax'                             => ['syntax' => 'long'],
      'increment_style'                         => ['style' => 'post'],
      'class_definition'                        => ['space_before_parenthesis' => false],
      'control_structure_continuation_position' => ['position' => 'same_line'],
      'empty_loop_body'                         => ['style' => 'braces'],
      'no_unneeded_curly_braces'                => ['namespaces' => false],
      'binary_operator_spaces'                  => ['default' => 'align_single_space'],
      'trailing_comma_in_multiline'             => [
         'elements' => ['arrays', 'arguments', 'parameters']
      ],
      'class_attributes_separation' => [
         'elements' => [
            'const'        => 'one',
            'method'       => 'one',
            'property'     => 'only_if_meta',
            'trait_import' => 'none',
            'case'         => 'none'
         ]
      ],
      'function_declaration' => [
         'closure_function_spacing'   => 'none',
         'closure_fn_spacing'         => 'none',
         'trailing_comma_single_line' => false,
      ],
      'ordered_imports' => [
         'sort_algorithm' => 'alpha',
         'imports_order' => ['class', 'function', 'const'],
      ],
      'blank_line_before_statement' => [
         'statements' => ['break', 'if', 'continue', 'declare', 'do', 'exit', 'foreach', 'switch', 'return', 'throw', 'try', 'phpdoc', 'while']
      ],
      'no_extra_blank_lines' => [
         'tokens' => ['attribute', 'break', 'case', 'continue', 'curly_brace_block', 'default', 'extra', 'parenthesis_brace_block', 'return', 'square_brace_block', 'switch', 'throw', 'use', 'use_trait']
      ],
      'yoda_style' => [
         'equal' => true,
         'identical' => true,
         'less_and_greater' => null,
         'always_move_variable' => true
      ],
      'class_definition' => [
         'single_item_single_line' => true,
         'single_line' => true
      ]
   ]);
