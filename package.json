{"name": "bx-wp-theme", "private": true, "config": {"jsFiles": ["all", "admin"]}, "scripts": {"start": "npm install --silent && composer install --quiet && npm run assets", "css": "npm run css-all && npm run css-admin", "css-all": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/all/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/all.min.css --minify", "css-all-watch": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/all/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/all.min.css --minify --watch", "css-admin": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/admin/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/admin.min.css --minify", "css-admin-watch": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/admin/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/admin.min.css --minify --watch", "js": "wp-scripts build", "js-watch": "wp-scripts start", "assets": "npm run css && npm run js"}, "keywords": [], "version": "3.0.0", "author": "Buildbox", "license": "MIT", "description": "Assets handlers for theme.", "devDependencies": {"@iconify-json/ph": "^1.2.2", "@jcamp/tailwindcss-plugin-icons": "^0.6.2", "@tailwindcss/forms": "^0.5.9", "@wordpress/scripts": "^26.6.0", "swiper": "^11.0.2", "tailwindcss": "^3.4.16"}}