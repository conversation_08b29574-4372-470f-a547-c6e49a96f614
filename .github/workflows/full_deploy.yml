name: Full_Deploy

on:
    push:
        branches:
            - stage
            - main
            - prod
            - dev

jobs:
    deploy:
        name: 'Deploy'
        runs-on: ubuntu-latest
        permissions:
            contents: read
            id-token: write

        steps:
            - name: Set ACCOUNT_ID based on branch
              id: set-account-id
              run: |
                  if [ "$GITHUB_REF_NAME" == "prod" ]; then
                    echo "ACCOUNT_ID=************" >> $GITHUB_ENV
                  elif [ "$GITHUB_REF_NAME" == "stage" ]; then
                    echo "ACCOUNT_ID=************" >> $GITHUB_ENV
                  elif [ "$GITHUB_REF_NAME" == "dev" ]; then
                    echo "ACCOUNT_ID=************" >> $GITHUB_ENV
                  else
                    echo "::error::Unknown branch"
                    exit 1
                  fi

            - name: configure aws credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  role-to-assume: arn:aws:iam::${{ env.ACCOUNT_ID }}:role/iam-role-canais-digitais
                  role-session-name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> # This is more for descriptive purposes for CloudTrail etc.
                  aws-region: sa-east-1

            - name: Get the current trust policy
              id: get-trust-policy
              run: |
                  aws iam get-role --role-name iam-role-canais-digitais \
                    --query "Role.AssumeRolePolicyDocument" \
                    --output json > current-trust-policy.json
              if: ${{ success() }}

            - name: Update trust policy file
              run: |
                  cat <<EOT > new-trust-policy.json
                  {
                    "Version": "2012-10-17",
                    "Statement": [
                      {
                        "Effect": "Allow",
                        "Principal": {
                          "Federated": "arn:aws:iam::${{ env.ACCOUNT_ID }}:oidc-provider/token.actions.githubusercontent.com"
                        },
                        "Action": "sts:AssumeRoleWithWebIdentity",
                        "Condition": {
                          "StringEquals": {
                            "token.actions.githubusercontent.com:sub": [
                              "repo:Informa-Unity/infra-live-latam-port-dev-front:ref:${GITHUB_REF}"
                            ]
                          }
                        }
                      },
                      {
                        "Effect": "Allow",
                        "Principal": {
                          "Service": [
                            "ecs-tasks.amazonaws.com",
                            "ecs.amazonaws.com"
                          ]
                        },
                        "Action": "sts:AssumeRole"
                      }
                    ]
                  }
                  EOT
              if: ${{ success() }}

            - name: Update IAM Trust Policy
              run: |
                  aws iam update-assume-role-policy --role-name iam-role-canais-digitais \
                    --policy-document file://new-trust-policy.json
              if: ${{ success() }}

            - name: Checkout code
              uses: actions/checkout@v2

            - name: Set up Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: '18.16.0'

            - name: Install Node Dependencies and Build Assets
              run: |
                  npm install
                  npm run assets
                  rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/
                  rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/typescript/

            - name: Install Wordpress Dependencies
              run: |
                  chmod +x deploy/bin/download-wp.sh
                  ./deploy/bin/download-wp.sh
                  chmod +x deploy/bin/download-plugins.sh
                  ./deploy/bin/download-plugins.sh

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v2

            - name: Log in to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v2

            - name: Build Docker Image
              env:
                  IMAGE_URI: ${{ steps.login-ecr.outputs.registry }}/canais-digitais:latest
              run: |
                  docker build -t $IMAGE_URI -f deploy/Dockerfile .
                  docker push ${{ env.IMAGE_URI }}

            - name: Deploy to ECS
              run: |
                  SITES=$(aws ecs list-services --cluster ecs-cluster-canais-digitais --output text)
                  echo "$SITES" | while read -r value; do
                    SITE_KEY=$(echo "$value" | sed -E 's/.*-([a-z0-9]+)$/\1/')
                    echo $SITE_KEY
                    aws ecs update-service \
                    --output table \
                    --cluster ecs-cluster-canais-digitais \
                    --service "ecs-service-${SITE_KEY}" \
                    --task-definition "ecs-task-${SITE_KEY}" --force-new-deployment
                  done

            - name: Notify
              run: |
                  curl -A "github" "https://buildbox.one/tools/webhooks/deploy.php?repository=${GITHUB_REPOSITORY}&domain=buildbox.one/tools/dashboard/cd.php%3Fenv%3D${GITHUB_REF_NAME}&env=${GITHUB_REF_NAME}&bx_token=ekH4e78dg11nKoa"
