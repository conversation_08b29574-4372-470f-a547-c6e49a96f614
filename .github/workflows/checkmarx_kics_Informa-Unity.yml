#Checkmarx KICS – Based on Open Policy Agent. Supports static code analysis of AWS CF, Terraform, HELM, and Kubernetes. 
#The documents are at https://docs.kics.io/latest/documentation/
#Supported platforms is listed at https://docs.kics.io/latest/platforms 
#Supported integrations (include IDE plugin) is listed at https://docs.kics.io/latest/integrations/  
#Certifacations: https://docs.kics.io/latest/certifications/
#Github: https://github.com/Checkmarx/kics.
#Github: https://github.com/Checkmarx/kics-github-action
#The workflow upload sarif testing result file to Github repositroy to support Github integration. 
#
#The workflow create artifact which contain the testing result report in html format. The retention-days of artifact: 1
#
#Contact Huijin Liu (<EMAIL>) for issues and feedback
on: 
  push:
    branches: [ "stage", "main", "prod" ]
  pull_request:
    branches: [ "stage", "main", "prod" ]
  workflow_dispatch:
   

jobs:
  use_checkmarx_kics_action:
    uses: Informa-Unity/security-test-tools/.github/workflows/checkmarx_kics_config_Informa-Unity.yml@main
