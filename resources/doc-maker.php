<?php

define('HEADERS', [
   'Entity' => [
      'Name'        => 'Name',
      'Type'        => 'Type',
      'Taxs'        => 'Taxonomies',
      'URL'         => 'Base URL',
      'Description' => 'Description',
      'Fields'      => 'Custom Fields',
   ],
   'Generic' => [
      'Name'   => 'Name',
      'Type'   => 'Type',
      'Description' => 'Description'
   ],
   'Global' => [
      'Name'   => 'Name',
      'Type'   => 'Type',
      'Fields' => 'Custom Fields',
      'Taxs'   => 'Taxonomies',
      'Description' => 'Description'
   ],
   'Service' => [
      'Name'   => 'Name',
      'Type'   => 'Type',
      'Fields' => 'Custom Fields',
      'Taxs'   => 'Taxonomies',
      'Description' => 'Description'
   ],
   'Util' => [
      'Name'   => 'Name',
      'Type'   => 'Type',
      'Fields' => 'Custom Fields',
      'Taxs'   => 'Taxonomies',
      'Description' => 'Description'
   ],
   'Module' => [
      'Name'   => 'Name',
      'Type'   => 'Type',
      'Fields' => 'Custom Fields',
      'Taxs'   => 'Taxonomies',
      'Description' => 'Description'
   ],
]);

function get_file_headers($file, $all_headers)
{
   if (!is_file($file)) {
      return [];
   }

   $file_data = file_get_contents($file, false, null, 0, 8 * 1024);

   if (false === $file_data) {
      return [];
   }

   $file_data = str_replace("\r", "\n", $file_data);

   foreach ($all_headers as $field => $regex) {
      if (preg_match('/^(?:[ \t]*<\?php)?[ \t\/*#@]*' . preg_quote($regex, '/') . ':(.*)$/mi', $file_data, $match) && $match[1]) {

         $all_headers[$field] = trim(preg_replace('/\s*(?:\*\/|\?>).*/', '', $match[1]));

         if ('Fields' === $field) {
            $fields = array_map(function ($field) {
               return "- $field";
            }, explode(', ', $all_headers[$field]));
            $all_headers[$field] = implode('<br>', $fields);
         }
      } else {
         $all_headers[$field] = '';
      }
   }

   if (empty(array_filter($all_headers))) {
      return [];
   }

   return $all_headers;
}

function check_folders()
{
   $doc_path   = dirname(__DIR__, 1) . '/docs';
   $theme_path = dirname(__DIR__, 1) . '/bxwp/wp-content/themes/bx-wp-theme/';
   $text       = '# Classes' . PHP_EOL;
   $folders    = [
      'Entity'  => 'includes/entities/',
      'Generic' => 'includes/generic/',
      'Global'  => 'includes/global/',
      'Service' => 'includes/services/',
      'Util'    => 'includes/utils/',
      'Module'  => 'modules/',
   ];

   if (!is_dir($doc_path)) {
      mkdir($doc_path);
   }

   foreach ($folders as $type => $folder) {
      if (!is_dir($theme_path . $folder)) {
         continue;
      }

      $items = scandir($theme_path . $folder);

      foreach ($items as $item) {
         if (in_array($item, ['..', '.'])) {
            continue;
         }

         if (is_dir($theme_path . $folder . $item) && is_file($theme_path . $folder . $item . "/$item.php")) {
            $file = $theme_path . $folder . $item . "/$item.php";
         } else {
            $file = $theme_path . $folder . $item;
         }

         $headers = get_file_headers($file, HEADERS[$type]);

         if (empty($headers)) {
            continue;
         }

         $doc[$type][str_replace($theme_path, '', $file)] = $headers;
      }

      if (empty($doc[$type])) {
         continue;
      }

      $text .= PHP_EOL . "## $type" . PHP_EOL;
      $dashes = array_map(function () {
         return '-';
      }, HEADERS[$type]);

      $text .= '| ' . implode(' | ', HEADERS[$type]) . " |" . PHP_EOL;
      $text .= '| ' . implode(' | ', $dashes) . " |" . PHP_EOL;

      foreach ($doc[$type] as $file => $headers) {
         $text .= '| ' . implode(' | ', $headers) . " |" . PHP_EOL;
      }
   }

   $doc_file = fopen("$doc_path/Classes.md", 'w');
   fwrite($doc_file, $text);
   fclose($doc_file);
   echo 'Done!' . PHP_EOL;
   echo "See $doc_path/Classes.md.";
}

check_folders();
