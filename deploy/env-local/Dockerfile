FROM henriquedamota/php-for-wordpress:8.2.10

# Clear workdir folder
RUN rm -rf /var/www/html/*

COPY ["bxwp", "/var/www/html"]

# Install WordPress
RUN wget https://wordpress.org/wordpress-6.6.1.tar.gz -O /tmp/wordpress.tar.gz \
    && tar -xzf /tmp/wordpress.tar.gz -C /var/www/html --strip-components=1 \
    && rm /tmp/wordpress.tar.gz

# Copy configuration and custom files
COPY ["deploy/commons/nginx.conf", "/etc/nginx/nginx.conf"]
COPY ["deploy/commons/publichost.conf", "/etc/nginx/sites-enable/1-publichost.conf"]

# wp-config move
COPY ["deploy/wp-config.php", "/var/www/html/wp-config.php"]

# Make correct permissions for Wordpress
RUN mkdir -p /var/www/html/wp-content/uploads /var/log/php /var/log/nginx \
   && chown -R www-data:www-data /var/log /var/www/html \
   && chmod 755 -R /var/log \
   && chmod 755 $(find /var/www/html -type d) \
   && chmod 644 $(find /var/www/html -type f)

# Start container using Supervisor to start Nginx and PHP FPM
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
