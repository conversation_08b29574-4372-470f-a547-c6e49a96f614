#!/bin/bash

export $(grep -v '^#' deploy/.env.versions | xargs);
PLUGINS_WP=($(echo $WP_PLUGINS | tr "|" "\n"))

for PLUGIN in "${PLUGINS_WP[@]}"; do
curl -s -O https://downloads.wordpress.org/plugin/$PLUGIN.zip
unzip $PLUGIN.zip -d bxwp/wp-content/plugins
done

PLUGINS_CDN=($(echo $CDN_PLUGINS | tr "|" "\n"))

for PLUGIN in "${PLUGINS_CDN[@]}"; do
curl -s -o $PLUGIN.zip https://cdn.buildbox.one/plugins/$PLUGIN.zip?key=3R61RY7ByjRUD3MD
unzip $PLUGIN.zip -d bxwp/wp-content/plugins
done
