worker_processes 4;
worker_rlimit_nofile 65535;
pid /var/run/nginx.pid;
user www-data;
load_module /etc/nginx/modules/ngx_http_headers_more_filter_module.so;

events {
    worker_connections  4096;
    multi_accept on;
}

http {
    server{
       location / {
          default_type text/html;
          return 200 "<!DOCTYPE html><h1>health check ok!</h1>\n";
       }
    }

    include       mime.types;

    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent"';

    keepalive_timeout 75;
    autoindex off;
    server_tokens off;
    port_in_redirect off;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    etag off;

    client_max_body_size 100m;
    client_body_buffer_size 16k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 8k;

    client_body_timeout 1m;
    client_header_timeout 1m;

    fastcgi_buffer_size 16k;
    fastcgi_buffers 8 8k;
    fastcgi_connect_timeout 1m;
    fastcgi_send_timeout 1m;
    fastcgi_read_timeout 1m;
    types_hash_max_size 1024;

    limit_req_zone $binary_remote_addr zone=goeasy:35m rate=1r/s;

    access_log /var/log/nginx/nginx.log;

    gzip  on;
    gzip_disable "msie6";
    gzip_comp_level 6;
    gzip_min_length 100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/rss+xml
        font/truetype
        font/opentype
        image/svg+xml;

    upstream php {
        server unix:/run/php-fpm.sock;
    }

    include /etc/nginx/sites-enable/*.conf;

    more_clear_headers 'X-Powered-By';
    more_clear_headers 'X-Redirect-By';
    more_clear_headers 'Server';
    more_clear_headers 'Link';
}
