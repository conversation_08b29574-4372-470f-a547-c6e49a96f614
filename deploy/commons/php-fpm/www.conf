[www]
user = www-data
group = www-data
listen.owner = www-data
listen.group = www-data
listen.mode = 0666
listen = /run/php-fpm.sock

pm = dynamic
pm.max_children = 10
pm.start_servers = 5
pm.min_spare_servers = 2
pm.max_spare_servers = 10

php_admin_value[error_log] = /proc/self/fd/2
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 986M
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M

request_terminate_timeout = 1m
catch_workers_output = yes
clear_env = no
access.log = /var/log/php/$pool.access.log
security.limit_extensions = .php
