FROM henriquedamota/php-for-wordpress:8.2.10

# Clear workdir folder
RUN rm -rf /var/www/html/*

# Copy Wordpress source to container
COPY ["bxwp", "/var/www/html"]
COPY ["deploy/commons/nginx.conf", "/etc/nginx/nginx.conf"]
COPY ["deploy/commons/publichost.conf", "/etc/nginx/sites-enable/1-publichost.conf"]

# wp-config move
COPY ["deploy/wp-config.php", "/var/www/html/wp-config.php"]

# Make correct permissions for Wordpress
RUN mkdir -p /var/www/html/wp-content/uploads /var/log/php /var/log/nginx \
   && chown -R www-data:www-data /var/log /var/www/html \
   && chmod 755 -R /var/log \
   && chmod 755 $(find /var/www/html -type d) \
   && chmod 644 $(find /var/www/html -type f)

# override upload and execution time variables
COPY ["deploy/commons/php-fpm/www.conf", "/opt/php/etc/php-fpm.d/"]   

# Start container using Supervisor to start NgInx and PHP Fpm (see /etc/supervisord.conf)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
