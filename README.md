# Canais Digitais (Informa Markets) / (multisite)

Plataforma de notícias para diversos segmentos.

## Ambientes

| Ambiente        | Branch Bitbucket | Branch GitHub  | `wp_get_environment_type()` | URL                                                                  |
| --------------- | ---------------- | -------------- | --------------------------- | -------------------------------------------------------------------- |
| Produção        |                  | `prod` (PR)    | `production`                | [Lista](https://buildbox.one/tools/dashboard/cd.php?env=main)        |
| Homologação     | `stage`          | `stage` (Pipe) | `staging`                   | [Lista](https://buildbox.one/tools/dashboard/cd.php?env=stage)       |
| Desenvolvimento | `dev`            | -              | `development`               | [canais-digitais.buildbox.one](https://canais-digitais.buildbox.one) |
| Local           | -                | -              | `local`                     | [canais-digitais.local](https://canais-digitais.local)               |

## Links

-  [Classes deste projeto](docs/Classes)
-  [Versões dos Recursos](deploy/.env.versions)
-  [Responsáveis atuais deste projeto](https://app.clickup.com/30906204/v/b/6-901301462428-2) ↗

---

## Estrutura de pastas

```bash
├──assets          # Todos os recursos do tema.
│   ├──images         # Outras imagens e vetores.
│   │   └──icons         # Imagens e vetores para ícones.
│   ├──scripts     # Arquivos JavaScript.
│   │   ├──all           # Arquivos .js do tema.
│   │   ├──admin         # Arquivos .js do painel do admin.
│   │   └──common        # Arquivos .js em comum.
│   ├──tailwind       # .css de conteúdo para Tailwind e configurações.
│   │   ├──all           # Arquivos .css do tema.
│   │   ├──admin         # Arquivos .css do painel do admin.
│   │   └──common        # CSS em comum em vários .css.
│   ├──js             # .min.js gerados pelo JavaScript, e pastas do seu conteúdo.
│   ├──css            # Mantém .min.css gerados pelo Tailwind.
│   └──fonts          # .woff e .woff2 de fontes usadas no tema.
├──includes        # Regras de negócio, configurações, etc.
│   ├──global         # Hooks de configurações e registros gerais do tema.
│   ├──generic        # Classes e funções não especificas do tema.
│   ├──entities       # Classes com CRUD e Registers internos.
│   ├──services       # Classes com Requests e APIs externos.
│   ├──hooks          # Hooks que adicionam/modificam HTML e/ou configurações no tema.
│   └──utils          # Classes e funções que não fazem parte de nenhuma entidade.
├──pages           # Templates chamados pela hierarquia padrão, e suas de partes.
└──components      # Templates partes usados em várias páginas ou pelo includes.
```

---

## Plugins

Plugins necessários e respectivas configurações.

| Plugin                          | Slug | Obrigatório | Ambiente | Notas                                        |
| ------------------------------- | ---- | ----------- | -------- | -------------------------------------------- |
| BX Essentials                   |      | Sim         | Todos    | Funções e hooks padrões.                     |
| ACF Pro                         |      | Sim         | Todos    | Campos personalizados.                       |
| Yoast SEO                       |      | Não         | Prod     | Otimização para motores de busca.            |
| Autopost for X/Twitter Settings |      | Não         | Todos    | Plugin para agendamento de posts no X.       |
| Site Kit por Google             |      | Não         | Todos    | Kit de ferramentas de rastreamento do Google |
| Redirection                     |      | Não         | Todos    | Plugin para criar redirecionamentos          |
| Wordfence                       |      | Não         | Todos    | Firewall e scans de malware                  |
| WP to LinkedIn Auto Publish     |      | Não         | Todos    | Plugin para agendamento de posts no LinkedIn |

## Ambiente Local de Desenvolvimento

### Clonar repositório

1. Todos os projetos WordPress devem ficar na mesma pasta localmente.
2. Nesta pasta, clonar este repositório.

### Configuração Local (by Flywheel)

1. Após criar o site local, entrar na pasta `app`, renomeie a pasta `public`. E.g.: `public_local`.
2. Criar links simbólicos do repositório na pasta `app`:

   No Windows (CMD como administrador):

   ```prompt
   mklink /d "(caminho para Local)\app\public\wp-content\themes\(nome do tema)" "(caminho para o repositório)\bxwp\wp-content\themes\(nome do tema)"
   ```

   No Mac ou Linux:

   ```bash
   ln -s "(caminho para o repositório)/bxwp/wp-content/themes/(nome do tema)" "(caminho para Local)/app/public/wp-content/themes/(nome do tema)"
   ```

3. Repita para cada plugin personalizado se existir, nas respectivas pastas.
4. Copiar `wp-config.php` da pasta renomeada (`public_local`) para `public`.
5. **Confiar certificado (Trust) antes de acessar por https**.

### Editar wp-config.php

1. Incluir no `wp-config.php` a seguinte linha após _$table_prefix_:
2. Criar link simbólico para este arquivo.

```php
include 'bx-env-local.php';
```

## Comandos

### Assets e automações

Para gerar os assets do projeto é necessário rodar o NPM (node).

Abra o terminal na pasta do projeto para executar os comandos seguintes.

```bash
# package.json
npm start # instala todas as dependências do projeto (npm e composer)
npm install # instale ou atualiza as dependências
npm run assets # gera CSS e JS
npm run css-all-watch # observa atualizações para arquivo all do CSS
npm run js-watch # observa atualizações no JS
npm run adr "Título" # Novo doc ADR
npm run settings # atualiza configurações

# composer.json
composer install # instalar/atualizar PHPCSFixer e dependências
sed -i "s/$indent, \['  '/$indent, \['   '/g" vendor/friendsofphp/php-cs-fixer/src/WhitespacesFixerConfig.php # modifica core
composer fix # roda fixer
composer fix # gera docs de classes
```

## Para outros comandos, veja `package.json` e `composer.json`.
